/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;

/**
 * Represents an active deployment session
 */
public class DeploymentSession {
    public enum Status {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED,
        CANCELLED
    }
    
    private final String mId;
    private final DeploymentConfiguration mConfiguration;
    private final long mStartTime;
    private Status mStatus;
    private String mCurrentPhase;
    private int mProgress;
    private String mErrorMessage;
    private DeploymentResult mResult;
    private long mEndTime;
    
    public DeploymentSession(String id, DeploymentConfiguration configuration, long startTime) {
        mId = id;
        mConfiguration = configuration;
        mStartTime = startTime;
        mStatus = Status.PENDING;
        mProgress = 0;
    }
    
    public String getId() {
        return mId;
    }
    
    public DeploymentConfiguration getConfiguration() {
        return mConfiguration;
    }
    
    public long getStartTime() {
        return mStartTime;
    }
    
    public Status getStatus() {
        return mStatus;
    }
    
    public void setStatus(Status status) {
        mStatus = status;
    }
    
    public String getCurrentPhase() {
        return mCurrentPhase;
    }
    
    public void setCurrentPhase(String currentPhase) {
        mCurrentPhase = currentPhase;
    }
    
    public int getProgress() {
        return mProgress;
    }
    
    public void setProgress(int progress) {
        mProgress = progress;
    }
    
    public String getErrorMessage() {
        return mErrorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        mErrorMessage = errorMessage;
    }
    
    public DeploymentResult getResult() {
        return mResult;
    }
    
    public long getEndTime() {
        return mEndTime;
    }
    
    public void complete(DeploymentResult result) {
        mResult = result;
        mStatus = result.isSuccess() ? Status.COMPLETED : Status.FAILED;
        mEndTime = System.currentTimeMillis();
        mProgress = 100;
    }
    
    public void fail(String errorMessage) {
        mErrorMessage = errorMessage;
        mStatus = Status.FAILED;
        mEndTime = System.currentTimeMillis();
    }
    
    public void cancel() {
        mStatus = Status.CANCELLED;
        mEndTime = System.currentTimeMillis();
    }
    
    public void start() {
        mStatus = Status.RUNNING;
    }
    
    public boolean isActive() {
        return mStatus == Status.PENDING || mStatus == Status.RUNNING;
    }
    
    public boolean isCompleted() {
        return mStatus == Status.COMPLETED || mStatus == Status.FAILED || mStatus == Status.CANCELLED;
    }
    
    public long getDuration() {
        if (mEndTime > 0) {
            return mEndTime - mStartTime;
        }
        return System.currentTimeMillis() - mStartTime;
    }
    
    public Bundle toBundle() {
        Bundle bundle = new Bundle();
        bundle.putString("id", mId);
        bundle.putString("status", mStatus.name());
        bundle.putString("current_phase", mCurrentPhase);
        bundle.putInt("progress", mProgress);
        bundle.putLong("start_time", mStartTime);
        bundle.putLong("end_time", mEndTime);
        bundle.putLong("duration", getDuration());
        if (mErrorMessage != null) {
            bundle.putString("error_message", mErrorMessage);
        }
        return bundle;
    }
}
