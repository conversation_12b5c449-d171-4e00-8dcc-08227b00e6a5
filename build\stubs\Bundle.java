package android.os;
import java.util.*;
public final class Bundle implements Parcelable {
    private final Map<String, Object> mMap = new HashMap<>();
    public Bundle() {}
    public void putString(String key, String value) { mMap.put(key, value); }
    public void putInt(String key, int value) { mMap.put(key, value); }
    public void putLong(String key, long value) { mMap.put(key, value); }
    public void putFloat(String key, float value) { mMap.put(key, value); }
    public void putDouble(String key, double value) { mMap.put(key, value); }
    public void putBoolean(String key, boolean value) { mMap.put(key, value); }
    public void putBundle(String key, Bundle value) { mMap.put(key, value); }
    public String getString(String key) { return (String) mMap.get(key); }
    public String getString(String key, String defaultValue) { String val = getString(key); return val != null ? val : defaultValue; }
    public int getInt(String key) { Integer val = (Integer) mMap.get(key); return val != null ? val : 0; }
    public int getInt(String key, int defaultValue) { Integer val = (Integer) mMap.get(key); return val != null ? val : defaultValue; }
    public long getLong(String key) { Long val = (Long) mMap.get(key); return val != null ? val : 0L; }
    public long getLong(String key, long defaultValue) { Long val = (Long) mMap.get(key); return val != null ? val : defaultValue; }
    public float getFloat(String key) { Float val = (Float) mMap.get(key); return val != null ? val : 0.0f; }
    public float getFloat(String key, float defaultValue) { Float val = (Float) mMap.get(key); return val != null ? val : defaultValue; }
    public double getDouble(String key) { Double val = (Double) mMap.get(key); return val != null ? val : 0.0; }
    public double getDouble(String key, double defaultValue) { Double val = (Double) mMap.get(key); return val != null ? val : defaultValue; }
    public boolean getBoolean(String key) { Boolean val = (Boolean) mMap.get(key); return val != null ? val : false; }
    public boolean getBoolean(String key, boolean defaultValue) { Boolean val = (Boolean) mMap.get(key); return val != null ? val : defaultValue; }
    public Bundle getBundle(String key) { return (Bundle) mMap.get(key); }
    public boolean containsKey(String key) { return mMap.containsKey(key); }
    public Object get(String key) { return mMap.get(key); }
    public void remove(String key) { mMap.remove(key); }
    public void clear() { mMap.clear(); }
    public Set<String> keySet() { return mMap.keySet(); }
    public int size() { return mMap.size(); }
    public boolean isEmpty() { return mMap.isEmpty(); }
    public void putCharSequence(String key, CharSequence value) { mMap.put(key, value); }
    public CharSequence getCharSequence(String key) { Object value = mMap.get(key); return value instanceof CharSequence ? (CharSequence) value : null; }
    public CharSequence getCharSequence(String key, CharSequence defaultValue) { CharSequence value = getCharSequence(key); return value != null ? value : defaultValue; }
    public int describeContents() { return 0; }
    public void writeToParcel(Object dest, int flags) {}
    public static final Creator<Bundle> CREATOR = new Creator<Bundle>() {
        public Bundle createFromParcel(Object in) { return new Bundle(); }
        public Bundle[] newArray(int size) { return new Bundle[size]; }
    };
}
