/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Result of quality assurance operations
 */
public class QualityAssuranceResult implements Parcelable {
    private boolean mSuccess;
    private String mMessage;
    private String mErrorMessage;
    private float mQualityScore;
    private Bundle mMetrics;
    private long mTimestamp;
    
    public QualityAssuranceResult() {
        mMetrics = new Bundle();
        mTimestamp = System.currentTimeMillis();
        mQualityScore = 0.0f;
    }
    
    public QualityAssuranceResult(Parcel in) {
        mSuccess = in.readBoolean();
        mMessage = in.readString();
        mErrorMessage = in.readString();
        mQualityScore = in.readFloat();
        mMetrics = in.readBundle(getClass().getClassLoader());
        mTimestamp = in.readLong();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeBoolean(mSuccess);
        dest.writeString(mMessage);
        dest.writeString(mErrorMessage);
        dest.writeFloat(mQualityScore);
        dest.writeBundle(mMetrics);
        dest.writeLong(mTimestamp);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<QualityAssuranceResult> CREATOR = new Creator<QualityAssuranceResult>() {
        @Override
        public QualityAssuranceResult createFromParcel(Parcel in) {
            return new QualityAssuranceResult(in);
        }
        
        @Override
        public QualityAssuranceResult[] newArray(int size) {
            return new QualityAssuranceResult[size];
        }
    };
    
    // Getters and setters
    public boolean isSuccess() { return mSuccess; }
    public void setSuccess(boolean success) { mSuccess = success; }
    
    public String getMessage() { return mMessage; }
    public void setMessage(String message) { mMessage = message; }
    
    public String getErrorMessage() { return mErrorMessage; }
    public void setErrorMessage(String errorMessage) { mErrorMessage = errorMessage; }
    
    public float getQualityScore() { return mQualityScore; }
    public void setQualityScore(float qualityScore) { mQualityScore = qualityScore; }
    
    public Bundle getMetrics() { return mMetrics; }
    public void setMetrics(Bundle metrics) { mMetrics = metrics; }
    
    public long getTimestamp() { return mTimestamp; }
    public void setTimestamp(long timestamp) { mTimestamp = timestamp; }
}
