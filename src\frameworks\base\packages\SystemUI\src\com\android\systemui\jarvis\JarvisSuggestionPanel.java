/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.systemui.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Proactive AI suggestion panel for Jarvis.
 * 
 * Displays contextual suggestions based on:
 * - Current user context
 * - Time of day and location
 * - User patterns and preferences
 * - App usage and system state
 * - Recent activities and interactions
 */
public class JarvisSuggestionPanel extends LinearLayout {
    private static final String TAG = "JarvisSuggestionPanel";
    private static final boolean DEBUG = true;
    
    private static final int ANIMATION_DURATION = 250;
    private static final int MAX_SUGGESTIONS = 8;
    private static final int SUGGESTION_REFRESH_INTERVAL = 30000; // 30 seconds
    
    // UI Components
    private TextView mPanelTitle;
    private RecyclerView mSuggestionsRecyclerView;
    private ImageButton mRefreshButton;
    private ImageButton mDismissButton;
    private View mEmptyStateView;
    private TextView mEmptyStateText;
    
    // Suggestion Management
    private SuggestionAdapter mSuggestionAdapter;
    private List<AiSuggestion> mCurrentSuggestions;
    private Handler mMainHandler;
    private Runnable mRefreshRunnable;
    
    // State
    private boolean mIsVisible = false;
    private boolean mIsAnimating = false;
    private boolean mAutoRefreshEnabled = true;
    
    // Callbacks
    private SuggestionListener mSuggestionListener;
    
    public interface SuggestionListener {
        void onSuggestionSelected(AiSuggestion suggestion);
        void onSuggestionDismissed(AiSuggestion suggestion);
        void onPanelDismissed();
        void onRefreshRequested();
        void onSuggestionFeedback(AiSuggestion suggestion, boolean helpful);
    }
    
    public JarvisSuggestionPanel(Context context) {
        this(context, null);
    }
    
    public JarvisSuggestionPanel(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }
    
    public JarvisSuggestionPanel(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        mMainHandler = new Handler(Looper.getMainLooper());
        mCurrentSuggestions = new ArrayList<>();
        
        setOrientation(VERTICAL);
        setVisibility(GONE);
        
        // Inflate the suggestion panel layout
        LayoutInflater.from(context).inflate(R.layout.jarvis_suggestion_panel, this, true);
        
        initializeViews();
        setupSuggestionsRecyclerView();
        setupRefreshMechanism();
        setupClickHandlers();
        
        if (DEBUG) Log.d(TAG, "JarvisSuggestionPanel initialized");
    }
    
    private void initializeViews() {
        mPanelTitle = findViewById(R.id.panel_title);
        mSuggestionsRecyclerView = findViewById(R.id.suggestions_recycler_view);
        mRefreshButton = findViewById(R.id.refresh_button);
        mDismissButton = findViewById(R.id.dismiss_button);
        mEmptyStateView = findViewById(R.id.empty_state_view);
        mEmptyStateText = findViewById(R.id.empty_state_text);
        
        updatePanelTitle();
    }
    
    private void setupSuggestionsRecyclerView() {
        mSuggestionAdapter = new SuggestionAdapter(mCurrentSuggestions);
        mSuggestionAdapter.setSuggestionClickListener(new SuggestionAdapter.SuggestionClickListener() {
            @Override
            public void onSuggestionClick(AiSuggestion suggestion) {
                handleSuggestionSelection(suggestion);
            }
            
            @Override
            public void onSuggestionDismiss(AiSuggestion suggestion) {
                handleSuggestionDismissal(suggestion);
            }
            
            @Override
            public void onSuggestionFeedback(AiSuggestion suggestion, boolean helpful) {
                handleSuggestionFeedback(suggestion, helpful);
            }
        });
        
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        mSuggestionsRecyclerView.setLayoutManager(layoutManager);
        mSuggestionsRecyclerView.setAdapter(mSuggestionAdapter);
        
        // Disable nested scrolling for better performance
        mSuggestionsRecyclerView.setNestedScrollingEnabled(false);
    }
    
    private void setupRefreshMechanism() {
        mRefreshRunnable = () -> {
            if (mIsVisible && mAutoRefreshEnabled) {
                refreshSuggestions();
                scheduleNextRefresh();
            }
        };
    }
    
    private void setupClickHandlers() {
        mRefreshButton.setOnClickListener(v -> {
            refreshSuggestions();
            if (mSuggestionListener != null) {
                mSuggestionListener.onRefreshRequested();
            }
        });
        
        mDismissButton.setOnClickListener(v -> {
            hidePanel();
            if (mSuggestionListener != null) {
                mSuggestionListener.onPanelDismissed();
            }
        });
    }
    
    public void setSuggestionListener(SuggestionListener listener) {
        mSuggestionListener = listener;
    }
    
    public void showPanel() {
        if (mIsVisible || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Showing suggestion panel");
        
        mIsAnimating = true;
        setVisibility(VISIBLE);
        
        // Start show animation
        animateShow(() -> {
            mIsVisible = true;
            mIsAnimating = false;
            
            // Start auto-refresh if enabled
            if (mAutoRefreshEnabled) {
                scheduleNextRefresh();
            }
            
            if (DEBUG) Log.d(TAG, "Suggestion panel shown");
        });
    }
    
    public void hidePanel() {
        if (!mIsVisible || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Hiding suggestion panel");
        
        mIsAnimating = true;
        
        // Cancel auto-refresh
        cancelRefresh();
        
        // Start hide animation
        animateHide(() -> {
            setVisibility(GONE);
            mIsVisible = false;
            mIsAnimating = false;
            
            if (DEBUG) Log.d(TAG, "Suggestion panel hidden");
        });
    }
    
    public boolean isVisible() {
        return mIsVisible;
    }
    
    public void updateSuggestions(List<AiSuggestion> suggestions) {
        if (suggestions == null) {
            suggestions = new ArrayList<>();
        }
        
        // Limit to maximum suggestions
        if (suggestions.size() > MAX_SUGGESTIONS) {
            suggestions = suggestions.subList(0, MAX_SUGGESTIONS);
        }
        
        mCurrentSuggestions.clear();
        mCurrentSuggestions.addAll(suggestions);
        
        mMainHandler.post(() -> {
            mSuggestionAdapter.notifyDataSetChanged();
            updateEmptyState();
            updatePanelTitle();
        });
        
        if (DEBUG) Log.d(TAG, "Updated suggestions: " + suggestions.size() + " items");
    }
    
    public void addSuggestion(AiSuggestion suggestion) {
        if (suggestion == null || mCurrentSuggestions.size() >= MAX_SUGGESTIONS) {
            return;
        }
        
        mCurrentSuggestions.add(suggestion);
        
        mMainHandler.post(() -> {
            mSuggestionAdapter.notifyItemInserted(mCurrentSuggestions.size() - 1);
            updateEmptyState();
            updatePanelTitle();
        });
        
        if (DEBUG) Log.d(TAG, "Added suggestion: " + suggestion.getTitle());
    }
    
    public void removeSuggestion(String suggestionId) {
        for (int i = 0; i < mCurrentSuggestions.size(); i++) {
            if (mCurrentSuggestions.get(i).getId().equals(suggestionId)) {
                mCurrentSuggestions.remove(i);
                
                mMainHandler.post(() -> {
                    mSuggestionAdapter.notifyItemRemoved(i);
                    updateEmptyState();
                    updatePanelTitle();
                });
                
                if (DEBUG) Log.d(TAG, "Removed suggestion: " + suggestionId);
                break;
            }
        }
    }
    
    public void refreshSuggestions() {
        if (DEBUG) Log.d(TAG, "Refreshing suggestions");
        
        // Show refresh animation
        animateRefresh();
        
        if (mSuggestionListener != null) {
            mSuggestionListener.onRefreshRequested();
        }
    }
    
    public void setAutoRefreshEnabled(boolean enabled) {
        mAutoRefreshEnabled = enabled;
        
        if (enabled && mIsVisible) {
            scheduleNextRefresh();
        } else {
            cancelRefresh();
        }
    }
    
    private void handleSuggestionSelection(AiSuggestion suggestion) {
        if (mSuggestionListener != null) {
            mSuggestionListener.onSuggestionSelected(suggestion);
        }
        
        // Mark suggestion as used
        suggestion.markAsUsed();
        
        if (DEBUG) Log.d(TAG, "Suggestion selected: " + suggestion.getTitle());
    }
    
    private void handleSuggestionDismissal(AiSuggestion suggestion) {
        removeSuggestion(suggestion.getId());
        
        if (mSuggestionListener != null) {
            mSuggestionListener.onSuggestionDismissed(suggestion);
        }
        
        if (DEBUG) Log.d(TAG, "Suggestion dismissed: " + suggestion.getTitle());
    }
    
    private void handleSuggestionFeedback(AiSuggestion suggestion, boolean helpful) {
        suggestion.recordFeedback(helpful);
        
        if (mSuggestionListener != null) {
            mSuggestionListener.onSuggestionFeedback(suggestion, helpful);
        }
        
        if (DEBUG) Log.d(TAG, "Suggestion feedback: " + suggestion.getTitle() + " helpful=" + helpful);
    }
    
    private void updateEmptyState() {
        boolean isEmpty = mCurrentSuggestions.isEmpty();
        mEmptyStateView.setVisibility(isEmpty ? VISIBLE : GONE);
        mSuggestionsRecyclerView.setVisibility(isEmpty ? GONE : VISIBLE);
        
        if (isEmpty) {
            mEmptyStateText.setText(getContext().getString(R.string.jarvis_no_suggestions));
        }
    }
    
    private void updatePanelTitle() {
        int count = mCurrentSuggestions.size();
        String title;
        
        if (count == 0) {
            title = getContext().getString(R.string.jarvis_suggestions);
        } else {
            title = getContext().getResources().getQuantityString(
                R.plurals.jarvis_suggestions_count, count, count);
        }
        
        mPanelTitle.setText(title);
    }
    
    private void scheduleNextRefresh() {
        cancelRefresh();
        mMainHandler.postDelayed(mRefreshRunnable, SUGGESTION_REFRESH_INTERVAL);
    }
    
    private void cancelRefresh() {
        mMainHandler.removeCallbacks(mRefreshRunnable);
    }
    
    private void animateShow(Runnable onComplete) {
        // Set initial state
        setAlpha(0f);
        setTranslationY(-50f);
        setScaleY(0.8f);
        
        // Animate to visible state
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(this, "translationY", -50f, 0f);
        ObjectAnimator scaleAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0.8f, 1f);
        
        alphaAnimator.setDuration(ANIMATION_DURATION);
        translateAnimator.setDuration(ANIMATION_DURATION);
        scaleAnimator.setDuration(ANIMATION_DURATION);
        
        alphaAnimator.setInterpolator(new DecelerateInterpolator());
        translateAnimator.setInterpolator(new DecelerateInterpolator());
        scaleAnimator.setInterpolator(new DecelerateInterpolator());
        
        alphaAnimator.start();
        translateAnimator.start();
        scaleAnimator.start();
        
        alphaAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (onComplete != null) {
                    onComplete.run();
                }
            }
        });
    }
    
    private void animateHide(Runnable onComplete) {
        // Animate to hidden state
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(this, "translationY", 0f, -50f);
        ObjectAnimator scaleAnimator = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.8f);
        
        alphaAnimator.setDuration(ANIMATION_DURATION);
        translateAnimator.setDuration(ANIMATION_DURATION);
        scaleAnimator.setDuration(ANIMATION_DURATION);
        
        alphaAnimator.setInterpolator(new DecelerateInterpolator());
        translateAnimator.setInterpolator(new DecelerateInterpolator());
        scaleAnimator.setInterpolator(new DecelerateInterpolator());
        
        alphaAnimator.start();
        translateAnimator.start();
        scaleAnimator.start();
        
        alphaAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (onComplete != null) {
                    onComplete.run();
                }
            }
        });
    }
    
    private void animateRefresh() {
        // Rotate refresh button
        ObjectAnimator rotateAnimator = ObjectAnimator.ofFloat(mRefreshButton, "rotation", 0f, 360f);
        rotateAnimator.setDuration(500);
        rotateAnimator.start();
    }
    
    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Handle orientation changes
        if (mSuggestionAdapter != null) {
            mSuggestionAdapter.notifyDataSetChanged();
        }
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cancelRefresh();
    }
    
    // Public API for external control
    public List<AiSuggestion> getCurrentSuggestions() {
        return new ArrayList<>(mCurrentSuggestions);
    }
    
    public int getSuggestionCount() {
        return mCurrentSuggestions.size();
    }
    
    public void clearSuggestions() {
        mCurrentSuggestions.clear();
        mSuggestionAdapter.notifyDataSetChanged();
        updateEmptyState();
        updatePanelTitle();
    }
}
