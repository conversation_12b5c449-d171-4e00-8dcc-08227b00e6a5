package android.os;
import java.util.*;
public class Parcel {
    private List<Object> mData = new ArrayList<>();
    private int mPosition = 0;
    public void writeString(String val) { mData.add(val); }
    public void writeInt(int val) { mData.add(val); }
    public void writeLong(long val) { mData.add(val); }
    public void writeFloat(float val) { mData.add(val); }
    public void writeDouble(double val) { mData.add(val); }
    public void writeByte(byte val) { mData.add(val); }
    public void writeValue(Object val) { mData.add(val); }
    public String readString() { return mPosition < mData.size() ? (String) mData.get(mPosition++) : null; }
    public int readInt() { return mPosition < mData.size() ? (Integer) mData.get(mPosition++) : 0; }
    public long readLong() { return mPosition < mData.size() ? (Long) mData.get(mPosition++) : 0L; }
    public float readFloat() { return mPosition < mData.size() ? (Float) mData.get(mPosition++) : 0.0f; }
    public double readDouble() { return mPosition < mData.size() ? (Double) mData.get(mPosition++) : 0.0; }
    public byte readByte() { return mPosition < mData.size() ? (Byte) mData.get(mPosition++) : 0; }
    public Object readValue(ClassLoader loader) { return mPosition < mData.size() ? mData.get(mPosition++) : null; }
    public static Parcel obtain() { return new Parcel(); }
    public void recycle() { mData.clear(); mPosition = 0; }
}
