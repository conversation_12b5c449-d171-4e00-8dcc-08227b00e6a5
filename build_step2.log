src\frameworks\base\core\java\android\ai\ActionResult.java:19: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\ActionResult.java:20: error: package android.os does not exist
import android.os.Parcel;
                 ^
src\frameworks\base\core\java\android\ai\ActionResult.java:21: error: package android.os does not exist
import android.os.Parcelable;
                 ^
src\frameworks\base\core\java\android\ai\ActionResult.java:26: error: cannot find symbol
public class ActionResult implements Parcelable {
                                     ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ActionResult.java:31: error: cannot find symbol
    public Bundle resultData;
           ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:50: error: cannot find symbol
    public ActionResult(boolean success, String message, Bundle resultData) {
                                                         ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:55: error: cannot find symbol
    protected ActionResult(Parcel in) {
                           ^
  symbol:   class Parcel
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:64: error: cannot find symbol
    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
                        ^
  symbol:   class Creator
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:82: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:104: error: cannot find symbol
    public Bundle getResultData() {
           ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:19: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:20: error: package android.os does not exist
import android.os.Parcel;
                 ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:21: error: package android.os does not exist
import android.os.Parcelable;
                 ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:28: error: cannot find symbol
public class ContextSnapshot implements Parcelable {
                                        ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:32: error: cannot find symbol
    public Bundle appStates;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:33: error: cannot find symbol
    public Bundle sensorData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:34: error: cannot find symbol
    public Bundle notificationData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:35: error: cannot find symbol
    public Bundle userInteractionData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:36: error: cannot find symbol
    public Bundle environmentalData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:37: error: cannot find symbol
    public Bundle calendarData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:38: error: cannot find symbol
    public Bundle communicationData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:53: error: cannot find symbol
    public ContextSnapshot(Parcel in) {
                           ^
  symbol:   class Parcel
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:68: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:87: error: cannot find symbol
    public static final Creator<ContextSnapshot> CREATOR = new Creator<ContextSnapshot>() {
                        ^
  symbol:   class Creator
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:135: error: cannot find symbol
    public Bundle toBundle() {
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:154: error: cannot find symbol
    public static ContextSnapshot fromBundle(Bundle bundle) {
                                             ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ActionRequest.java:19: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:20: error: package android.os does not exist
import android.os.Parcel;
                 ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:21: error: package android.os does not exist
import android.os.Parcelable;
                 ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:26: error: cannot find symbol
public class ActionRequest implements Parcelable {
                                      ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ActionRequest.java:29: error: cannot find symbol
    public Bundle parameters;
           ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:57: error: cannot find symbol
    protected ActionRequest(Parcel in) {
                            ^
  symbol:   class Parcel
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:70: error: cannot find symbol
    public static final Creator<ActionRequest> CREATOR = new Creator<ActionRequest>() {
                        ^
  symbol:   class Creator
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:88: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:19: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:20: error: package android.os does not exist
import android.os.Parcel;
                 ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:21: error: package android.os does not exist
import android.os.Parcelable;
                 ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:26: error: cannot find symbol
public class ExecutionStatus implements Parcelable {
                                        ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:38: error: cannot find symbol
    public Bundle statusData;
           ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:57: error: cannot find symbol
    protected ExecutionStatus(Parcel in) {
                              ^
  symbol:   class Parcel
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:67: error: cannot find symbol
    public static final Creator<ExecutionStatus> CREATOR = new Creator<ExecutionStatus>() {
                        ^
  symbol:   class Creator
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:85: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ActionResult.java:39: error: cannot find symbol
        this.resultData = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:52: error: cannot find symbol
        this.resultData = resultData != null ? resultData : new Bundle();
                                                                ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:59: error: cannot find symbol
        resultData = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:64: error: cannot find symbol
    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
                                                            ^
  symbol:   class Creator
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:66: error: cannot find symbol
        public ActionResult createFromParcel(Parcel in) {
                                             ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ActionResult.java:65: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionResult.java:70: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionResult.java:76: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionResult.java:81: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionResult.java:105: error: cannot find symbol
        return resultData != null ? resultData : new Bundle();
                                                     ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:110: error: cannot find symbol
            resultData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:120: error: cannot find symbol
        } else if (value instanceof Bundle) {
                                    ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:121: error: cannot find symbol
            resultData.putBundle(key, (Bundle) value);
                                       ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:43: error: cannot find symbol
        appStates = new Bundle();
                        ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:44: error: cannot find symbol
        sensorData = new Bundle();
                         ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:45: error: cannot find symbol
        notificationData = new Bundle();
                               ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:46: error: cannot find symbol
        userInteractionData = new Bundle();
                                  ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:47: error: cannot find symbol
        environmentalData = new Bundle();
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:48: error: cannot find symbol
        calendarData = new Bundle();
                           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:49: error: cannot find symbol
        communicationData = new Bundle();
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:67: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:82: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:87: error: cannot find symbol
    public static final Creator<ContextSnapshot> CREATOR = new Creator<ContextSnapshot>() {
                                                               ^
  symbol:   class Creator
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:89: error: cannot find symbol
        public ContextSnapshot createFromParcel(Parcel in) {
                                                ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:88: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:93: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:121: error: cannot find symbol
        copy.appStates = new Bundle(this.appStates);
                             ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:122: error: cannot find symbol
        copy.sensorData = new Bundle(this.sensorData);
                              ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:123: error: cannot find symbol
        copy.notificationData = new Bundle(this.notificationData);
                                    ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:124: error: cannot find symbol
        copy.userInteractionData = new Bundle(this.userInteractionData);
                                       ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:125: error: cannot find symbol
        copy.environmentalData = new Bundle(this.environmentalData);
                                     ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:126: error: cannot find symbol
        copy.calendarData = new Bundle(this.calendarData);
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:127: error: cannot find symbol
        copy.communicationData = new Bundle(this.communicationData);
                                     ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:136: error: cannot find symbol
        Bundle bundle = new Bundle();
        ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:136: error: cannot find symbol
        Bundle bundle = new Bundle();
                            ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ActionRequest.java:41: error: cannot find symbol
        this.parameters = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:60: error: cannot find symbol
        parameters = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:70: error: cannot find symbol
    public static final Creator<ActionRequest> CREATOR = new Creator<ActionRequest>() {
                                                             ^
  symbol:   class Creator
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:72: error: cannot find symbol
        public ActionRequest createFromParcel(Parcel in) {
                                              ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ActionRequest.java:71: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:76: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:82: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:87: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:103: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:110: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:117: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:47: error: cannot find symbol
        this.statusData = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:62: error: cannot find symbol
        statusData = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:67: error: cannot find symbol
    public static final Creator<ExecutionStatus> CREATOR = new Creator<ExecutionStatus>() {
                                                               ^
  symbol:   class Creator
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:69: error: cannot find symbol
        public ExecutionStatus createFromParcel(Parcel in) {
                                                ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:68: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:73: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:79: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:84: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:143: error: cannot find symbol
            statusData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:150: error: cannot find symbol
            statusData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ExecutionStatus
98 errors
src\frameworks\base\core\java\android\ai\IActionProvider.java:19: error: package android.os does not exist
import android.os.IInterface;
                 ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:20: error: package android.os does not exist
import android.os.RemoteException;
                 ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:25: error: cannot find symbol
public interface IActionProvider extends IInterface {
                                         ^
  symbol: class IInterface
src\frameworks\base\core\java\android\ai\IActionProvider.java:30: error: cannot find symbol
    ActionResult executeAction(ActionRequest request) throws RemoteException;
                               ^
  symbol:   class ActionRequest
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:30: error: cannot find symbol
    ActionResult executeAction(ActionRequest request) throws RemoteException;
    ^
  symbol:   class ActionResult
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:30: error: cannot find symbol
    ActionResult executeAction(ActionRequest request) throws RemoteException;
                                                             ^
  symbol:   class RemoteException
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:35: error: cannot find symbol
    boolean supportsAction(String actionType) throws RemoteException;
                                                     ^
  symbol:   class RemoteException
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:40: error: cannot find symbol
    String[] getSupportedActions() throws RemoteException;
                                          ^
  symbol:   class RemoteException
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:45: error: cannot find symbol
    String getProviderInfo() throws RemoteException;
                                    ^
  symbol:   class RemoteException
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:50: error: package android.os does not exist
    public static abstract class Stub extends android.os.Binder implements IActionProvider {
                                                        ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:57: error: package android.os does not exist
        public static IActionProvider asInterface(android.os.IBinder obj) {
                                                            ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:69: error: package android.os does not exist
        public android.os.IBinder asBinder() {
                         ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:74: error: package android.os does not exist
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
                                                      ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:74: error: package android.os does not exist
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
                                                                              ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:74: error: cannot find symbol
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
                                                                                                               ^
  symbol:   class RemoteException
  location: class Stub
src\frameworks\base\core\java\android\ai\IActionProvider.java:110: error: package android.os does not exist
            private android.os.IBinder mRemote;
                              ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:112: error: package android.os does not exist
            Proxy(android.os.IBinder remote) {
                            ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:117: error: package android.os does not exist
            public android.os.IBinder asBinder() {
                             ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:126: error: cannot find symbol
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                                              ^
  symbol:   class ActionRequest
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:126: error: cannot find symbol
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                   ^
  symbol:   class ActionResult
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:126: error: cannot find symbol
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                                                                            ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:142: error: cannot find symbol
            public boolean supportsAction(String actionType) throws RemoteException {
                                                                    ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:158: error: cannot find symbol
            public String[] getSupportedActions() throws RemoteException {
                                                         ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:173: error: cannot find symbol
            public String getProviderInfo() throws RemoteException {
                                                   ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:19: error: package android.os does not exist
import android.os.Binder;
                 ^
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:20: error: package android.os does not exist
import android.os.IBinder;
                 ^
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:21: error: package android.os does not exist
import android.os.IInterface;
                 ^
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:22: error: package android.os does not exist
import android.os.RemoteException;
                 ^
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:29: error: cannot find symbol
public interface IAiContextEngine extends IInterface {
                                          ^
  symbol: class IInterface
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:31: error: cannot find symbol
    ContextSnapshot getCurrentContext() throws RemoteException;
    ^
  symbol:   class ContextSnapshot
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:31: error: cannot find symbol
    ContextSnapshot getCurrentContext() throws RemoteException;
                                               ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IContextListener.java:19: error: package android.os does not exist
import android.os.Binder;
                 ^
src\frameworks\base\core\java\android\ai\IContextListener.java:20: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\IContextListener.java:21: error: package android.os does not exist
import android.os.IBinder;
                 ^
src\frameworks\base\core\java\android\ai\IContextListener.java:22: error: package android.os does not exist
import android.os.IInterface;
                 ^
src\frameworks\base\core\java\android\ai\IContextListener.java:23: error: package android.os does not exist
import android.os.RemoteException;
                 ^
src\frameworks\base\core\java\android\ai\IContextListener.java:28: error: cannot find symbol
public interface IContextListener extends IInterface {
                                          ^
  symbol: class IInterface
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:33: error: cannot find symbol
    void registerContextListener(IContextListener listener) throws RemoteException;
                                                                   ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:35: error: cannot find symbol
    void unregisterContextListener(IContextListener listener) throws RemoteException;
                                                                     ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:37: error: cannot find symbol
    boolean requestContextPermission(String contextType, String callingPackage) throws RemoteException;
                                                                                       ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:39: error: cannot find symbol
    List<String> getAvailableContextTypes() throws RemoteException;
                                                   ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:41: error: cannot find symbol
    List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException;
         ^
  symbol:   class ContextSnapshot
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:41: error: cannot find symbol
    List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException;
                                                                                    ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:43: error: cannot find symbol
    void setContextCollectionEnabled(String contextType, boolean enabled) throws RemoteException;
                                                                                 ^
  symbol:   class RemoteException
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IContextListener.java:30: error: cannot find symbol
    void onContextChanged(ContextSnapshot context) throws RemoteException;
                          ^
  symbol:   class ContextSnapshot
  location: interface IContextListener
src\frameworks\base\core\java\android\ai\IContextListener.java:30: error: cannot find symbol
    void onContextChanged(ContextSnapshot context) throws RemoteException;
                                                          ^
  symbol:   class RemoteException
  location: interface IContextListener
src\frameworks\base\core\java\android\ai\IContextListener.java:32: error: cannot find symbol
    void onContextTypeUpdated(String contextType, Bundle contextData) throws RemoteException;
                                                  ^
  symbol:   class Bundle
  location: interface IContextListener
src\frameworks\base\core\java\android\ai\IContextListener.java:32: error: cannot find symbol
    void onContextTypeUpdated(String contextType, Bundle contextData) throws RemoteException;
                                                                             ^
  symbol:   class RemoteException
  location: interface IContextListener
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:48: error: cannot find symbol
    public static abstract class Stub extends Binder implements IAiContextEngine {
                                              ^
  symbol:   class Binder
  location: interface IAiContextEngine
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:55: error: cannot find symbol
        public static IAiContextEngine asInterface(IBinder obj) {
                                                   ^
  symbol:   class IBinder
  location: class Stub
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:67: error: cannot find symbol
        public IBinder asBinder() {
               ^
  symbol:   class IBinder
  location: class Stub
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:72: error: cannot find symbol
            private IBinder mRemote;
                    ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:74: error: cannot find symbol
            Proxy(IBinder remote) {
                  ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:79: error: cannot find symbol
            public IBinder asBinder() {
                   ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:84: error: cannot find symbol
            public ContextSnapshot getCurrentContext() throws RemoteException {
                   ^
  symbol:   class ContextSnapshot
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:84: error: cannot find symbol
            public ContextSnapshot getCurrentContext() throws RemoteException {
                                                              ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:90: error: cannot find symbol
            public void registerContextListener(IContextListener listener) throws RemoteException {
                                                                                  ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:95: error: cannot find symbol
            public void unregisterContextListener(IContextListener listener) throws RemoteException {
                                                                                    ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:100: error: cannot find symbol
            public boolean requestContextPermission(String contextType, String callingPackage) throws RemoteException {
                                                                                                      ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:106: error: cannot find symbol
            public List<String> getAvailableContextTypes() throws RemoteException {
                                                                  ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:112: error: cannot find symbol
            public List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException {
                        ^
  symbol:   class ContextSnapshot
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:112: error: cannot find symbol
            public List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException {
                                                                                                   ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiContextEngine.java:118: error: cannot find symbol
            public void setContextCollectionEnabled(String contextType, boolean enabled) throws RemoteException {
                                                                                                ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:19: error: package android.os does not exist
import android.os.Binder;
                 ^
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:20: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:21: error: package android.os does not exist
import android.os.IBinder;
                 ^
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:22: error: package android.os does not exist
import android.os.IInterface;
                 ^
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:23: error: package android.os does not exist
import android.os.RemoteException;
                 ^
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:30: error: cannot find symbol
public interface IAiPersonalization extends IInterface {
                                            ^
  symbol: class IInterface
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:32: error: cannot find symbol
    UserProfile getUserProfile() throws RemoteException;
    ^
  symbol:   class UserProfile
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:32: error: cannot find symbol
    UserProfile getUserProfile() throws RemoteException;
                                        ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:34: error: cannot find symbol
    void updatePreference(String key, Bundle value) throws RemoteException;
                                      ^
  symbol:   class Bundle
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:34: error: cannot find symbol
    void updatePreference(String key, Bundle value) throws RemoteException;
                                                           ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:36: error: cannot find symbol
    LearningModel getPersonalizedModel(String modelType) throws RemoteException;
    ^
  symbol:   class LearningModel
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:36: error: cannot find symbol
    LearningModel getPersonalizedModel(String modelType) throws RemoteException;
                                                                ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:38: error: cannot find symbol
    void recordUserInteraction(UserInteraction interaction) throws RemoteException;
                               ^
  symbol:   class UserInteraction
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:38: error: cannot find symbol
    void recordUserInteraction(UserInteraction interaction) throws RemoteException;
                                                                   ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:40: error: cannot find symbol
    List<Recommendation> getRecommendations(String category, ContextSnapshot context) throws RemoteException;
                                                             ^
  symbol:   class ContextSnapshot
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:40: error: cannot find symbol
    List<Recommendation> getRecommendations(String category, ContextSnapshot context) throws RemoteException;
         ^
  symbol:   class Recommendation
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:40: error: cannot find symbol
    List<Recommendation> getRecommendations(String category, ContextSnapshot context) throws RemoteException;
                                                                                             ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:42: error: cannot find symbol
    void provideFeedback(String modelType, FeedbackData feedback) throws RemoteException;
                                           ^
  symbol:   class FeedbackData
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:42: error: cannot find symbol
    void provideFeedback(String modelType, FeedbackData feedback) throws RemoteException;
                                                                         ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:44: error: cannot find symbol
    void resetPersonalizationData(String dataType) throws RemoteException;
                                                          ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:46: error: cannot find symbol
    Bundle exportPersonalizationData() throws RemoteException;
    ^
  symbol:   class Bundle
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:46: error: cannot find symbol
    Bundle exportPersonalizationData() throws RemoteException;
                                              ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:48: error: cannot find symbol
    boolean importPersonalizationData(Bundle data) throws RemoteException;
                                      ^
  symbol:   class Bundle
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:48: error: cannot find symbol
    boolean importPersonalizationData(Bundle data) throws RemoteException;
                                                          ^
  symbol:   class RemoteException
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:53: error: cannot find symbol
    public static abstract class Stub extends Binder implements IAiPersonalization {
                                              ^
  symbol:   class Binder
  location: interface IAiPersonalization
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:60: error: cannot find symbol
        public static IAiPersonalization asInterface(IBinder obj) {
                                                     ^
  symbol:   class IBinder
  location: class Stub
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:72: error: cannot find symbol
        public IBinder asBinder() {
               ^
  symbol:   class IBinder
  location: class Stub
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:77: error: cannot find symbol
            private IBinder mRemote;
                    ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:79: error: cannot find symbol
            Proxy(IBinder remote) {
                  ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:84: error: cannot find symbol
            public IBinder asBinder() {
                   ^
  symbol:   class IBinder
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:89: error: cannot find symbol
            public UserProfile getUserProfile() throws RemoteException {
                   ^
  symbol:   class UserProfile
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:89: error: cannot find symbol
            public UserProfile getUserProfile() throws RemoteException {
                                                       ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:94: error: cannot find symbol
            public void updatePreference(String key, Bundle value) throws RemoteException {
                                                     ^
  symbol:   class Bundle
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:94: error: cannot find symbol
            public void updatePreference(String key, Bundle value) throws RemoteException {
                                                                          ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:98: error: cannot find symbol
            public LearningModel getPersonalizedModel(String modelType) throws RemoteException {
                   ^
  symbol:   class LearningModel
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:98: error: cannot find symbol
            public LearningModel getPersonalizedModel(String modelType) throws RemoteException {
                                                                               ^
  symbol:   class RemoteException
  location: class Proxy
src\frameworks\base\core\java\android\ai\IAiPersonalization.java:103: error: cannot find symbol
            public void recordUserInteraction(UserInteraction interaction) throws RemoteException {
                                              ^
  symbol:   class UserInteraction
  location: class Proxy
100 errors
error: file not found: src\frameworks\base\core\java\android\ai\PersonalizationData.java
Usage: javac <options> <source files>
use --help for a list of possible options
