/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.IInterface;
import android.os.RemoteException;

/**
 * Interface for action providers that can execute specific actions
 */
public interface IActionProvider extends IInterface {
    
    /**
     * Execute an action with the given request
     */
    ActionResult executeAction(ActionRequest request) throws RemoteException;
    
    /**
     * Check if this provider supports the given action type
     */
    boolean supportsAction(String actionType) throws RemoteException;
    
    /**
     * Get the list of supported action types
     */
    String[] getSupportedActions() throws RemoteException;
    
    /**
     * Get provider information
     */
    String getProviderInfo() throws RemoteException;
    
    /**
     * Abstract base class for IActionProvider implementations
     */
    public static abstract class Stub extends android.os.Binder implements IActionProvider {
        private static final String DESCRIPTOR = "android.ai.IActionProvider";
        
        public Stub() {
            this.attachInterface(this, DESCRIPTOR);
        }
        
        public static IActionProvider asInterface(android.os.IBinder obj) {
            if (obj == null) {
                return null;
            }
            android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
            if (iin != null && iin instanceof IActionProvider) {
                return (IActionProvider) iin;
            }
            return new Proxy(obj);
        }
        
        @Override
        public android.os.IBinder asBinder() {
            return this;
        }
        
        @Override
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
            switch (code) {
                case INTERFACE_TRANSACTION:
                    reply.writeString(DESCRIPTOR);
                    return true;
                case 1: // executeAction
                    data.enforceInterface(DESCRIPTOR);
                    ActionRequest request = ActionRequest.CREATOR.createFromParcel(data);
                    ActionResult result = this.executeAction(request);
                    reply.writeNoException();
                    result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
                    return true;
                case 2: // supportsAction
                    data.enforceInterface(DESCRIPTOR);
                    String actionType = data.readString();
                    boolean supports = this.supportsAction(actionType);
                    reply.writeNoException();
                    reply.writeInt(supports ? 1 : 0);
                    return true;
                case 3: // getSupportedActions
                    data.enforceInterface(DESCRIPTOR);
                    String[] actions = this.getSupportedActions();
                    reply.writeNoException();
                    reply.writeStringArray(actions);
                    return true;
                case 4: // getProviderInfo
                    data.enforceInterface(DESCRIPTOR);
                    String info = this.getProviderInfo();
                    reply.writeNoException();
                    reply.writeString(info);
                    return true;
            }
            return super.onTransact(code, data, reply, flags);
        }
        
        private static class Proxy implements IActionProvider {
            private android.os.IBinder mRemote;
            
            Proxy(android.os.IBinder remote) {
                mRemote = remote;
            }
            
            @Override
            public android.os.IBinder asBinder() {
                return mRemote;
            }
            
            public String getInterfaceDescriptor() {
                return DESCRIPTOR;
            }
            
            @Override
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                android.os.Parcel data = android.os.Parcel.obtain();
                android.os.Parcel reply = android.os.Parcel.obtain();
                try {
                    data.writeInterfaceToken(DESCRIPTOR);
                    request.writeToParcel(data, 0);
                    mRemote.transact(1, data, reply, 0);
                    reply.readException();
                    return ActionResult.CREATOR.createFromParcel(reply);
                } finally {
                    reply.recycle();
                    data.recycle();
                }
            }
            
            @Override
            public boolean supportsAction(String actionType) throws RemoteException {
                android.os.Parcel data = android.os.Parcel.obtain();
                android.os.Parcel reply = android.os.Parcel.obtain();
                try {
                    data.writeInterfaceToken(DESCRIPTOR);
                    data.writeString(actionType);
                    mRemote.transact(2, data, reply, 0);
                    reply.readException();
                    return reply.readInt() != 0;
                } finally {
                    reply.recycle();
                    data.recycle();
                }
            }
            
            @Override
            public String[] getSupportedActions() throws RemoteException {
                android.os.Parcel data = android.os.Parcel.obtain();
                android.os.Parcel reply = android.os.Parcel.obtain();
                try {
                    data.writeInterfaceToken(DESCRIPTOR);
                    mRemote.transact(3, data, reply, 0);
                    reply.readException();
                    return reply.createStringArray();
                } finally {
                    reply.recycle();
                    data.recycle();
                }
            }
            
            @Override
            public String getProviderInfo() throws RemoteException {
                android.os.Parcel data = android.os.Parcel.obtain();
                android.os.Parcel reply = android.os.Parcel.obtain();
                try {
                    data.writeInterfaceToken(DESCRIPTOR);
                    mRemote.transact(4, data, reply, 0);
                    reply.readException();
                    return reply.readString();
                } finally {
                    reply.recycle();
                    data.recycle();
                }
            }
        }
    }
}
