/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Binder;
import android.os.Bundle;
import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;

/**
 * Interface for listening to context changes
 */
public interface IContextListener extends IInterface {
    
    void onContextChanged(ContextSnapshot context) throws RemoteException;
    
    void onContextTypeUpdated(String contextType, Bundle contextData) throws RemoteException;

    /**
     * Local-side IPC implementation stub class.
     */
    public static abstract class Stub extends Binder implements IContextListener {
        private static final String DESCRIPTOR = "android.ai.IContextListener";

        public Stub() {
            this.attachInterface(this, DESCRIPTOR);
        }

        public static IContextListener asInterface(IBinder obj) {
            if (obj == null) {
                return null;
            }
            IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
            if (iin != null && iin instanceof IContextListener) {
                return (IContextListener) iin;
            }
            return new Proxy(obj);
        }

        @Override
        public IBinder asBinder() {
            return this;
        }

        private static class Proxy implements IContextListener {
            private IBinder mRemote;

            Proxy(IBinder remote) {
                mRemote = remote;
            }

            @Override
            public IBinder asBinder() {
                return mRemote;
            }

            @Override
            public void onContextChanged(ContextSnapshot context) throws RemoteException {
                // Implementation would be generated by AIDL
            }

            @Override
            public void onContextTypeUpdated(String contextType, Bundle contextData) throws RemoteException {
                // Implementation would be generated by AIDL
            }
        }
    }
}
