/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.os.Bundle;

import java.util.List;
import java.util.Map;

/**
 * Data types for advanced workflow planning in Jarvis OS.
 * 
 * Contains all the data structures needed for complex workflow planning,
 * execution, and optimization.
 */
public class WorkflowDataTypes {
    
    /**
     * Represents a complex workflow plan with multiple phases
     */
    public static class WorkflowPlan {
        public String workflowId;
        public String goal;
        public String workflowType; // sequential, parallel, conditional
        public List<WorkflowPhase> phases;
        public List<String> synchronizationPoints;
        public List<String> resourceRequirements;
        public long estimatedDuration;
        public double confidence;
        public String errorMessage;
        public long createdTime;
        public String plannerVersion;
    }
    
    /**
     * Represents a phase within a workflow
     */
    public static class WorkflowPhase {
        public String phaseId;
        public String description;
        public List<WorkflowStep> steps;
        public boolean parallelExecution;
        public List<String> prerequisites;
        public long estimatedDuration;
    }
    
    /**
     * Enhanced workflow step with additional metadata
     */
    public static class WorkflowStep {
        public String stepId;
        public String action;
        public String description;
        public String targetApp;
        public Bundle parameters;
        public List<String> dependencies;
        public List<String> resourceRequirements;
        public long timeout;
        public boolean canRunInParallel;
        public boolean required;
        public int priority;
        public String errorHandling; // retry, skip, abort
    }
    
    /**
     * Context information for workflow planning
     */
    public static class WorkflowContext {
        public List<String> activeApps;
        public List<String> availableResources;
        public Map<String, String> userPreferences;
        public Map<String, Object> deviceState;
        public List<String> recentActions;
        public long timestamp;
        
        public String toContextString() {
            StringBuilder sb = new StringBuilder();
            
            if (activeApps != null && !activeApps.isEmpty()) {
                sb.append("Active apps: ").append(String.join(", ", activeApps)).append("\n");
            }
            
            if (availableResources != null && !availableResources.isEmpty()) {
                sb.append("Available resources: ").append(String.join(", ", availableResources)).append("\n");
            }
            
            if (userPreferences != null && !userPreferences.isEmpty()) {
                sb.append("User preferences: ").append(userPreferences.size()).append(" items\n");
            }
            
            if (deviceState != null && !deviceState.isEmpty()) {
                sb.append("Device state: ").append(deviceState.size()).append(" properties\n");
            }
            
            return sb.toString();
        }
    }
    
    /**
     * Workflow complexity analysis
     */
    public static class WorkflowComplexity {
        public int goalComplexity;
        public int contextComplexity;
        public List<String> requiredApps;
        public long estimatedDuration;
        public String planningStrategy; // simple, intermediate, advanced
        public int parallelismLevel;
        public boolean requiresUserInteraction;
    }
    
    /**
     * Workflow validation result
     */
    public static class WorkflowValidation {
        public boolean isValid;
        public String errorMessage;
        public List<String> warnings;
        public double feasibilityScore;
        public List<String> missingResources;
        public List<String> conflictingSteps;
    }
    
    /**
     * Conditional workflow with multiple execution paths
     */
    public static class ConditionalWorkflow {
        public String workflowId;
        public String goal;
        public List<WorkflowCondition> conditions;
        public Map<String, WorkflowPath> executionPaths;
        public WorkflowPath defaultPath;
        public String errorMessage;
        public long createdTime;
    }
    
    /**
     * Condition for conditional workflow execution
     */
    public static class WorkflowCondition {
        public String conditionId;
        public String conditionType; // device_state, user_input, app_state, time_based
        public String conditionExpression;
        public Map<String, Object> conditionParameters;
        public int priority;
        public boolean required;
    }
    
    /**
     * Execution path for conditional workflow
     */
    public static class WorkflowPath {
        public String pathId;
        public String description;
        public List<WorkflowStep> steps;
        public long estimatedDuration;
        public double confidence;
        public List<String> resourceRequirements;
    }
    
    /**
     * Parallel workflow with concurrent execution branches
     */
    public static class ParallelWorkflow {
        public String workflowId;
        public List<WorkflowBranch> parallelBranches;
        public List<SynchronizationPoint> synchronizationPoints;
        public ResourceAllocation resourceAllocation;
        public String errorMessage;
        public long createdTime;
    }
    
    /**
     * Branch in parallel workflow
     */
    public static class WorkflowBranch {
        public String branchId;
        public String description;
        public List<WorkflowStep> steps;
        public int priority;
        public List<String> resourceRequirements;
        public long estimatedDuration;
        public boolean canRunConcurrently;
    }
    
    /**
     * Synchronization point for parallel execution
     */
    public static class SynchronizationPoint {
        public String syncId;
        public List<String> waitForBranches;
        public List<String> waitForSteps;
        public String syncType; // barrier, merge, conditional
        public long timeoutMs;
        public String onTimeoutAction; // continue, abort, retry
    }
    
    /**
     * Resource allocation for parallel execution
     */
    public static class ResourceAllocation {
        public Map<String, Integer> cpuAllocation;
        public Map<String, Integer> memoryAllocation;
        public Map<String, Boolean> networkAccess;
        public Map<String, Boolean> storageAccess;
        public Map<String, Integer> priorityLevels;
    }
    
    /**
     * Workflow execution metrics
     */
    public static class WorkflowMetrics {
        public String workflowId;
        public long startTime;
        public long endTime;
        public long actualDuration;
        public long estimatedDuration;
        public int totalSteps;
        public int completedSteps;
        public int failedSteps;
        public int skippedSteps;
        public double successRate;
        public Map<String, Long> stepDurations;
        public List<String> errors;
        public List<String> warnings;
    }
    
    /**
     * Workflow optimization result
     */
    public static class WorkflowOptimization {
        public WorkflowPlan originalPlan;
        public WorkflowPlan optimizedPlan;
        public List<String> optimizations;
        public long estimatedTimeSaving;
        public double confidenceImprovement;
        public List<String> resourceOptimizations;
    }
    
    /**
     * Workflow execution status
     */
    public static class WorkflowExecutionStatus {
        public String workflowId;
        public String status; // pending, running, completed, failed, cancelled
        public String currentPhase;
        public String currentStep;
        public int progress; // 0-100
        public long startTime;
        public long estimatedCompletionTime;
        public List<String> completedSteps;
        public List<String> failedSteps;
        public String lastError;
        public Map<String, Object> executionContext;
    }
    
    /**
     * Workflow template for reusable patterns
     */
    public static class WorkflowTemplate {
        public String templateId;
        public String name;
        public String description;
        public String category;
        public List<String> tags;
        public WorkflowPlan templatePlan;
        public List<TemplateParameter> parameters;
        public List<String> requiredPermissions;
        public long usageCount;
        public double averageRating;
    }
    
    /**
     * Template parameter for workflow customization
     */
    public static class TemplateParameter {
        public String parameterId;
        public String name;
        public String description;
        public String type; // string, number, boolean, choice
        public Object defaultValue;
        public List<Object> allowedValues;
        public boolean required;
        public String validationPattern;
    }
    
    /**
     * Workflow learning data for AI improvement
     */
    public static class WorkflowLearningData {
        public String workflowId;
        public String goal;
        public WorkflowPlan plannedWorkflow;
        public WorkflowMetrics executionMetrics;
        public List<String> userFeedback;
        public List<String> optimizationOpportunities;
        public Map<String, Double> featureImportance;
        public long timestamp;
    }
    
    /**
     * Cross-app coordination data
     */
    public static class CrossAppCoordination {
        public String coordinationId;
        public List<String> involvedApps;
        public Map<String, List<String>> appDependencies;
        public Map<String, Object> sharedData;
        public List<String> dataFlowSteps;
        public Map<String, String> appRoles; // primary, secondary, data_provider, etc.
        public List<String> synchronizationRequirements;
    }
    
    /**
     * Resource conflict detection and resolution
     */
    public static class ResourceConflict {
        public String conflictId;
        public String resourceType;
        public List<String> conflictingSteps;
        public String conflictSeverity; // low, medium, high, critical
        public List<String> resolutionOptions;
        public String recommendedResolution;
        public long detectionTime;
    }
    
    /**
     * Workflow performance profile
     */
    public static class WorkflowPerformanceProfile {
        public String profileId;
        public String workflowType;
        public Map<String, Double> averageStepDurations;
        public Map<String, Double> stepSuccessRates;
        public Map<String, Integer> resourceUsagePatterns;
        public List<String> commonFailurePoints;
        public List<String> optimizationRecommendations;
        public long lastUpdated;
    }
}
