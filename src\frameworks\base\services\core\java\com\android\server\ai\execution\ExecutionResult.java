/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution;



import java.util.ArrayList;
import java.util.List;

/**
 * Result of task execution
 */
public class ExecutionResult {
    public String taskId;
    public boolean success;
    public String errorMessage;
    public List<ActionResult> actionResults;
    public long executionTime;

    public ExecutionResult() {
        this.taskId = null;
        this.success = false;
        this.errorMessage = null;
        this.actionResults = new ArrayList<>();
        this.executionTime = 0;
    }

    public ExecutionResult(String taskId) {
        this();
        this.taskId = taskId;
    }



    public void addActionResult(ActionResult actionResult) {
        if (actionResults == null) {
            actionResults = new ArrayList<>();
        }
        actionResults.add(actionResult);
    }

    public boolean hasErrors() {
        return !success || errorMessage != null;
    }

    public int getActionCount() {
        return actionResults != null ? actionResults.size() : 0;
    }
}
