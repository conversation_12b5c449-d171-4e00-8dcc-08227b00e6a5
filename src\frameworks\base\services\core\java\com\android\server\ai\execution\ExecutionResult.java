/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Result of task execution
 */
public class ExecutionResult implements Parcelable {
    public String taskId;
    public boolean success;
    public String errorMessage;
    public Bundle results;
    public List<ActionResult> actionResults;
    public long executionTime;
    public Bundle metadata;

    public ExecutionResult() {
        this.taskId = null;
        this.success = false;
        this.errorMessage = null;
        this.results = new Bundle();
        this.actionResults = new ArrayList<>();
        this.executionTime = 0;
        this.metadata = new Bundle();
    }

    public ExecutionResult(String taskId) {
        this();
        this.taskId = taskId;
    }

    protected ExecutionResult(Parcel in) {
        taskId = in.readString();
        success = in.readByte() != 0;
        errorMessage = in.readString();
        results = in.readBundle(getClass().getClassLoader());
        actionResults = in.createTypedArrayList(ActionResult.CREATOR);
        executionTime = in.readLong();
        metadata = in.readBundle(getClass().getClassLoader());
    }

    public static final Creator<ExecutionResult> CREATOR = new Creator<ExecutionResult>() {
        @Override
        public ExecutionResult createFromParcel(Parcel in) {
            return new ExecutionResult(in);
        }

        @Override
        public ExecutionResult[] newArray(int size) {
            return new ExecutionResult[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeByte((byte) (success ? 1 : 0));
        dest.writeString(errorMessage);
        dest.writeBundle(results);
        dest.writeTypedList(actionResults);
        dest.writeLong(executionTime);
        dest.writeBundle(metadata);
    }

    public void addActionResult(ActionResult actionResult) {
        if (actionResults == null) {
            actionResults = new ArrayList<>();
        }
        actionResults.add(actionResult);
    }

    public boolean hasErrors() {
        return !success || errorMessage != null;
    }

    public int getActionCount() {
        return actionResults != null ? actionResults.size() : 0;
    }
}
