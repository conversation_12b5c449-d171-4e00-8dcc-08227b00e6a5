/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.service.notification;

import android.app.Notification;
import android.os.UserHandle;

/**
 * Mock implementation of StatusBarNotification for Jarvis OS compilation.
 * This is a stub implementation for development and testing.
 */
public class StatusBarNotification {
    private final String mPkg;
    private final String mOpPkg;
    private final int mId;
    private final String mTag;
    private final int mUid;
    private final int mInitialPid;
    private final Notification mNotification;
    private final UserHandle mUser;
    private final String mOverrideGroupKey;
    private final long mPostTime;
    private final String mKey;

    public StatusBarNotification(String pkg, String opPkg, int id, String tag, int uid,
            int initialPid, Notification notification, UserHandle user, String overrideGroupKey,
            long postTime) {
        if (pkg == null) throw new NullPointerException();
        if (notification == null) throw new NullPointerException();

        this.mPkg = pkg;
        this.mOpPkg = opPkg;
        this.mId = id;
        this.mTag = tag;
        this.mUid = uid;
        this.mInitialPid = initialPid;
        this.mNotification = notification;
        this.mUser = user != null ? user : new UserHandle(0);
        this.mOverrideGroupKey = overrideGroupKey;
        this.mPostTime = postTime;
        this.mKey = key();
    }

    public StatusBarNotification(String pkg, String opPkg, int id, String tag, int uid,
            int initialPid, Notification notification, UserHandle user, long postTime) {
        this(pkg, opPkg, id, tag, uid, initialPid, notification, user, null, postTime);
    }

    /**
     * The package of the app that posted the notification.
     */
    public String getPackageName() {
        return mPkg;
    }

    /**
     * The package used for AppOps tracking.
     */
    public String getOpPkg() {
        return mOpPkg;
    }

    /**
     * The id supplied to {@link android.app.NotificationManager#notify(int, Notification)}.
     */
    public int getId() {
        return mId;
    }

    /**
     * The tag supplied to {@link android.app.NotificationManager#notify(String, int, Notification)},
     * or null if no tag was specified.
     */
    public String getTag() {
        return mTag;
    }

    /**
     * The notifying app's ({@link #getPackageName()}) uid.
     */
    public int getUid() {
        return mUid;
    }

    /**
     * The package that the notification was posted for.
     */
    public String getOpPkg() {
        return mOpPkg;
    }

    /**
     * The pid of the process that posted the notification.
     */
    public int getInitialPid() {
        return mInitialPid;
    }

    /**
     * The {@link Notification} supplied to
     * {@link android.app.NotificationManager#notify(int, Notification)}.
     */
    public Notification getNotification() {
        return mNotification;
    }

    /**
     * The {@link UserHandle} for whom this notification is intended.
     */
    public UserHandle getUser() {
        return mUser;
    }

    /**
     * The user id for whom this notification is intended.
     */
    public int getUserId() {
        return mUser.getIdentifier();
    }

    /**
     * A unique instance key for this notification record.
     */
    public String getKey() {
        return mKey;
    }

    /**
     * A key that indicates the group with which this message ranks.
     */
    public String getGroupKey() {
        return mOverrideGroupKey != null ? mOverrideGroupKey : getGroup();
    }

    /**
     * The group key from the notification, if it has one.
     */
    public String getGroup() {
        return getNotification().group;
    }

    /**
     * The time (in {@link System#currentTimeMillis} time) the notification was posted,
     * which may be different than {@link Notification#when}.
     */
    public long getPostTime() {
        return mPostTime;
    }

    /**
     * Returns true if this notification is part of a group.
     */
    public boolean isGroup() {
        return getGroup() != null;
    }

    /**
     * Returns true if this notification is the summary for a group.
     */
    public boolean isGroupSummary() {
        return (getNotification().flags & Notification.FLAG_GROUP_SUMMARY) != 0;
    }

    /**
     * Returns true if this notification should be shown to the user.
     */
    public boolean isClearable() {
        return (getNotification().flags & Notification.FLAG_NO_CLEAR) == 0 &&
               (getNotification().flags & Notification.FLAG_ONGOING_EVENT) == 0;
    }

    /**
     * Returns true if this is an ongoing notification.
     */
    public boolean isOngoing() {
        return (getNotification().flags & Notification.FLAG_ONGOING_EVENT) != 0;
    }

    /**
     * Returns the notification's channel id.
     */
    public String getChannelId() {
        return getNotification().channelId;
    }

    /**
     * Returns the notification's override group key.
     */
    public String getOverrideGroupKey() {
        return mOverrideGroupKey;
    }

    /**
     * @hide
     */
    public StatusBarNotification cloneLight() {
        final Notification no = new Notification();
        mNotification.cloneInto(no, false);
        return new StatusBarNotification(this.mPkg, this.mOpPkg,
                this.mId, this.mTag, this.mUid, this.mInitialPid,
                no, this.mUser, this.mOverrideGroupKey, this.mPostTime);
    }

    @Override
    public StatusBarNotification clone() {
        return new StatusBarNotification(this.mPkg, this.mOpPkg,
                this.mId, this.mTag, this.mUid, this.mInitialPid,
                this.mNotification.clone(), this.mUser, this.mOverrideGroupKey, this.mPostTime);
    }

    @Override
    public String toString() {
        return String.format(
                "StatusBarNotification(pkg=%s user=%s id=%d tag=%s key=%s: %s)",
                this.mPkg, this.mUser, this.mId, this.mTag,
                this.mKey, this.mNotification);
    }

    /**
     * Convenience method to check the notification's flags for
     * {@link Notification#FLAG_ONGOING_EVENT}.
     */
    public boolean isOngoing() {
        return (mNotification.flags & Notification.FLAG_ONGOING_EVENT) != 0;
    }

    /**
     * Convenience method to check the notification's flags for
     * either {@link Notification#FLAG_ONGOING_EVENT} or
     * {@link Notification#FLAG_NO_CLEAR}.
     */
    public boolean isClearable() {
        return ((mNotification.flags & Notification.FLAG_ONGOING_EVENT) == 0)
                && ((mNotification.flags & Notification.FLAG_NO_CLEAR) == 0);
    }

    private String key() {
        return mUser.getIdentifier() + "|" + mPkg + "|" + mId + "|" + mTag + "|" + mUid;
    }

    /**
     * Mock UserHandle class
     */
    public static class UserHandle {
        private final int mHandle;

        public UserHandle(int h) {
            mHandle = h;
        }

        public int getIdentifier() {
            return mHandle;
        }

        @Override
        public String toString() {
            return "UserHandle{" + mHandle + "}";
        }

        @Override
        public boolean equals(Object obj) {
            try {
                if (obj != null) {
                    UserHandle other = (UserHandle)obj;
                    return mHandle == other.mHandle;
                }
            } catch (ClassCastException e) {
            }
            return false;
        }

        @Override
        public int hashCode() {
            return mHandle;
        }
    }
}
