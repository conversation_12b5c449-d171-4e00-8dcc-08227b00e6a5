/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents an AI-generated suggestion for the user.
 * 
 * Contains all information needed to display and execute a suggestion,
 * including metadata for ranking, feedback, and analytics.
 */
public class AiSuggestion implements Parcelable {
    
    public enum SuggestionType {
        ACTION,           // Execute a specific action
        APP_LAUNCH,       // Launch an application
        SETTING_CHANGE,   // Modify a system setting
        REMINDER,         // Set a reminder or notification
        INFORMATION,      // Provide information or answer
        ROUTINE,          // Suggest a routine or automation
        SHORTCUT,         // Create or use a shortcut
        CONTEXT_AWARE     // Context-specific suggestion
    }
    
    public enum Priority {
        LOW(1),
        NORMAL(2),
        HIGH(3),
        URGENT(4);
        
        private final int value;
        
        Priority(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
    }
    
    // Core suggestion data
    private String mId;
    private String mTitle;
    private String mDescription;
    private String mActionText;
    private SuggestionType mType;
    private Priority mPriority;
    
    // Visual representation
    private String mIconResourceName;
    private int mIconColor;
    private String mBackgroundColor;
    
    // Execution data
    private String mActionId;
    private Bundle mActionData;
    private List<String> mRequiredPermissions;
    
    // Metadata
    private long mCreatedTime;
    private long mExpiryTime;
    private float mConfidenceScore;
    private int mUsageCount;
    private boolean mIsUsed;
    private boolean mIsDismissed;
    
    // Context information
    private String mContextSource;
    private Bundle mContextData;
    private List<String> mContextTags;
    
    // Feedback and learning
    private int mPositiveFeedback;
    private int mNegativeFeedback;
    private float mRelevanceScore;
    private boolean mHasFeedback;
    
    public AiSuggestion(String id, String title, String description, SuggestionType type) {
        mId = id;
        mTitle = title;
        mDescription = description;
        mType = type;
        mPriority = Priority.NORMAL;
        mCreatedTime = System.currentTimeMillis();
        mExpiryTime = mCreatedTime + (24 * 60 * 60 * 1000); // 24 hours default
        mConfidenceScore = 0.5f;
        mUsageCount = 0;
        mIsUsed = false;
        mIsDismissed = false;
        mRequiredPermissions = new ArrayList<>();
        mContextTags = new ArrayList<>();
        mPositiveFeedback = 0;
        mNegativeFeedback = 0;
        mRelevanceScore = 0.5f;
        mHasFeedback = false;
        mIconColor = 0xFF2196F3; // Default blue
        mBackgroundColor = "#F5F5F5"; // Default light gray
    }
    
    // Parcelable constructor
    protected AiSuggestion(Parcel in) {
        mId = in.readString();
        mTitle = in.readString();
        mDescription = in.readString();
        mActionText = in.readString();
        mType = SuggestionType.valueOf(in.readString());
        mPriority = Priority.valueOf(in.readString());
        mIconResourceName = in.readString();
        mIconColor = in.readInt();
        mBackgroundColor = in.readString();
        mActionId = in.readString();
        mActionData = in.readBundle(getClass().getClassLoader());
        mRequiredPermissions = in.createStringArrayList();
        mCreatedTime = in.readLong();
        mExpiryTime = in.readLong();
        mConfidenceScore = in.readFloat();
        mUsageCount = in.readInt();
        mIsUsed = in.readByte() != 0;
        mIsDismissed = in.readByte() != 0;
        mContextSource = in.readString();
        mContextData = in.readBundle(getClass().getClassLoader());
        mContextTags = in.createStringArrayList();
        mPositiveFeedback = in.readInt();
        mNegativeFeedback = in.readInt();
        mRelevanceScore = in.readFloat();
        mHasFeedback = in.readByte() != 0;
    }
    
    public static final Creator<AiSuggestion> CREATOR = new Creator<AiSuggestion>() {
        @Override
        public AiSuggestion createFromParcel(Parcel in) {
            return new AiSuggestion(in);
        }
        
        @Override
        public AiSuggestion[] newArray(int size) {
            return new AiSuggestion[size];
        }
    };
    
    // Getters
    public String getId() { return mId; }
    public String getTitle() { return mTitle; }
    public String getDescription() { return mDescription; }
    public String getActionText() { return mActionText; }
    public SuggestionType getType() { return mType; }
    public Priority getPriority() { return mPriority; }
    public String getIconResourceName() { return mIconResourceName; }
    public int getIconColor() { return mIconColor; }
    public String getBackgroundColor() { return mBackgroundColor; }
    public String getActionId() { return mActionId; }
    public Bundle getActionData() { return mActionData; }
    public List<String> getRequiredPermissions() { return new ArrayList<>(mRequiredPermissions); }
    public long getCreatedTime() { return mCreatedTime; }
    public long getExpiryTime() { return mExpiryTime; }
    public float getConfidenceScore() { return mConfidenceScore; }
    public int getUsageCount() { return mUsageCount; }
    public boolean isUsed() { return mIsUsed; }
    public boolean isDismissed() { return mIsDismissed; }
    public String getContextSource() { return mContextSource; }
    public Bundle getContextData() { return mContextData; }
    public List<String> getContextTags() { return new ArrayList<>(mContextTags); }
    public int getPositiveFeedback() { return mPositiveFeedback; }
    public int getNegativeFeedback() { return mNegativeFeedback; }
    public float getRelevanceScore() { return mRelevanceScore; }
    public boolean hasFeedback() { return mHasFeedback; }
    
    // Setters
    public AiSuggestion setTitle(String title) { mTitle = title; return this; }
    public AiSuggestion setDescription(String description) { mDescription = description; return this; }
    public AiSuggestion setActionText(String actionText) { mActionText = actionText; return this; }
    public AiSuggestion setPriority(Priority priority) { mPriority = priority; return this; }
    public AiSuggestion setIconResourceName(String iconResourceName) { mIconResourceName = iconResourceName; return this; }
    public AiSuggestion setIconColor(int iconColor) { mIconColor = iconColor; return this; }
    public AiSuggestion setBackgroundColor(String backgroundColor) { mBackgroundColor = backgroundColor; return this; }
    public AiSuggestion setActionId(String actionId) { mActionId = actionId; return this; }
    public AiSuggestion setActionData(Bundle actionData) { mActionData = actionData; return this; }
    public AiSuggestion setExpiryTime(long expiryTime) { mExpiryTime = expiryTime; return this; }
    public AiSuggestion setConfidenceScore(float confidenceScore) { mConfidenceScore = confidenceScore; return this; }
    public AiSuggestion setContextSource(String contextSource) { mContextSource = contextSource; return this; }
    public AiSuggestion setContextData(Bundle contextData) { mContextData = contextData; return this; }
    
    // Utility methods
    public boolean isExpired() {
        return System.currentTimeMillis() > mExpiryTime;
    }
    
    public boolean isValid() {
        return !isExpired() && !mIsDismissed && mTitle != null && !mTitle.isEmpty();
    }
    
    public void markAsUsed() {
        mIsUsed = true;
        mUsageCount++;
    }
    
    public void markAsDismissed() {
        mIsDismissed = true;
    }
    
    public void recordFeedback(boolean positive) {
        if (positive) {
            mPositiveFeedback++;
        } else {
            mNegativeFeedback++;
        }
        mHasFeedback = true;
        updateRelevanceScore();
    }
    
    public void addRequiredPermission(String permission) {
        if (!mRequiredPermissions.contains(permission)) {
            mRequiredPermissions.add(permission);
        }
    }
    
    public void addContextTag(String tag) {
        if (!mContextTags.contains(tag)) {
            mContextTags.add(tag);
        }
    }
    
    public boolean hasContextTag(String tag) {
        return mContextTags.contains(tag);
    }
    
    public float getOverallScore() {
        // Combine confidence, relevance, priority, and usage for ranking
        float priorityWeight = mPriority.getValue() / 4.0f;
        float usageWeight = Math.min(mUsageCount / 10.0f, 1.0f);
        float timeWeight = getTimeRelevanceWeight();
        
        return (mConfidenceScore * 0.3f) + 
               (mRelevanceScore * 0.3f) + 
               (priorityWeight * 0.2f) + 
               (usageWeight * 0.1f) + 
               (timeWeight * 0.1f);
    }
    
    private void updateRelevanceScore() {
        int totalFeedback = mPositiveFeedback + mNegativeFeedback;
        if (totalFeedback > 0) {
            mRelevanceScore = (float) mPositiveFeedback / totalFeedback;
        }
    }
    
    private float getTimeRelevanceWeight() {
        long age = System.currentTimeMillis() - mCreatedTime;
        long maxAge = mExpiryTime - mCreatedTime;
        
        if (maxAge <= 0) return 0f;
        
        // Newer suggestions get higher weight
        return 1.0f - ((float) age / maxAge);
    }
    
    @Override
    public String toString() {
        return "AiSuggestion{" +
                "id='" + mId + '\'' +
                ", title='" + mTitle + '\'' +
                ", type=" + mType +
                ", priority=" + mPriority +
                ", confidenceScore=" + mConfidenceScore +
                ", overallScore=" + getOverallScore() +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        AiSuggestion that = (AiSuggestion) obj;
        return mId != null ? mId.equals(that.mId) : that.mId == null;
    }
    
    @Override
    public int hashCode() {
        return mId != null ? mId.hashCode() : 0;
    }
    
    // Parcelable implementation
    @Override
    public int describeContents() {
        return 0;
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mId);
        dest.writeString(mTitle);
        dest.writeString(mDescription);
        dest.writeString(mActionText);
        dest.writeString(mType.name());
        dest.writeString(mPriority.name());
        dest.writeString(mIconResourceName);
        dest.writeInt(mIconColor);
        dest.writeString(mBackgroundColor);
        dest.writeString(mActionId);
        dest.writeBundle(mActionData);
        dest.writeStringList(mRequiredPermissions);
        dest.writeLong(mCreatedTime);
        dest.writeLong(mExpiryTime);
        dest.writeFloat(mConfidenceScore);
        dest.writeInt(mUsageCount);
        dest.writeByte((byte) (mIsUsed ? 1 : 0));
        dest.writeByte((byte) (mIsDismissed ? 1 : 0));
        dest.writeString(mContextSource);
        dest.writeBundle(mContextData);
        dest.writeStringList(mContextTags);
        dest.writeInt(mPositiveFeedback);
        dest.writeInt(mNegativeFeedback);
        dest.writeFloat(mRelevanceScore);
        dest.writeByte((byte) (mHasFeedback ? 1 : 0));
    }
    
    // Builder pattern for complex suggestion creation
    public static class Builder {
        private AiSuggestion suggestion;
        
        public Builder(String id, String title, SuggestionType type) {
            suggestion = new AiSuggestion(id, title, "", type);
        }
        
        public Builder setDescription(String description) {
            suggestion.setDescription(description);
            return this;
        }
        
        public Builder setActionText(String actionText) {
            suggestion.setActionText(actionText);
            return this;
        }
        
        public Builder setPriority(Priority priority) {
            suggestion.setPriority(priority);
            return this;
        }
        
        public Builder setIcon(String iconResourceName, int iconColor) {
            suggestion.setIconResourceName(iconResourceName);
            suggestion.setIconColor(iconColor);
            return this;
        }
        
        public Builder setAction(String actionId, Bundle actionData) {
            suggestion.setActionId(actionId);
            suggestion.setActionData(actionData);
            return this;
        }
        
        public Builder setConfidenceScore(float score) {
            suggestion.setConfidenceScore(score);
            return this;
        }
        
        public Builder setExpiryTime(long expiryTime) {
            suggestion.setExpiryTime(expiryTime);
            return this;
        }
        
        public Builder addPermission(String permission) {
            suggestion.addRequiredPermission(permission);
            return this;
        }
        
        public Builder addContextTag(String tag) {
            suggestion.addContextTag(tag);
            return this;
        }
        
        public Builder setContext(String source, Bundle data) {
            suggestion.setContextSource(source);
            suggestion.setContextData(data);
            return this;
        }
        
        public AiSuggestion build() {
            return suggestion;
        }
    }
}
