/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.content.Context;
import android.util.Slog;

import com.android.server.ai.planning.WorkflowDataTypes.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Advanced plan optimizer for workflow efficiency and performance.
 * 
 * Optimizes workflow plans for:
 * - Execution time reduction
 * - Resource usage optimization
 * - Parallel execution opportunities
 * - Error handling improvements
 */
public class PlanOptimizer {
    private static final String TAG = "PlanOptimizer";
    private static final boolean DEBUG = true;
    
    // Optimization thresholds
    private static final double MIN_OPTIMIZATION_BENEFIT = 0.1; // 10% improvement
    private static final int MAX_PARALLEL_STEPS = 5;
    private static final long MAX_STEP_DURATION_MS = 60000; // 1 minute
    
    private final Context mContext;
    private final PerformanceProfiler mProfiler;
    private final ResourceAnalyzer mResourceAnalyzer;
    
    public PlanOptimizer(Context context) {
        mContext = context;
        mProfiler = new PerformanceProfiler();
        mResourceAnalyzer = new ResourceAnalyzer();
        
        if (DEBUG) Slog.d(TAG, "PlanOptimizer initialized");
    }
    
    /**
     * Optimize workflow plan for better performance and efficiency
     */
    public WorkflowPlan optimizeWorkflow(WorkflowPlan originalPlan) {
        if (DEBUG) Slog.d(TAG, "Optimizing workflow: " + originalPlan.workflowId);
        
        try {
            // Create a copy for optimization
            WorkflowPlan optimizedPlan = cloneWorkflowPlan(originalPlan);
            
            // Apply various optimization strategies
            optimizeStepOrdering(optimizedPlan);
            optimizeParallelExecution(optimizedPlan);
            optimizeResourceUsage(optimizedPlan);
            optimizeErrorHandling(optimizedPlan);
            optimizeTimeouts(optimizedPlan);
            
            // Validate optimization benefits
            WorkflowOptimization optimization = analyzeOptimization(originalPlan, optimizedPlan);
            
            if (optimization.estimatedTimeSaving > 0 || optimization.confidenceImprovement > MIN_OPTIMIZATION_BENEFIT) {
                if (DEBUG) Slog.d(TAG, "Workflow optimization successful: " + 
                        optimization.estimatedTimeSaving + "ms saved");
                return optimizedPlan;
            } else {
                if (DEBUG) Slog.d(TAG, "No significant optimization benefit found, returning original plan");
                return originalPlan;
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error optimizing workflow", e);
            return originalPlan; // Return original plan if optimization fails
        }
    }
    
    /**
     * Optimize step ordering for better dependency resolution
     */
    private void optimizeStepOrdering(WorkflowPlan plan) {
        if (plan.phases == null) return;
        
        for (WorkflowPhase phase : plan.phases) {
            if (phase.steps == null || phase.steps.size() <= 1) continue;
            
            // Build dependency graph
            Map<String, Set<String>> dependencyGraph = buildDependencyGraph(phase.steps);
            
            // Topological sort for optimal ordering
            List<WorkflowStep> optimizedSteps = topologicalSort(phase.steps, dependencyGraph);
            
            if (optimizedSteps != null && optimizedSteps.size() == phase.steps.size()) {
                phase.steps = optimizedSteps;
                if (DEBUG) Slog.d(TAG, "Optimized step ordering for phase: " + phase.phaseId);
            }
        }
    }
    
    /**
     * Optimize parallel execution opportunities
     */
    private void optimizeParallelExecution(WorkflowPlan plan) {
        if (plan.phases == null) return;
        
        for (WorkflowPhase phase : plan.phases) {
            if (phase.steps == null || phase.steps.size() <= 1) continue;
            
            // Identify steps that can run in parallel
            List<List<WorkflowStep>> parallelGroups = identifyParallelGroups(phase.steps);
            
            if (parallelGroups.size() > 1) {
                // Mark steps for parallel execution
                for (List<WorkflowStep> group : parallelGroups) {
                    if (group.size() > 1 && group.size() <= MAX_PARALLEL_STEPS) {
                        for (WorkflowStep step : group) {
                            step.canRunInParallel = true;
                        }
                    }
                }
                
                phase.parallelExecution = true;
                if (DEBUG) Slog.d(TAG, "Enabled parallel execution for phase: " + phase.phaseId);
            }
        }
    }
    
    /**
     * Optimize resource usage across steps
     */
    private void optimizeResourceUsage(WorkflowPlan plan) {
        if (plan.phases == null) return;
        
        // Analyze resource usage patterns
        Map<String, Integer> resourceUsage = analyzeResourceUsage(plan);
        
        // Optimize resource allocation
        for (WorkflowPhase phase : plan.phases) {
            if (phase.steps == null) continue;
            
            for (WorkflowStep step : phase.steps) {
                if (step.resourceRequirements != null) {
                    // Remove redundant resource requirements
                    step.resourceRequirements = optimizeStepResources(step.resourceRequirements, resourceUsage);
                }
            }
        }
        
        // Update plan-level resource requirements
        plan.resourceRequirements = optimizePlanResources(plan.resourceRequirements, resourceUsage);
        
        if (DEBUG) Slog.d(TAG, "Optimized resource usage for workflow: " + plan.workflowId);
    }
    
    /**
     * Optimize error handling strategies
     */
    private void optimizeErrorHandling(WorkflowPlan plan) {
        if (plan.phases == null) return;
        
        for (WorkflowPhase phase : plan.phases) {
            if (phase.steps == null) continue;
            
            for (WorkflowStep step : phase.steps) {
                // Set appropriate error handling based on step criticality
                if (step.required) {
                    step.errorHandling = "retry";
                } else if (step.canRunInParallel) {
                    step.errorHandling = "skip";
                } else {
                    step.errorHandling = "retry";
                }
                
                // Adjust priority based on dependencies
                step.priority = calculateStepPriority(step, phase.steps);
            }
        }
        
        if (DEBUG) Slog.d(TAG, "Optimized error handling for workflow: " + plan.workflowId);
    }
    
    /**
     * Optimize timeouts based on historical performance
     */
    private void optimizeTimeouts(WorkflowPlan plan) {
        if (plan.phases == null) return;
        
        for (WorkflowPhase phase : plan.phases) {
            if (phase.steps == null) continue;
            
            for (WorkflowStep step : phase.steps) {
                // Get historical performance data
                long historicalDuration = mProfiler.getAverageStepDuration(step.action);
                
                if (historicalDuration > 0) {
                    // Set timeout to 2x historical average with minimum bounds
                    long optimizedTimeout = Math.max(historicalDuration * 2, 5000);
                    optimizedTimeout = Math.min(optimizedTimeout, MAX_STEP_DURATION_MS);
                    
                    if (optimizedTimeout != step.timeout) {
                        step.timeout = optimizedTimeout;
                        if (DEBUG) Slog.d(TAG, "Optimized timeout for step " + step.stepId + 
                                ": " + optimizedTimeout + "ms");
                    }
                }
            }
        }
    }
    
    /**
     * Build dependency graph for steps
     */
    private Map<String, Set<String>> buildDependencyGraph(List<WorkflowStep> steps) {
        Map<String, Set<String>> graph = new HashMap<>();
        
        for (WorkflowStep step : steps) {
            graph.put(step.stepId, new HashSet<>());
            
            if (step.dependencies != null) {
                for (String dependency : step.dependencies) {
                    graph.get(step.stepId).add(dependency);
                }
            }
        }
        
        return graph;
    }
    
    /**
     * Topological sort for optimal step ordering
     */
    private List<WorkflowStep> topologicalSort(List<WorkflowStep> steps, Map<String, Set<String>> dependencyGraph) {
        List<WorkflowStep> result = new ArrayList<>();
        Set<String> visited = new HashSet<>();
        Set<String> visiting = new HashSet<>();
        Map<String, WorkflowStep> stepMap = new HashMap<>();
        
        // Create step lookup map
        for (WorkflowStep step : steps) {
            stepMap.put(step.stepId, step);
        }
        
        // Perform DFS-based topological sort
        for (WorkflowStep step : steps) {
            if (!visited.contains(step.stepId)) {
                if (!topologicalSortDFS(step.stepId, dependencyGraph, stepMap, visited, visiting, result)) {
                    // Cycle detected, return null
                    return null;
                }
            }
        }
        
        return result;
    }
    
    /**
     * DFS helper for topological sort
     */
    private boolean topologicalSortDFS(String stepId, Map<String, Set<String>> graph, 
                                     Map<String, WorkflowStep> stepMap, Set<String> visited, 
                                     Set<String> visiting, List<WorkflowStep> result) {
        if (visiting.contains(stepId)) {
            // Cycle detected
            return false;
        }
        
        if (visited.contains(stepId)) {
            return true;
        }
        
        visiting.add(stepId);
        
        Set<String> dependencies = graph.get(stepId);
        if (dependencies != null) {
            for (String dependency : dependencies) {
                if (!topologicalSortDFS(dependency, graph, stepMap, visited, visiting, result)) {
                    return false;
                }
            }
        }
        
        visiting.remove(stepId);
        visited.add(stepId);
        result.add(0, stepMap.get(stepId)); // Add to beginning for reverse order
        
        return true;
    }
    
    /**
     * Identify groups of steps that can run in parallel
     */
    private List<List<WorkflowStep>> identifyParallelGroups(List<WorkflowStep> steps) {
        List<List<WorkflowStep>> parallelGroups = new ArrayList<>();
        Set<String> processed = new HashSet<>();
        
        for (WorkflowStep step : steps) {
            if (processed.contains(step.stepId)) continue;
            
            List<WorkflowStep> group = new ArrayList<>();
            group.add(step);
            processed.add(step.stepId);
            
            // Find other steps that can run in parallel with this step
            for (WorkflowStep otherStep : steps) {
                if (processed.contains(otherStep.stepId)) continue;
                
                if (canRunInParallel(step, otherStep, steps)) {
                    group.add(otherStep);
                    processed.add(otherStep.stepId);
                }
            }
            
            parallelGroups.add(group);
        }
        
        return parallelGroups;
    }
    
    /**
     * Check if two steps can run in parallel
     */
    private boolean canRunInParallel(WorkflowStep step1, WorkflowStep step2, List<WorkflowStep> allSteps) {
        // Check if there's a dependency relationship
        if (hasDependencyRelationship(step1, step2, allSteps)) {
            return false;
        }
        
        // Check for resource conflicts
        if (hasResourceConflict(step1, step2)) {
            return false;
        }
        
        // Check if both steps support parallel execution
        return step1.canRunInParallel && step2.canRunInParallel;
    }
    
    /**
     * Check if there's a dependency relationship between steps
     */
    private boolean hasDependencyRelationship(WorkflowStep step1, WorkflowStep step2, List<WorkflowStep> allSteps) {
        // Direct dependency check
        if (step1.dependencies != null && step1.dependencies.contains(step2.stepId)) {
            return true;
        }
        if (step2.dependencies != null && step2.dependencies.contains(step1.stepId)) {
            return true;
        }
        
        // Transitive dependency check (simplified)
        return false;
    }
    
    /**
     * Check for resource conflicts between steps
     */
    private boolean hasResourceConflict(WorkflowStep step1, WorkflowStep step2) {
        if (step1.resourceRequirements == null || step2.resourceRequirements == null) {
            return false;
        }
        
        // Check for exclusive resource usage
        for (String resource1 : step1.resourceRequirements) {
            for (String resource2 : step2.resourceRequirements) {
                if (resource1.equals(resource2) && isExclusiveResource(resource1)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check if a resource requires exclusive access
     */
    private boolean isExclusiveResource(String resource) {
        // Define resources that require exclusive access
        return "camera".equals(resource) || 
               "microphone".equals(resource) || 
               "speaker".equals(resource) ||
               "display".equals(resource);
    }
    
    /**
     * Analyze resource usage across the workflow
     */
    private Map<String, Integer> analyzeResourceUsage(WorkflowPlan plan) {
        Map<String, Integer> usage = new HashMap<>();
        
        if (plan.phases != null) {
            for (WorkflowPhase phase : plan.phases) {
                if (phase.steps != null) {
                    for (WorkflowStep step : phase.steps) {
                        if (step.resourceRequirements != null) {
                            for (String resource : step.resourceRequirements) {
                                usage.put(resource, usage.getOrDefault(resource, 0) + 1);
                            }
                        }
                    }
                }
            }
        }
        
        return usage;
    }
    
    /**
     * Optimize step-level resource requirements
     */
    private List<String> optimizeStepResources(List<String> resources, Map<String, Integer> globalUsage) {
        List<String> optimized = new ArrayList<>();
        
        for (String resource : resources) {
            // Keep resource if it's used frequently or is critical
            if (globalUsage.getOrDefault(resource, 0) > 1 || isCriticalResource(resource)) {
                optimized.add(resource);
            }
        }
        
        return optimized.isEmpty() ? resources : optimized;
    }
    
    /**
     * Optimize plan-level resource requirements
     */
    private List<String> optimizePlanResources(List<String> resources, Map<String, Integer> usage) {
        if (resources == null) return new ArrayList<>();
        
        List<String> optimized = new ArrayList<>();
        
        for (String resource : resources) {
            // Keep resource if it's actually used in the plan
            if (usage.containsKey(resource)) {
                optimized.add(resource);
            }
        }
        
        return optimized;
    }
    
    /**
     * Check if a resource is critical for execution
     */
    private boolean isCriticalResource(String resource) {
        return "network".equals(resource) || 
               "storage".equals(resource) || 
               "cpu".equals(resource) ||
               "memory".equals(resource);
    }
    
    /**
     * Calculate step priority based on dependencies and criticality
     */
    private int calculateStepPriority(WorkflowStep step, List<WorkflowStep> allSteps) {
        int priority = step.required ? 10 : 5;
        
        // Increase priority based on number of dependents
        int dependentCount = 0;
        for (WorkflowStep otherStep : allSteps) {
            if (otherStep.dependencies != null && otherStep.dependencies.contains(step.stepId)) {
                dependentCount++;
            }
        }
        
        priority += dependentCount * 2;
        
        return Math.min(priority, 20); // Cap at 20
    }
    
    /**
     * Clone workflow plan for optimization
     */
    private WorkflowPlan cloneWorkflowPlan(WorkflowPlan original) {
        // Simple cloning implementation
        // In production, this would be a deep clone
        WorkflowPlan clone = new WorkflowPlan();
        clone.workflowId = original.workflowId + "_optimized";
        clone.goal = original.goal;
        clone.workflowType = original.workflowType;
        clone.estimatedDuration = original.estimatedDuration;
        clone.confidence = original.confidence;
        
        // Clone phases and steps
        if (original.phases != null) {
            clone.phases = new ArrayList<>();
            for (WorkflowPhase phase : original.phases) {
                clone.phases.add(cloneWorkflowPhase(phase));
            }
        }
        
        // Clone other collections
        if (original.synchronizationPoints != null) {
            clone.synchronizationPoints = new ArrayList<>(original.synchronizationPoints);
        }
        if (original.resourceRequirements != null) {
            clone.resourceRequirements = new ArrayList<>(original.resourceRequirements);
        }
        
        return clone;
    }
    
    /**
     * Clone workflow phase
     */
    private WorkflowPhase cloneWorkflowPhase(WorkflowPhase original) {
        WorkflowPhase clone = new WorkflowPhase();
        clone.phaseId = original.phaseId;
        clone.description = original.description;
        clone.parallelExecution = original.parallelExecution;
        clone.estimatedDuration = original.estimatedDuration;
        
        if (original.steps != null) {
            clone.steps = new ArrayList<>();
            for (WorkflowStep step : original.steps) {
                clone.steps.add(cloneWorkflowStep(step));
            }
        }
        
        if (original.prerequisites != null) {
            clone.prerequisites = new ArrayList<>(original.prerequisites);
        }
        
        return clone;
    }
    
    /**
     * Clone workflow step
     */
    private WorkflowStep cloneWorkflowStep(WorkflowStep original) {
        WorkflowStep clone = new WorkflowStep();
        clone.stepId = original.stepId;
        clone.action = original.action;
        clone.description = original.description;
        clone.targetApp = original.targetApp;
        clone.timeout = original.timeout;
        clone.canRunInParallel = original.canRunInParallel;
        clone.required = original.required;
        clone.priority = original.priority;
        clone.errorHandling = original.errorHandling;
        
        // Clone parameters (shallow copy for now)
        clone.parameters = original.parameters;
        
        if (original.dependencies != null) {
            clone.dependencies = new ArrayList<>(original.dependencies);
        }
        if (original.resourceRequirements != null) {
            clone.resourceRequirements = new ArrayList<>(original.resourceRequirements);
        }
        
        return clone;
    }
    
    /**
     * Analyze optimization benefits
     */
    private WorkflowOptimization analyzeOptimization(WorkflowPlan original, WorkflowPlan optimized) {
        WorkflowOptimization optimization = new WorkflowOptimization();
        optimization.originalPlan = original;
        optimization.optimizedPlan = optimized;
        optimization.optimizations = new ArrayList<>();
        
        // Calculate time savings
        optimization.estimatedTimeSaving = original.estimatedDuration - optimized.estimatedDuration;
        
        // Calculate confidence improvement
        optimization.confidenceImprovement = optimized.confidence - original.confidence;
        
        // Identify specific optimizations
        if (optimization.estimatedTimeSaving > 0) {
            optimization.optimizations.add("Reduced execution time by " + optimization.estimatedTimeSaving + "ms");
        }
        
        if (optimization.confidenceImprovement > 0) {
            optimization.optimizations.add("Improved confidence by " + 
                    String.format("%.2f", optimization.confidenceImprovement * 100) + "%");
        }
        
        return optimization;
    }
    
    /**
     * Performance profiler for historical data
     */
    private static class PerformanceProfiler {
        private final Map<String, Long> averageDurations = new HashMap<>();
        
        public PerformanceProfiler() {
            // Initialize with some default values
            averageDurations.put("openApp", 3000L);
            averageDurations.put("setSystemSetting", 1000L);
            averageDurations.put("sendNotification", 500L);
            averageDurations.put("makeCall", 5000L);
            averageDurations.put("sendMessage", 2000L);
            averageDurations.put("setAlarm", 1000L);
            averageDurations.put("createCalendarEvent", 2000L);
            averageDurations.put("searchWeb", 3000L);
            averageDurations.put("readFile", 1000L);
            averageDurations.put("writeFile", 2000L);
        }
        
        public long getAverageStepDuration(String action) {
            return averageDurations.getOrDefault(action, 5000L);
        }
    }
    
    /**
     * Resource analyzer for optimization
     */
    private static class ResourceAnalyzer {
        public ResourceAnalyzer() {
            // Initialize resource analysis capabilities
        }
    }
}
