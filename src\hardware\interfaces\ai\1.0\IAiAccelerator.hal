/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware.ai@1.0;

import IAiAcceleratorCallback;

/**
 * AI Accelerator HAL Interface for Jarvis OS
 * 
 * Provides hardware acceleration for AI inference operations,
 * including NPU, GPU, and DSP acceleration capabilities.
 */
interface IAiAccelerator {
    /**
     * AI accelerator types supported by the hardware
     */
    enum AcceleratorType : uint32_t {
        CPU = 0,
        GPU = 1,
        NPU = 2,        // Neural Processing Unit
        DSP = 3,        // Digital Signal Processor
        TPU = 4,        // Tensor Processing Unit
        VPU = 5,        // Vision Processing Unit
        CUSTOM = 100,   // Custom accelerator
    };

    /**
     * AI model formats supported by the accelerator
     */
    enum ModelFormat : uint32_t {
        TENSORFLOW_LITE = 0,
        ONNX = 1,
        PYTORCH = 2,
        CAFFE2 = 3,
        CUSTOM = 100,
    };

    /**
     * Data types supported for tensor operations
     */
    enum DataType : uint32_t {
        FLOAT32 = 0,
        FLOAT16 = 1,
        INT32 = 2,
        INT16 = 3,
        INT8 = 4,
        UINT8 = 5,
        BOOL = 6,
    };

    /**
     * Execution priority levels
     */
    enum Priority : uint32_t {
        LOW = 0,
        NORMAL = 1,
        HIGH = 2,
        REALTIME = 3,
    };

    /**
     * Result codes for HAL operations
     */
    enum Result : uint32_t {
        OK = 0,
        INVALID_ARGUMENT = 1,
        NO_MEMORY = 2,
        TIMEOUT = 3,
        DEVICE_UNAVAILABLE = 4,
        GENERAL_FAILURE = 5,
        OUTPUT_INSUFFICIENT_SIZE = 6,
        UNSUPPORTED_OPERATION = 7,
    };

    /**
     * Accelerator capabilities structure
     */
    struct AcceleratorCapabilities {
        AcceleratorType type;
        string name;
        string vendor;
        string version;
        uint32_t maxConcurrentInferences;
        uint64_t maxMemoryBytes;
        vec<ModelFormat> supportedFormats;
        vec<DataType> supportedDataTypes;
        bool supportsQuantization;
        bool supportsBatching;
        uint32_t maxBatchSize;
        float performanceScore;  // Relative performance indicator
    };

    /**
     * Model information structure
     */
    struct ModelInfo {
        uint32_t modelId;
        ModelFormat format;
        uint64_t sizeBytes;
        uint32_t inputCount;
        uint32_t outputCount;
        bool isQuantized;
        string checksum;
    };

    /**
     * Tensor descriptor
     */
    struct TensorDescriptor {
        vec<uint32_t> dimensions;
        DataType dataType;
        uint64_t sizeBytes;
        string name;
    };

    /**
     * Execution request structure
     */
    struct ExecutionRequest {
        uint32_t modelId;
        vec<memory> inputBuffers;
        vec<memory> outputBuffers;
        Priority priority;
        uint32_t timeoutMs;
        bool enableProfiling;
    };

    /**
     * Performance metrics
     */
    struct PerformanceMetrics {
        uint64_t executionTimeUs;
        uint64_t queueTimeUs;
        uint64_t memoryUsedBytes;
        float powerConsumptionMw;
        uint32_t cacheHits;
        uint32_t cacheMisses;
    };

    /**
     * Initialize the AI accelerator
     * 
     * @return result OK if successful, error code otherwise
     */
    initialize() generates (Result result);

    /**
     * Get accelerator capabilities
     * 
     * @return result OK if successful, error code otherwise
     * @return capabilities Accelerator capabilities information
     */
    getCapabilities() generates (Result result, AcceleratorCapabilities capabilities);

    /**
     * Check if accelerator is available and ready
     * 
     * @return result OK if available, error code otherwise
     * @return available True if accelerator is ready for use
     */
    isAvailable() generates (Result result, bool available);

    /**
     * Load an AI model onto the accelerator
     * 
     * @param modelData Binary model data
     * @param format Model format type
     * @param callback Callback for asynchronous operations
     * @return result OK if successful, error code otherwise
     * @return modelId Unique identifier for the loaded model
     */
    loadModel(memory modelData, ModelFormat format, IAiAcceleratorCallback callback)
        generates (Result result, uint32_t modelId);

    /**
     * Unload a model from the accelerator
     * 
     * @param modelId Model identifier to unload
     * @return result OK if successful, error code otherwise
     */
    unloadModel(uint32_t modelId) generates (Result result);

    /**
     * Get information about a loaded model
     * 
     * @param modelId Model identifier
     * @return result OK if successful, error code otherwise
     * @return modelInfo Information about the model
     */
    getModelInfo(uint32_t modelId) generates (Result result, ModelInfo modelInfo);

    /**
     * Get input tensor descriptors for a model
     * 
     * @param modelId Model identifier
     * @return result OK if successful, error code otherwise
     * @return inputTensors Array of input tensor descriptors
     */
    getInputTensors(uint32_t modelId) generates (Result result, vec<TensorDescriptor> inputTensors);

    /**
     * Get output tensor descriptors for a model
     * 
     * @param modelId Model identifier
     * @return result OK if successful, error code otherwise
     * @return outputTensors Array of output tensor descriptors
     */
    getOutputTensors(uint32_t modelId) generates (Result result, vec<TensorDescriptor> outputTensors);

    /**
     * Execute inference on a loaded model
     * 
     * @param request Execution request with input/output buffers
     * @param callback Callback for asynchronous execution
     * @return result OK if execution started, error code otherwise
     * @return executionId Unique identifier for this execution
     */
    executeInference(ExecutionRequest request, IAiAcceleratorCallback callback)
        generates (Result result, uint32_t executionId);

    /**
     * Execute inference synchronously
     * 
     * @param request Execution request with input/output buffers
     * @return result OK if successful, error code otherwise
     * @return metrics Performance metrics for the execution
     */
    executeInferenceSync(ExecutionRequest request)
        generates (Result result, PerformanceMetrics metrics);

    /**
     * Cancel a running inference execution
     * 
     * @param executionId Execution identifier to cancel
     * @return result OK if successful, error code otherwise
     */
    cancelExecution(uint32_t executionId) generates (Result result);

    /**
     * Get performance metrics for the accelerator
     * 
     * @return result OK if successful, error code otherwise
     * @return metrics Overall accelerator performance metrics
     */
    getPerformanceMetrics() generates (Result result, PerformanceMetrics metrics);

    /**
     * Set power management mode
     * 
     * @param powerMode Power management mode (0=low power, 1=balanced, 2=performance)
     * @return result OK if successful, error code otherwise
     */
    setPowerMode(uint32_t powerMode) generates (Result result);

    /**
     * Get current power consumption
     * 
     * @return result OK if successful, error code otherwise
     * @return powerMw Current power consumption in milliwatts
     */
    getPowerConsumption() generates (Result result, float powerMw);

    /**
     * Enable or disable debug mode
     * 
     * @param enable True to enable debug mode, false to disable
     * @return result OK if successful, error code otherwise
     */
    setDebugMode(bool enable) generates (Result result);

    /**
     * Get accelerator status and health information
     * 
     * @return result OK if successful, error code otherwise
     * @return status Status information as key-value pairs
     */
    getStatus() generates (Result result, vec<string> status);

    /**
     * Reset the accelerator to initial state
     * 
     * @return result OK if successful, error code otherwise
     */
    reset() generates (Result result);

    /**
     * Cleanup and shutdown the accelerator
     * 
     * @return result OK if successful, error code otherwise
     */
    shutdown() generates (Result result);
};
