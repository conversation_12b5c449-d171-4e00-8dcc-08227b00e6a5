/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

import java.util.ArrayList;
import java.util.List;

/**
 * Container for a message (data and object references) that can be sent through an IBinder.
 * This is a stub implementation for the Jarvis OS project.
 */
public final class Parcel {
    private List<Object> mData;
    private int mPosition;
    
    private Parcel() {
        mData = new ArrayList<>();
        mPosition = 0;
    }
    
    public static Parcel obtain() {
        return new Parcel();
    }
    
    public void recycle() {
        mData.clear();
        mPosition = 0;
    }
    
    // Write methods
    public void writeString(String val) {
        mData.add(val);
    }
    
    public void writeInt(int val) {
        mData.add(val);
    }
    
    public void writeLong(long val) {
        mData.add(val);
    }
    
    public void writeFloat(float val) {
        mData.add(val);
    }
    
    public void writeDouble(double val) {
        mData.add(val);
    }
    
    public void writeBoolean(boolean val) {
        mData.add(val);
    }
    
    public void writeBundle(Bundle val) {
        mData.add(val);
    }
    
    public void writeValue(Object val) {
        mData.add(val);
    }
    
    public void writeStringList(List<String> val) {
        mData.add(val);
    }
    
    // Read methods
    public String readString() {
        if (mPosition < mData.size()) {
            return (String) mData.get(mPosition++);
        }
        return null;
    }
    
    public int readInt() {
        if (mPosition < mData.size()) {
            return (Integer) mData.get(mPosition++);
        }
        return 0;
    }
    
    public long readLong() {
        if (mPosition < mData.size()) {
            return (Long) mData.get(mPosition++);
        }
        return 0L;
    }
    
    public float readFloat() {
        if (mPosition < mData.size()) {
            return (Float) mData.get(mPosition++);
        }
        return 0.0f;
    }
    
    public double readDouble() {
        if (mPosition < mData.size()) {
            return (Double) mData.get(mPosition++);
        }
        return 0.0;
    }
    
    public boolean readBoolean() {
        if (mPosition < mData.size()) {
            return (Boolean) mData.get(mPosition++);
        }
        return false;
    }
    
    public Bundle readBundle() {
        return readBundle(null);
    }
    
    public Bundle readBundle(ClassLoader loader) {
        if (mPosition < mData.size()) {
            return (Bundle) mData.get(mPosition++);
        }
        return null;
    }
    
    public Object readValue(ClassLoader loader) {
        if (mPosition < mData.size()) {
            return mData.get(mPosition++);
        }
        return null;
    }
    
    @SuppressWarnings("unchecked")
    public void readStringList(List<String> list) {
        if (mPosition < mData.size()) {
            List<String> data = (List<String>) mData.get(mPosition++);
            if (data != null) {
                list.addAll(data);
            }
        }
    }
    
    // Position methods
    public int dataPosition() {
        return mPosition;
    }
    
    public void setDataPosition(int pos) {
        mPosition = pos;
    }
    
    public int dataSize() {
        return mData.size();
    }

    // Additional methods needed for AIDL compilation
    public void enforceInterface(String descriptor) {
        // Stub implementation for interface enforcement
    }

    public void writeNoException() {
        // Stub implementation for exception handling
    }

    public void readException() {
        // Stub implementation for exception handling
    }

    public void writeStringArray(String[] array) {
        mData.add(array);
    }

    public String[] createStringArray() {
        if (mPosition < mData.size()) {
            return (String[]) mData.get(mPosition++);
        }
        return null;
    }

    public void writeInterfaceToken(String descriptor) {
        writeString(descriptor);
    }

    public <T extends Parcelable> T readParcelable(ClassLoader loader) {
        if (mPosition < mData.size()) {
            return (T) mData.get(mPosition++);
        }
        return null;
    }

    public void writeParcelable(Parcelable p, int flags) {
        mData.add(p);
    }

    public byte readByte() {
        if (mPosition < mData.size()) {
            return (Byte) mData.get(mPosition++);
        }
        return 0;
    }

    public void writeByte(byte value) {
        mData.add(value);
    }

    public ArrayList<String> createStringArrayList() {
        if (mPosition < mData.size()) {
            return (ArrayList<String>) mData.get(mPosition++);
        }
        return new ArrayList<>();
    }
}
