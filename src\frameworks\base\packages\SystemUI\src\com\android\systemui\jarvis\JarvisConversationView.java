/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.speech.RecognitionListener;
import android.speech.SpeechRecognizer;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.DecelerateInterpolator;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.systemui.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Main conversational interface for Jarvis AI assistant.
 * 
 * Provides a chat-like interface with support for:
 * - Text input and voice input
 * - Conversation history
 * - AI response display
 * - Quick action suggestions
 * - Accessibility features
 */
public class JarvisConversationView extends LinearLayout {
    private static final String TAG = "JarvisConversationView";
    private static final boolean DEBUG = true;
    
    // UI Components
    private RecyclerView mConversationRecyclerView;
    private EditText mTextInput;
    private ImageButton mVoiceButton;
    private ImageButton mSendButton;
    private ProgressBar mThinkingIndicator;
    private TextView mStatusText;
    private ScrollView mQuickActionsScroll;
    private LinearLayout mQuickActionsContainer;
    
    // Conversation Management
    private ConversationAdapter mConversationAdapter;
    private List<ConversationMessage> mConversationHistory;
    private Handler mMainHandler;
    
    // Voice Input
    private SpeechRecognizer mSpeechRecognizer;
    private boolean mIsListening = false;
    private boolean mIsProcessing = false;
    
    // Callbacks
    private ConversationListener mConversationListener;
    
    public interface ConversationListener {
        void onTextMessage(String message);
        void onVoiceMessage(String message);
        void onQuickAction(String actionId);
        void onConversationStateChanged(boolean isActive);
    }
    
    public JarvisConversationView(Context context) {
        this(context, null);
    }
    
    public JarvisConversationView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }
    
    public JarvisConversationView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }
    
    private void init(Context context) {
        mMainHandler = new Handler(Looper.getMainLooper());
        mConversationHistory = new ArrayList<>();
        
        setOrientation(VERTICAL);
        
        // Inflate the conversation layout
        LayoutInflater.from(context).inflate(R.layout.jarvis_conversation_view, this, true);
        
        initializeViews();
        setupConversationRecyclerView();
        setupInputHandlers();
        setupVoiceRecognition();
        setupQuickActions();
        
        if (DEBUG) Log.d(TAG, "JarvisConversationView initialized");
    }
    
    private void initializeViews() {
        mConversationRecyclerView = findViewById(R.id.conversation_recycler_view);
        mTextInput = findViewById(R.id.text_input);
        mVoiceButton = findViewById(R.id.voice_button);
        mSendButton = findViewById(R.id.send_button);
        mThinkingIndicator = findViewById(R.id.thinking_indicator);
        mStatusText = findViewById(R.id.status_text);
        mQuickActionsScroll = findViewById(R.id.quick_actions_scroll);
        mQuickActionsContainer = findViewById(R.id.quick_actions_container);
        
        // Initially hide thinking indicator
        mThinkingIndicator.setVisibility(GONE);
        mStatusText.setVisibility(GONE);
    }
    
    private void setupConversationRecyclerView() {
        mConversationAdapter = new ConversationAdapter(mConversationHistory);
        mConversationRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        mConversationRecyclerView.setAdapter(mConversationAdapter);
        
        // Auto-scroll to bottom when new messages are added
        mConversationAdapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                mConversationRecyclerView.scrollToPosition(mConversationAdapter.getItemCount() - 1);
            }
        });
    }
    
    private void setupInputHandlers() {
        // Text input handling
        mTextInput.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateSendButtonState();
            }
            
            @Override
            public void afterTextChanged(Editable s) {}
        });
        
        // Send button handling
        mSendButton.setOnClickListener(v -> sendTextMessage());
        
        // Voice button handling
        mVoiceButton.setOnClickListener(v -> toggleVoiceInput());
        
        updateSendButtonState();
    }
    
    private void setupVoiceRecognition() {
        if (SpeechRecognizer.isRecognitionAvailable(getContext())) {
            mSpeechRecognizer = SpeechRecognizer.createSpeechRecognizer(getContext());
            mSpeechRecognizer.setRecognitionListener(new VoiceRecognitionListener());
        } else {
            // Hide voice button if speech recognition is not available
            mVoiceButton.setVisibility(GONE);
        }
    }
    
    private void setupQuickActions() {
        // Add default quick actions
        addQuickAction("What can you do?", "help");
        addQuickAction("Open Settings", "open_settings");
        addQuickAction("Check Weather", "check_weather");
        addQuickAction("Set Reminder", "set_reminder");
    }
    
    public void setConversationListener(ConversationListener listener) {
        mConversationListener = listener;
    }
    
    public void addMessage(String message, boolean isUser) {
        ConversationMessage msg = new ConversationMessage(message, isUser, System.currentTimeMillis());
        mConversationHistory.add(msg);
        mConversationAdapter.notifyItemInserted(mConversationHistory.size() - 1);
        
        if (DEBUG) Log.d(TAG, "Added message: " + message + " (user: " + isUser + ")");
    }
    
    public void addAiResponse(String response) {
        addMessage(response, false);
        setProcessingState(false);
    }
    
    public void setProcessingState(boolean processing) {
        mIsProcessing = processing;
        
        if (processing) {
            mThinkingIndicator.setVisibility(VISIBLE);
            mStatusText.setVisibility(VISIBLE);
            mStatusText.setText("Jarvis is thinking...");
        } else {
            mThinkingIndicator.setVisibility(GONE);
            mStatusText.setVisibility(GONE);
        }
        
        updateInputState();
    }
    
    public void setListeningState(boolean listening) {
        mIsListening = listening;
        
        if (listening) {
            mVoiceButton.setSelected(true);
            mStatusText.setVisibility(VISIBLE);
            mStatusText.setText("Listening...");
        } else {
            mVoiceButton.setSelected(false);
            if (!mIsProcessing) {
                mStatusText.setVisibility(GONE);
            }
        }
        
        updateInputState();
    }
    
    private void updateInputState() {
        boolean inputEnabled = !mIsProcessing && !mIsListening;
        mTextInput.setEnabled(inputEnabled);
        mSendButton.setEnabled(inputEnabled && !mTextInput.getText().toString().trim().isEmpty());
        mVoiceButton.setEnabled(!mIsProcessing);
    }
    
    private void updateSendButtonState() {
        boolean hasText = !mTextInput.getText().toString().trim().isEmpty();
        mSendButton.setEnabled(hasText && !mIsProcessing && !mIsListening);
    }
    
    private void sendTextMessage() {
        String message = mTextInput.getText().toString().trim();
        if (message.isEmpty() || mIsProcessing) {
            return;
        }
        
        // Add user message to conversation
        addMessage(message, true);
        
        // Clear input
        mTextInput.setText("");
        
        // Set processing state
        setProcessingState(true);
        
        // Notify listener
        if (mConversationListener != null) {
            mConversationListener.onTextMessage(message);
        }
        
        if (DEBUG) Log.d(TAG, "Sent text message: " + message);
    }
    
    private void toggleVoiceInput() {
        if (mSpeechRecognizer == null) {
            return;
        }
        
        if (mIsListening) {
            stopVoiceInput();
        } else {
            startVoiceInput();
        }
    }
    
    private void startVoiceInput() {
        if (mSpeechRecognizer == null || mIsListening || mIsProcessing) {
            return;
        }
        
        setListeningState(true);
        
        // Start speech recognition
        // Implementation would include proper intent setup
        if (DEBUG) Log.d(TAG, "Started voice input");
    }
    
    private void stopVoiceInput() {
        if (mSpeechRecognizer == null || !mIsListening) {
            return;
        }
        
        setListeningState(false);
        mSpeechRecognizer.stopListening();
        
        if (DEBUG) Log.d(TAG, "Stopped voice input");
    }
    
    public void addQuickAction(String text, String actionId) {
        TextView actionView = new TextView(getContext());
        actionView.setText(text);
        actionView.setBackground(getContext().getDrawable(R.drawable.jarvis_quick_action_background));
        actionView.setPadding(24, 12, 24, 12);
        actionView.setClickable(true);
        actionView.setFocusable(true);
        
        actionView.setOnClickListener(v -> {
            if (mConversationListener != null) {
                mConversationListener.onQuickAction(actionId);
            }
        });
        
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.setMarginEnd(16);
        
        mQuickActionsContainer.addView(actionView, params);
    }
    
    public void clearConversation() {
        mConversationHistory.clear();
        mConversationAdapter.notifyDataSetChanged();
        setProcessingState(false);
        setListeningState(false);
    }
    
    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Handle orientation changes
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        
        if (mSpeechRecognizer != null) {
            mSpeechRecognizer.destroy();
        }
    }
    
    private class VoiceRecognitionListener implements RecognitionListener {
        @Override
        public void onReadyForSpeech(Bundle params) {
            if (DEBUG) Log.d(TAG, "Voice recognition ready");
        }
        
        @Override
        public void onBeginningOfSpeech() {
            if (DEBUG) Log.d(TAG, "Voice recognition started");
        }
        
        @Override
        public void onRmsChanged(float rmsdB) {
            // Update voice level indicator if needed
        }
        
        @Override
        public void onBufferReceived(byte[] buffer) {}
        
        @Override
        public void onEndOfSpeech() {
            if (DEBUG) Log.d(TAG, "Voice recognition ended");
        }
        
        @Override
        public void onError(int error) {
            Log.e(TAG, "Voice recognition error: " + error);
            setListeningState(false);
        }
        
        @Override
        public void onResults(Bundle results) {
            ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
            if (matches != null && !matches.isEmpty()) {
                String voiceMessage = matches.get(0);
                
                // Add user message to conversation
                addMessage(voiceMessage, true);
                
                // Set processing state
                setProcessingState(true);
                
                // Notify listener
                if (mConversationListener != null) {
                    mConversationListener.onVoiceMessage(voiceMessage);
                }
                
                if (DEBUG) Log.d(TAG, "Voice message: " + voiceMessage);
            }
            
            setListeningState(false);
        }
        
        @Override
        public void onPartialResults(Bundle partialResults) {
            // Handle partial results if needed
        }
        
        @Override
        public void onEvent(int eventType, Bundle params) {}
    }
}
