/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.content;

/**
 * Interface to global information about an application environment.
 * This is a stub implementation for the Jarvis OS project.
 */
public abstract class Context {
    
    // Service constants for AI services
    public static final String AI_CONTEXT_SERVICE = "ai_context";
    public static final String AI_PERSONALIZATION_SERVICE = "ai_personalization";
    public static final String AI_PLANNING_ORCHESTRATION_SERVICE = "ai_planning_orchestration";
    public static final String AI_USER_INTERFACE_SERVICE = "ai_user_interface";
    public static final String AI_SERVICE_COORDINATOR = "ai_service_coordinator";
    public static final String AI_CONTEXT_ENGINE_SERVICE = "ai_context_engine";
    
    // Standard Android services
    public static final String ACTIVITY_SERVICE = "activity";
    public static final String WINDOW_SERVICE = "window";
    public static final String NOTIFICATION_SERVICE = "notification";
    public static final String POWER_SERVICE = "power";
    public static final String CONNECTIVITY_SERVICE = "connectivity";
    
    /**
     * Return the context of the single, global Application object of the
     * current process.
     */
    public abstract Context getApplicationContext();
    
    /**
     * Return a class loader you can use to retrieve classes in this package.
     */
    public abstract ClassLoader getClassLoader();
    
    /**
     * Return the name of this application's package.
     */
    public abstract String getPackageName();
    
    /**
     * Return the full application info for this context's package.
     */
    public abstract Object getApplicationInfo();
    
    /**
     * Return a ContentResolver instance for your application's package.
     */
    public abstract Object getContentResolver();
    
    /**
     * Return the Looper for the main thread of the current process.
     */
    public abstract Object getMainLooper();
    
    /**
     * Return an AssetManager instance for your application's package.
     */
    public abstract Object getAssets();
    
    /**
     * Return a Resources instance for your application's package.
     */
    public abstract Object getResources();
    
    /**
     * Return PackageManager instance to find global package information.
     */
    public abstract Object getPackageManager();
    
    /**
     * Return the handle to a system-level service by name.
     */
    public abstract Object getSystemService(String name);
    
    /**
     * Determine whether you have been granted a particular permission.
     */
    public abstract int checkPermission(String permission, int pid, int uid);
    
    /**
     * Start an activity.
     */
    public abstract void startActivity(Object intent);
    
    /**
     * Start a service.
     */
    public abstract Object startService(Object intent);
    
    /**
     * Stop a service.
     */
    public abstract boolean stopService(Object intent);
    
    /**
     * Bind to an application service.
     */
    public abstract boolean bindService(Object intent, Object conn, int flags);
    
    /**
     * Disconnect from an application service.
     */
    public abstract void unbindService(Object conn);
    
    /**
     * Send a broadcast intent.
     */
    public abstract void sendBroadcast(Object intent);
    
    /**
     * Register a BroadcastReceiver.
     */
    public abstract Object registerReceiver(Object receiver, Object filter);
    
    /**
     * Unregister a previously registered BroadcastReceiver.
     */
    public abstract void unregisterReceiver(Object receiver);
}
