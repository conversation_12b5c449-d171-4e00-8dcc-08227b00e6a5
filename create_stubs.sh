#!/bin/bash

# Create Dependency-Free Stubs
echo "🔧 Creating dependency-free stubs to break circular dependencies..."

# Create output directory
mkdir -p build/stubs
rm -rf build/stubs/* 2>/dev/null

# Create minimal stub files that don't reference each other
echo "📦 Creating minimal stubs..."

# 1. Create minimal Parcelable interface (no dependencies)
cat > build/stubs/Parcelable.java << 'EOF'
package android.os;
public interface Parcelable {
    int describeContents();
    void writeToParcel(Object dest, int flags);
    interface Creator<T> {
        T createFromParcel(Object source);
        T[] newArray(int size);
    }
}
EOF

# 2. Create minimal Parcel class (no dependencies)
cat > build/stubs/Parcel.java << 'EOF'
package android.os;
import java.util.*;
public class Parcel {
    private List<Object> mData = new ArrayList<>();
    private int mPosition = 0;
    public void writeString(String val) { mData.add(val); }
    public void writeInt(int val) { mData.add(val); }
    public void writeLong(long val) { mData.add(val); }
    public void writeFloat(float val) { mData.add(val); }
    public void writeDouble(double val) { mData.add(val); }
    public void writeByte(byte val) { mData.add(val); }
    public void writeValue(Object val) { mData.add(val); }
    public String readString() { return mPosition < mData.size() ? (String) mData.get(mPosition++) : null; }
    public int readInt() { return mPosition < mData.size() ? (Integer) mData.get(mPosition++) : 0; }
    public long readLong() { return mPosition < mData.size() ? (Long) mData.get(mPosition++) : 0L; }
    public float readFloat() { return mPosition < mData.size() ? (Float) mData.get(mPosition++) : 0.0f; }
    public double readDouble() { return mPosition < mData.size() ? (Double) mData.get(mPosition++) : 0.0; }
    public byte readByte() { return mPosition < mData.size() ? (Byte) mData.get(mPosition++) : 0; }
    public Object readValue(ClassLoader loader) { return mPosition < mData.size() ? mData.get(mPosition++) : null; }
    public static Parcel obtain() { return new Parcel(); }
    public void recycle() { mData.clear(); mPosition = 0; }
}
EOF

# 3. Create minimal Bundle class (no dependencies)
cat > build/stubs/Bundle.java << 'EOF'
package android.os;
import java.util.*;
public final class Bundle implements Parcelable {
    private final Map<String, Object> mMap = new HashMap<>();
    public Bundle() {}
    public void putString(String key, String value) { mMap.put(key, value); }
    public void putInt(String key, int value) { mMap.put(key, value); }
    public void putLong(String key, long value) { mMap.put(key, value); }
    public void putFloat(String key, float value) { mMap.put(key, value); }
    public void putDouble(String key, double value) { mMap.put(key, value); }
    public void putBoolean(String key, boolean value) { mMap.put(key, value); }
    public void putBundle(String key, Bundle value) { mMap.put(key, value); }
    public String getString(String key) { return (String) mMap.get(key); }
    public String getString(String key, String defaultValue) { String val = getString(key); return val != null ? val : defaultValue; }
    public int getInt(String key) { Integer val = (Integer) mMap.get(key); return val != null ? val : 0; }
    public int getInt(String key, int defaultValue) { Integer val = (Integer) mMap.get(key); return val != null ? val : defaultValue; }
    public long getLong(String key) { Long val = (Long) mMap.get(key); return val != null ? val : 0L; }
    public long getLong(String key, long defaultValue) { Long val = (Long) mMap.get(key); return val != null ? val : defaultValue; }
    public float getFloat(String key) { Float val = (Float) mMap.get(key); return val != null ? val : 0.0f; }
    public float getFloat(String key, float defaultValue) { Float val = (Float) mMap.get(key); return val != null ? val : defaultValue; }
    public double getDouble(String key) { Double val = (Double) mMap.get(key); return val != null ? val : 0.0; }
    public double getDouble(String key, double defaultValue) { Double val = (Double) mMap.get(key); return val != null ? val : defaultValue; }
    public boolean getBoolean(String key) { Boolean val = (Boolean) mMap.get(key); return val != null ? val : false; }
    public boolean getBoolean(String key, boolean defaultValue) { Boolean val = (Boolean) mMap.get(key); return val != null ? val : defaultValue; }
    public Bundle getBundle(String key) { return (Bundle) mMap.get(key); }
    public boolean containsKey(String key) { return mMap.containsKey(key); }
    public Object get(String key) { return mMap.get(key); }
    public void remove(String key) { mMap.remove(key); }
    public void clear() { mMap.clear(); }
    public Set<String> keySet() { return mMap.keySet(); }
    public int size() { return mMap.size(); }
    public boolean isEmpty() { return mMap.isEmpty(); }
    public void putCharSequence(String key, CharSequence value) { mMap.put(key, value); }
    public CharSequence getCharSequence(String key) { Object value = mMap.get(key); return value instanceof CharSequence ? (CharSequence) value : null; }
    public CharSequence getCharSequence(String key, CharSequence defaultValue) { CharSequence value = getCharSequence(key); return value != null ? value : defaultValue; }
    public int describeContents() { return 0; }
    public void writeToParcel(Object dest, int flags) {}
    public static final Creator<Bundle> CREATOR = new Creator<Bundle>() {
        public Bundle createFromParcel(Object in) { return new Bundle(); }
        public Bundle[] newArray(int size) { return new Bundle[size]; }
    };
}
EOF

# 4. Create minimal RemoteException
cat > build/stubs/RemoteException.java << 'EOF'
package android.os;
public class RemoteException extends Exception {
    public RemoteException() { super(); }
    public RemoteException(String message) { super(message); }
    public RemoteException(String message, Throwable cause) { super(message, cause); }
}
EOF

# 5. Create minimal IInterface
cat > build/stubs/IInterface.java << 'EOF'
package android.os;
public interface IInterface {
    Object asBinder();
}
EOF

# 6. Create minimal IBinder
cat > build/stubs/IBinder.java << 'EOF'
package android.os;
public interface IBinder {
    int FIRST_CALL_TRANSACTION = 0x00000001;
    int LAST_CALL_TRANSACTION = 0x00ffffff;
    int PING_TRANSACTION = ('_'<<24)|('P'<<16)|('N'<<8)|'G';
    int DUMP_TRANSACTION = ('_'<<24)|('D'<<16)|('M'<<8)|'P';
    int INTERFACE_TRANSACTION = ('_'<<24)|('N'<<16)|('T'<<8)|'F';
    int FLAG_ONEWAY = 0x00000001;
    String getInterfaceDescriptor() throws RemoteException;
    boolean pingBinder();
    boolean isBinderAlive();
    IInterface queryLocalInterface(String descriptor);
    void dump(java.io.FileDescriptor fd, String[] args) throws RemoteException;
    void dumpAsync(java.io.FileDescriptor fd, String[] args) throws RemoteException;
    boolean transact(int code, Object data, Object reply, int flags) throws RemoteException;
    void linkToDeath(DeathRecipient recipient, int flags) throws RemoteException;
    boolean unlinkToDeath(DeathRecipient recipient, int flags);
    interface DeathRecipient { void binderDied(); }
}
EOF

echo "✅ Dependency-free stubs created in build/stubs/"
echo "📦 Compiling stubs..."

# Compile all stubs
cd build/stubs
javac -d . *.java 2>&1 | tee ../stub_build.log

compiled_classes=$(find . -name "*.class" 2>/dev/null | wc -l)
echo "Compiled stub classes: $compiled_classes"

if [ $compiled_classes -gt 5 ]; then
    echo "✅ STUB BUILD SUCCESSFUL!"
    echo "🎯 Now we can use these as a foundation for the real classes"
else
    echo "❌ STUB BUILD FAILED"
    echo "📋 Check ../stub_build.log for errors"
fi
