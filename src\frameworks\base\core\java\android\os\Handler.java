/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * A Handler allows you to send and process Message and Runnable objects
 * associated with a thread's MessageQueue.
 * This is a stub implementation for the Jarvis OS project.
 */
public class Handler {
    private Looper mLooper;
    private Executor mExecutor;
    
    public Handler() {
        this(Looper.myLooper());
    }
    
    public Handler(Looper looper) {
        mLooper = looper;
        mExecutor = Executors.newSingleThreadExecutor();
    }
    
    public Handler(Looper looper, Callback callback) {
        this(looper);
    }
    
    /**
     * Causes the Runnable r to be added to the message queue.
     */
    public final boolean post(Runnable r) {
        if (r != null) {
            mExecutor.execute(r);
            return true;
        }
        return false;
    }
    
    /**
     * Causes the Runnable r to be added to the message queue, to be run
     * after the specified amount of time elapses.
     */
    public final boolean postDelayed(Runnable r, long delayMillis) {
        if (r != null) {
            // Simplified implementation - just execute immediately for now
            mExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        Thread.sleep(delayMillis);
                        r.run();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
            return true;
        }
        return false;
    }
    
    /**
     * Causes the Runnable r to be added to the message queue, to be run
     * at a specific time given by uptimeMillis.
     */
    public final boolean postAtTime(Runnable r, long uptimeMillis) {
        return postDelayed(r, uptimeMillis - System.currentTimeMillis());
    }
    
    /**
     * Remove any pending posts of Runnable r that are in the message queue.
     */
    public final void removeCallbacks(Runnable r) {
        // Simplified implementation - cannot remove from executor
    }
    
    /**
     * Pushes a message onto the end of the message queue after all pending messages
     * before the current time.
     */
    public final boolean sendMessage(Message msg) {
        return sendMessageDelayed(msg, 0);
    }
    
    /**
     * Enqueue a message into the message queue after all pending messages
     * before (current time + delayMillis).
     */
    public final boolean sendMessageDelayed(Message msg, long delayMillis) {
        if (msg != null) {
            mExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        if (delayMillis > 0) {
                            Thread.sleep(delayMillis);
                        }
                        handleMessage(msg);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            });
            return true;
        }
        return false;
    }
    
    /**
     * Enqueue a message into the message queue after all pending messages
     * before the absolute time (in milliseconds) uptimeMillis.
     */
    public final boolean sendMessageAtTime(Message msg, long uptimeMillis) {
        return sendMessageDelayed(msg, uptimeMillis - System.currentTimeMillis());
    }
    
    /**
     * Subclasses must implement this to receive messages.
     */
    public void handleMessage(Message msg) {
        // Default implementation does nothing
    }
    
    /**
     * Returns the looper associated with this handler.
     */
    public final Looper getLooper() {
        return mLooper;
    }
    
    /**
     * Callback interface you can use when instantiating a Handler to avoid
     * having to implement your own subclass of Handler.
     */
    public interface Callback {
        /**
         * @param msg A Message object
         * @return True if no further handling is desired
         */
        public boolean handleMessage(Message msg);
    }
}
