<?xml version="1.0" encoding="utf-8"?>
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <aapt:attr name="android:drawable">
        <vector
            android:width="24dp"
            android:height="24dp"
            android:viewportWidth="24"
            android:viewportHeight="24">
            
            <group android:name="breathing_group">
                <path
                    android:name="breathing_path"
                    android:pathData="M12,2C6.48,2 2,6.48 2,12s4.48,10 10,10 10,-4.48 10,-10S17.52,2 12,2z"
                    android:fillColor="@color/jarvis_ai_active"
                    android:fillAlpha="0.3" />
            </group>
            
        </vector>
    </aapt:attr>
    
    <target android:name="breathing_group">
        <aapt:attr name="android:animation">
            <set android:ordering="together">
                <objectAnimator
                    android:propertyName="scaleX"
                    android:duration="2000"
                    android:valueFrom="1.0"
                    android:valueTo="1.2"
                    android:valueType="floatType"
                    android:repeatCount="infinite"
                    android:repeatMode="reverse"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator" />
                    
                <objectAnimator
                    android:propertyName="scaleY"
                    android:duration="2000"
                    android:valueFrom="1.0"
                    android:valueTo="1.2"
                    android:valueType="floatType"
                    android:repeatCount="infinite"
                    android:repeatMode="reverse"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator" />
                    
                <objectAnimator
                    android:propertyName="fillAlpha"
                    android:duration="2000"
                    android:valueFrom="0.3"
                    android:valueTo="0.7"
                    android:valueType="floatType"
                    android:repeatCount="infinite"
                    android:repeatMode="reverse"
                    android:interpolator="@android:anim/accelerate_decelerate_interpolator" />
            </set>
        </aapt:attr>
    </target>
    
</animated-vector>
