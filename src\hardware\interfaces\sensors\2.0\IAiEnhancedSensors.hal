/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware.sensors@2.0;

import android.hardware.sensors@1.0::ISensors;
import IAiSensorCallback;

/**
 * AI-Enhanced Sensors HAL Interface for Jarvis OS
 * 
 * Extends the standard sensors interface with AI-powered sensor fusion,
 * context-aware sensing, and intelligent power management.
 */
interface IAiEnhancedSensors extends @1.0::ISensors {
    /**
     * AI-enhanced sensor types
     */
    enum AiSensorType : uint32_t {
        // Standard sensors with AI enhancement
        AI_ACCELEROMETER = 1000,
        AI_GYROSCOPE = 1001,
        AI_MAGNETOMETER = 1002,
        AI_PROXIMITY = 1003,
        AI_LIGHT = 1004,
        AI_PRESSURE = 1005,
        AI_TEMPERATURE = 1006,
        AI_HUMIDITY = 1007,
        
        // AI-fused virtual sensors
        AI_MOTION_CLASSIFIER = 2000,    // Walking, running, sitting, etc.
        AI_ACTIVITY_DETECTOR = 2001,    // Driving, cycling, exercising, etc.
        AI_CONTEXT_DETECTOR = 2002,     // Indoor/outdoor, meeting, etc.
        AI_GESTURE_RECOGNIZER = 2003,   // Hand gestures, device orientation
        AI_ENVIRONMENTAL_ANALYZER = 2004, // Air quality, noise level, etc.
        AI_HEALTH_MONITOR = 2005,       // Heart rate, stress level, etc.
        AI_LOCATION_CONTEXT = 2006,     // Home, work, transit, etc.
        AI_DEVICE_USAGE_PATTERN = 2007, // Usage patterns and habits
    };

    /**
     * AI processing modes for sensors
     */
    enum AiProcessingMode : uint32_t {
        DISABLED = 0,           // No AI processing
        BASIC = 1,              // Basic AI filtering and noise reduction
        ENHANCED = 2,           // Advanced AI fusion and pattern recognition
        INTELLIGENT = 3,        // Full AI context awareness and prediction
    };

    /**
     * Context awareness levels
     */
    enum ContextAwarenessLevel : uint32_t {
        NONE = 0,               // No context awareness
        BASIC = 1,              // Basic environmental context
        ADVANCED = 2,           // Advanced behavioral context
        PREDICTIVE = 3,         // Predictive context with learning
    };

    /**
     * AI sensor configuration
     */
    struct AiSensorConfig {
        AiSensorType sensorType;
        AiProcessingMode processingMode;
        ContextAwarenessLevel contextLevel;
        uint32_t samplingRateHz;
        uint32_t reportingRateHz;
        float confidenceThreshold;
        bool enableLearning;
        bool enablePrediction;
        uint32_t maxLatencyMs;
        vec<string> enabledFeatures;
    };

    /**
     * AI-enhanced sensor data
     */
    struct AiSensorData {
        AiSensorType sensorType;
        uint64_t timestamp;
        vec<float> values;
        float confidence;
        uint32_t accuracy;
        string context;
        vec<string> metadata;
        bool isPrediction;
        uint64_t predictionHorizonMs;
    };

    /**
     * Motion classification result
     */
    struct MotionClassification {
        string activityType;        // "walking", "running", "sitting", etc.
        float confidence;
        uint64_t startTime;
        uint64_t duration;
        vec<float> features;        // Feature vector for the classification
    };

    /**
     * Environmental context data
     */
    struct EnvironmentalContext {
        string locationType;        // "indoor", "outdoor", "vehicle", etc.
        float noiseLevel;          // Ambient noise level in dB
        float lightLevel;          // Ambient light level in lux
        float airQualityIndex;     // Air quality index (0-500)
        float temperature;         // Temperature in Celsius
        float humidity;            // Relative humidity percentage
        float confidence;
    };

    /**
     * Gesture recognition result
     */
    struct GestureRecognition {
        string gestureType;        // "tap", "swipe", "shake", etc.
        vec<float> parameters;     // Gesture-specific parameters
        float confidence;
        uint64_t timestamp;
        uint64_t duration;
    };

    /**
     * Result codes for AI sensor operations
     */
    enum AiResult : uint32_t {
        OK = 0,
        INVALID_ARGUMENT = 1,
        UNSUPPORTED_SENSOR = 2,
        INSUFFICIENT_RESOURCES = 3,
        AI_ENGINE_UNAVAILABLE = 4,
        CALIBRATION_REQUIRED = 5,
        LEARNING_IN_PROGRESS = 6,
        GENERAL_FAILURE = 7,
    };

    /**
     * Initialize AI-enhanced sensor capabilities
     * 
     * @return result OK if successful, error code otherwise
     */
    initializeAiCapabilities() generates (AiResult result);

    /**
     * Get available AI-enhanced sensors
     * 
     * @return result OK if successful, error code otherwise
     * @return sensors List of available AI sensor types
     */
    getAvailableAiSensors() generates (AiResult result, vec<AiSensorType> sensors);

    /**
     * Configure an AI-enhanced sensor
     * 
     * @param config AI sensor configuration
     * @param callback Callback for sensor data and events
     * @return result OK if successful, error code otherwise
     * @return sensorHandle Handle for the configured sensor
     */
    configureAiSensor(AiSensorConfig config, IAiSensorCallback callback)
        generates (AiResult result, uint32_t sensorHandle);

    /**
     * Start AI-enhanced sensor data collection
     * 
     * @param sensorHandle Handle of the sensor to start
     * @return result OK if successful, error code otherwise
     */
    startAiSensor(uint32_t sensorHandle) generates (AiResult result);

    /**
     * Stop AI-enhanced sensor data collection
     * 
     * @param sensorHandle Handle of the sensor to stop
     * @return result OK if successful, error code otherwise
     */
    stopAiSensor(uint32_t sensorHandle) generates (AiResult result);

    /**
     * Get current motion classification
     * 
     * @return result OK if successful, error code otherwise
     * @return classification Current motion classification
     */
    getMotionClassification() generates (AiResult result, MotionClassification classification);

    /**
     * Get environmental context
     * 
     * @return result OK if successful, error code otherwise
     * @return context Current environmental context
     */
    getEnvironmentalContext() generates (AiResult result, EnvironmentalContext context);

    /**
     * Get recent gesture recognitions
     * 
     * @param maxResults Maximum number of results to return
     * @return result OK if successful, error code otherwise
     * @return gestures Recent gesture recognitions
     */
    getRecentGestures(uint32_t maxResults) generates (AiResult result, vec<GestureRecognition> gestures);

    /**
     * Enable or disable sensor learning
     * 
     * @param sensorHandle Handle of the sensor
     * @param enable True to enable learning, false to disable
     * @return result OK if successful, error code otherwise
     */
    setSensorLearning(uint32_t sensorHandle, bool enable) generates (AiResult result);

    /**
     * Trigger sensor calibration
     * 
     * @param sensorHandle Handle of the sensor to calibrate
     * @return result OK if calibration started, error code otherwise
     */
    calibrateSensor(uint32_t sensorHandle) generates (AiResult result);

    /**
     * Get sensor fusion status
     * 
     * @return result OK if successful, error code otherwise
     * @return status Sensor fusion status information
     */
    getSensorFusionStatus() generates (AiResult result, vec<string> status);

    /**
     * Set context awareness level for all sensors
     * 
     * @param level Context awareness level to set
     * @return result OK if successful, error code otherwise
     */
    setContextAwarenessLevel(ContextAwarenessLevel level) generates (AiResult result);

    /**
     * Get AI processing performance metrics
     * 
     * @return result OK if successful, error code otherwise
     * @return metrics Performance metrics as key-value pairs
     */
    getAiPerformanceMetrics() generates (AiResult result, vec<string> metrics);

    /**
     * Reset AI learning data for a sensor
     * 
     * @param sensorHandle Handle of the sensor
     * @return result OK if successful, error code otherwise
     */
    resetSensorLearning(uint32_t sensorHandle) generates (AiResult result);

    /**
     * Set power optimization mode for AI sensors
     * 
     * @param mode Power mode (0=max performance, 1=balanced, 2=power save)
     * @return result OK if successful, error code otherwise
     */
    setAiPowerMode(uint32_t mode) generates (AiResult result);

    /**
     * Get current AI sensor power consumption
     * 
     * @return result OK if successful, error code otherwise
     * @return powerMw Power consumption in milliwatts
     */
    getAiPowerConsumption() generates (AiResult result, float powerMw);
};
