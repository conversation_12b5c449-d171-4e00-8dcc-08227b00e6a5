/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.gemini;

import android.content.Context;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Client for secure communication with Gemini Advanced API.
 * 
 * Handles request/response processing, authentication, error handling,
 * and implements privacy-preserving communication protocols.
 */
public class GeminiAPIClient {
    private static final String TAG = "GeminiAPIClient";
    private static final boolean DEBUG = true;
    
    // API Configuration
    private static final String API_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/";
    private static final String GENERATE_CONTENT_ENDPOINT = "models/gemini-1.5-pro:generateContent";
    private static final int CONNECTION_TIMEOUT_MS = 30000;
    private static final int READ_TIMEOUT_MS = 60000;
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;
    
    // Request limits
    private static final int MAX_PROMPT_LENGTH = 30000;
    private static final int MAX_CONTEXT_LENGTH = 10000;
    
    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    private final ExecutorService mExecutorService;
    private final SecureRandom mSecureRandom;
    
    // Rate limiting
    private long mLastRequestTime = 0;
    private static final long MIN_REQUEST_INTERVAL_MS = 1000;
    
    public GeminiAPIClient(Context context, AiSecurityManager securityManager) {
        mContext = context;
        mSecurityManager = securityManager;
        mExecutorService = Executors.newFixedThreadPool(3);
        mSecureRandom = new SecureRandom();
        
        if (DEBUG) Slog.d(TAG, "GeminiAPIClient initialized");
    }
    
    /**
     * Generate task plan using Gemini API
     */
    public CompletableFuture<GeminiResponse> generateTaskPlan(String goal, String context, String packageName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Validate inputs
                if (goal == null || goal.trim().isEmpty()) {
                    throw new IllegalArgumentException("Goal cannot be empty");
                }
                
                if (goal.length() > MAX_PROMPT_LENGTH) {
                    throw new IllegalArgumentException("Goal too long: " + goal.length());
                }
                
                if (context != null && context.length() > MAX_CONTEXT_LENGTH) {
                    context = context.substring(0, MAX_CONTEXT_LENGTH) + "...";
                }
                
                // Create request
                GeminiRequest request = createTaskPlanningRequest(goal, context, packageName);
                
                // Make API call with retries
                return makeRequestWithRetries(request);
                
            } catch (Exception e) {
                Slog.e(TAG, "Error generating task plan", e);
                return createErrorResponse("Failed to generate task plan: " + e.getMessage());
            }
        }, mExecutorService);
    }
    
    /**
     * Validate task plan using Gemini API
     */
    public CompletableFuture<GeminiResponse> validateTaskPlan(String planJson, String context, String packageName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Create validation request
                GeminiRequest request = createPlanValidationRequest(planJson, context, packageName);
                
                // Make API call with retries
                return makeRequestWithRetries(request);
                
            } catch (Exception e) {
                Slog.e(TAG, "Error validating task plan", e);
                return createErrorResponse("Failed to validate task plan: " + e.getMessage());
            }
        }, mExecutorService);
    }
    
    /**
     * Create task planning request
     */
    private GeminiRequest createTaskPlanningRequest(String goal, String context, String packageName) {
        try {
            JSONObject request = new JSONObject();
            
            // Create contents array
            JSONArray contents = new JSONArray();
            JSONObject content = new JSONObject();
            JSONArray parts = new JSONArray();
            
            // Build system prompt
            String systemPrompt = buildTaskPlanningPrompt(goal, context);
            
            JSONObject part = new JSONObject();
            part.put("text", systemPrompt);
            parts.put(part);
            
            content.put("parts", parts);
            content.put("role", "user");
            contents.put(content);
            
            request.put("contents", contents);
            
            // Add generation config
            JSONObject generationConfig = new JSONObject();
            generationConfig.put("temperature", 0.1);
            generationConfig.put("topK", 40);
            generationConfig.put("topP", 0.95);
            generationConfig.put("maxOutputTokens", 2048);
            request.put("generationConfig", generationConfig);
            
            // Add safety settings
            JSONArray safetySettings = new JSONArray();
            String[] categories = {"HARM_CATEGORY_HARASSMENT", "HARM_CATEGORY_HATE_SPEECH", 
                                 "HARM_CATEGORY_SEXUALLY_EXPLICIT", "HARM_CATEGORY_DANGEROUS_CONTENT"};
            for (String category : categories) {
                JSONObject safety = new JSONObject();
                safety.put("category", category);
                safety.put("threshold", "BLOCK_MEDIUM_AND_ABOVE");
                safetySettings.put(safety);
            }
            request.put("safetySettings", safetySettings);
            
            return new GeminiRequest(GENERATE_CONTENT_ENDPOINT, request.toString(), packageName);
            
        } catch (JSONException e) {
            throw new RuntimeException("Failed to create request JSON", e);
        }
    }
    
    /**
     * Create plan validation request
     */
    private GeminiRequest createPlanValidationRequest(String planJson, String context, String packageName) {
        try {
            JSONObject request = new JSONObject();
            
            // Create contents array
            JSONArray contents = new JSONArray();
            JSONObject content = new JSONObject();
            JSONArray parts = new JSONArray();
            
            // Build validation prompt
            String validationPrompt = buildPlanValidationPrompt(planJson, context);
            
            JSONObject part = new JSONObject();
            part.put("text", validationPrompt);
            parts.put(part);
            
            content.put("parts", parts);
            content.put("role", "user");
            contents.put(content);
            
            request.put("contents", contents);
            
            // Add generation config for validation
            JSONObject generationConfig = new JSONObject();
            generationConfig.put("temperature", 0.0);
            generationConfig.put("topK", 1);
            generationConfig.put("maxOutputTokens", 1024);
            request.put("generationConfig", generationConfig);
            
            return new GeminiRequest(GENERATE_CONTENT_ENDPOINT, request.toString(), packageName);
            
        } catch (JSONException e) {
            throw new RuntimeException("Failed to create validation request JSON", e);
        }
    }
    
    /**
     * Make HTTP request with retry logic
     */
    private GeminiResponse makeRequestWithRetries(GeminiRequest request) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                // Rate limiting
                enforceRateLimit();
                
                // Make the actual HTTP request
                GeminiResponse response = makeHttpRequest(request);
                
                if (response.isSuccess()) {
                    if (DEBUG) Slog.d(TAG, "Request successful on attempt " + attempt);
                    return response;
                }
                
                // If not successful but not an exception, return the response
                if (attempt == MAX_RETRIES) {
                    return response;
                }
                
            } catch (Exception e) {
                lastException = e;
                Slog.w(TAG, "Request attempt " + attempt + " failed", e);
                
                if (attempt < MAX_RETRIES) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        // All retries failed
        String errorMessage = "All retry attempts failed";
        if (lastException != null) {
            errorMessage += ": " + lastException.getMessage();
        }
        
        return createErrorResponse(errorMessage);
    }
    
    /**
     * Make actual HTTP request to Gemini API
     */
    private GeminiResponse makeHttpRequest(GeminiRequest request) throws IOException {
        String apiKey = mSecurityManager.getGeminiApiKey();
        if (apiKey == null || apiKey.isEmpty()) {
            throw new IllegalStateException("Gemini API key not configured");
        }
        
        URL url = new URL(API_BASE_URL + request.getEndpoint() + "?key=" + apiKey);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // Configure connection
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", "JarvisOS/1.0");
            connection.setRequestProperty("X-Request-ID", generateRequestId());
            connection.setConnectTimeout(CONNECTION_TIMEOUT_MS);
            connection.setReadTimeout(READ_TIMEOUT_MS);
            connection.setDoOutput(true);
            
            // Write request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = request.getBody().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
            
            // Read response
            int responseCode = connection.getResponseCode();
            String responseBody;
            
            if (responseCode >= 200 && responseCode < 300) {
                responseBody = readInputStream(connection.getInputStream());
            } else {
                responseBody = readInputStream(connection.getErrorStream());
            }
            
            // Log the request for audit
            mSecurityManager.logApiRequest(request.getPackageName(), request.getEndpoint(), 
                                         responseCode, System.currentTimeMillis());
            
            return new GeminiResponse(responseCode, responseBody, responseCode >= 200 && responseCode < 300);
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * Read input stream to string
     */
    private String readInputStream(java.io.InputStream inputStream) throws IOException {
        if (inputStream == null) return "";
        
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        return response.toString();
    }
    
    /**
     * Enforce rate limiting
     */
    private void enforceRateLimit() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastRequest = currentTime - mLastRequestTime;
        
        if (timeSinceLastRequest < MIN_REQUEST_INTERVAL_MS) {
            try {
                Thread.sleep(MIN_REQUEST_INTERVAL_MS - timeSinceLastRequest);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        mLastRequestTime = System.currentTimeMillis();
    }
    
    /**
     * Generate unique request ID
     */
    private String generateRequestId() {
        return "req_" + System.currentTimeMillis() + "_" + mSecureRandom.nextInt(10000);
    }
    
    /**
     * Create error response
     */
    private GeminiResponse createErrorResponse(String errorMessage) {
        return new GeminiResponse(500, "{\"error\":\"" + errorMessage + "\"}", false);
    }
    
    /**
     * Build task planning prompt
     */
    private String buildTaskPlanningPrompt(String goal, String context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are Jarvis, an advanced AI assistant integrated into Android OS. ");
        prompt.append("Create a detailed task plan to accomplish the following goal.\n\n");
        
        prompt.append("GOAL: ").append(goal).append("\n\n");
        
        if (context != null && !context.trim().isEmpty()) {
            prompt.append("CURRENT CONTEXT:\n").append(context).append("\n\n");
        }
        
        prompt.append("Please provide a JSON response with the following structure:\n");
        prompt.append("{\n");
        prompt.append("  \"taskId\": \"unique_task_id\",\n");
        prompt.append("  \"goal\": \"original goal\",\n");
        prompt.append("  \"steps\": [\n");
        prompt.append("    {\n");
        prompt.append("      \"stepId\": \"step_1\",\n");
        prompt.append("      \"action\": \"action_type\",\n");
        prompt.append("      \"description\": \"human readable description\",\n");
        prompt.append("      \"parameters\": {\"key\": \"value\"},\n");
        prompt.append("      \"dependencies\": [\"previous_step_ids\"],\n");
        prompt.append("      \"timeout\": 30000\n");
        prompt.append("    }\n");
        prompt.append("  ],\n");
        prompt.append("  \"estimatedDuration\": 60000,\n");
        prompt.append("  \"confidence\": 0.95\n");
        prompt.append("}\n\n");
        
        prompt.append("Available actions: openApp, setSystemSetting, sendNotification, makeCall, ");
        prompt.append("sendMessage, setAlarm, createCalendarEvent, searchWeb, readFile, writeFile\n\n");
        
        prompt.append("Ensure the plan is safe, feasible, and respects user privacy.");
        
        return prompt.toString();
    }
    
    /**
     * Build plan validation prompt
     */
    private String buildPlanValidationPrompt(String planJson, String context) {
        StringBuilder prompt = new StringBuilder();
        
        prompt.append("You are Jarvis, validating a task plan for safety and feasibility.\n\n");
        
        prompt.append("TASK PLAN TO VALIDATE:\n").append(planJson).append("\n\n");
        
        if (context != null && !context.trim().isEmpty()) {
            prompt.append("CURRENT CONTEXT:\n").append(context).append("\n\n");
        }
        
        prompt.append("Please validate this plan and respond with JSON:\n");
        prompt.append("{\n");
        prompt.append("  \"isValid\": true/false,\n");
        prompt.append("  \"confidence\": 0.0-1.0,\n");
        prompt.append("  \"issues\": [\"list of any issues found\"],\n");
        prompt.append("  \"suggestions\": [\"list of improvements\"],\n");
        prompt.append("  \"riskLevel\": \"low/medium/high\"\n");
        prompt.append("}\n\n");
        
        prompt.append("Check for: safety, privacy, feasibility, logical flow, dependencies.");
        
        return prompt.toString();
    }
    
    /**
     * Shutdown the client
     */
    public void shutdown() {
        if (mExecutorService != null && !mExecutorService.isShutdown()) {
            mExecutorService.shutdown();
            try {
                if (!mExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    mExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                mExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (DEBUG) Slog.d(TAG, "GeminiAPIClient shutdown");
    }
}
