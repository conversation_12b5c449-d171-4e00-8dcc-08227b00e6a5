<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/jarvis_background_primary">

    <!-- Search Header -->
    <LinearLayout
        android:id="@+id/search_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/jarvis_gradient_background"
        android:padding="@dimen/jarvis_spacing_lg"
        android:elevation="@dimen/jarvis_elevation_md">

        <!-- Search Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/jarvis_search_background"
            android:padding="@dimen/jarvis_spacing_md"
            android:layout_marginBottom="@dimen/jarvis_spacing_md">

            <ImageView
                android:layout_width="@dimen/jarvis_icon_size_md"
                android:layout_height="@dimen/jarvis_icon_size_md"
                android:src="@drawable/ic_search"
                android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                android:contentDescription="@string/search" />

            <EditText
                android:id="@+id/search_input"
                style="@style/JarvisInput"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="@string/search_hint"
                android:background="@android:color/transparent"
                android:padding="0dp"
                android:textColorHint="@color/jarvis_text_tertiary" />

            <ImageButton
                android:id="@+id/voice_search_button"
                android:layout_width="@dimen/jarvis_icon_size_md"
                android:layout_height="@dimen/jarvis_icon_size_md"
                android:src="@drawable/ic_mic"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/voice_search" />

        </LinearLayout>

        <!-- Quick Filters -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/quick_filters_recycler"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

    </LinearLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="120dp"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/jarvis_spacing_md">

            <!-- AI Recommendations -->
            <LinearLayout
                android:id="@+id/ai_recommendations_section"
                style="@style/JarvisCard.Elevated"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_lg">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_ai_recommendations"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/ai_recommendations" />

                    <TextView
                        style="@style/JarvisText.Headline3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/ai_recommendations" />

                    <ImageButton
                        android:id="@+id/refresh_recommendations_button"
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_refresh"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/refresh_recommendations" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/ai_recommendations_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- Categories Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_lg">

                <TextView
                    style="@style/JarvisText.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/categories"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md" />

                <GridLayout
                    android:id="@+id/categories_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:columnCount="3"
                    android:alignmentMode="alignBounds"
                    android:useDefaultMargins="true">

                    <!-- System Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/system_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_system"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/system_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/system_category"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Apps Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/apps_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_apps"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/apps_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/apps_category"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- AI Tools Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/ai_tools_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_ai_tools"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/ai_tools_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ai_tools_category"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Settings Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/settings_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_settings"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/settings_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/settings_category"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Productivity Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/productivity_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_productivity"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/productivity_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/productivity_category"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Entertainment Category -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/entertainment_category">

                        <ImageView
                            android:layout_width="@dimen/jarvis_icon_size_lg"
                            android:layout_height="@dimen/jarvis_icon_size_lg"
                            android:src="@drawable/ic_entertainment"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/entertainment_category" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/entertainment_category"
                            android:gravity="center" />

                    </LinearLayout>

                </GridLayout>

            </LinearLayout>

            <!-- Recent Items -->
            <LinearLayout
                style="@style/JarvisCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_xxl">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_recent"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/recent_items" />

                    <TextView
                        style="@style/JarvisText.Headline3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/recent_items" />

                    <Button
                        android:id="@+id/clear_recent_button"
                        style="@style/JarvisButton.Text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/clear_recent" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recent_items_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Radial Menu Overlay -->
    <FrameLayout
        android:id="@+id/radial_menu_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/jarvis_overlay_background"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true">

        <!-- Radial Menu Container -->
        <FrameLayout
            android:id="@+id/radial_menu_container"
            android:layout_width="300dp"
            android:layout_height="300dp"
            android:layout_gravity="center" />

    </FrameLayout>

    <!-- Floating Menu Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/radial_menu_fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|start"
        android:layout_margin="@dimen/jarvis_spacing_md"
        android:src="@drawable/ic_radial_menu"
        app:backgroundTint="@color/jarvis_secondary"
        app:tint="@color/jarvis_text_inverse"
        app:elevation="@dimen/jarvis_elevation_lg"
        android:contentDescription="@string/radial_menu" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
