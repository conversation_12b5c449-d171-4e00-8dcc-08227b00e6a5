/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "ai_security.h"
#include <android/log.h>
#include <cutils/log.h>
#include <utils/Mutex.h>
#include <keystore/keystore.h>
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/kdf.h>
#include <openssl/hmac.h>
#include <sys/mman.h>
#include <memory>
#include <unordered_map>
#include <string>

#define LOG_TAG "libai_security"
#define ALOGD_IF_DEBUG(cond, ...) if (cond) ALOGD(__VA_ARGS__)

namespace {

// Global state
static bool g_initialized = false;
static bool g_debug_logging = false;
static android::Mutex g_mutex;
static ai_crypto_config_t g_config;

// Key and context registries
static std::unordered_map<ai_key_handle_t*, std::unique_ptr<ai_key_handle_t>> g_keys;
static std::unordered_map<ai_crypto_context_t*, std::unique_ptr<ai_crypto_context_t>> g_contexts;
static std::unordered_map<ai_secure_buffer_t*, std::unique_ptr<ai_secure_buffer_t>> g_buffers;

// Internal structures
struct ai_key_handle {
    std::string alias;
    ai_data_level_t protection_level;
    bool exportable;
    bool hardware_backed;
    size_t key_size_bits;
    uint64_t creation_time;
    uint64_t validity_duration;
    void* key_data;  // For software keys
    size_t key_data_size;
    
    ai_key_handle() : key_data(nullptr), key_data_size(0) {}
    ~ai_key_handle() {
        if (key_data) {
            ai_secure_memwipe(key_data, key_data_size);
            free(key_data);
        }
    }
};

struct ai_crypto_context {
    ai_key_handle_t* key_handle;
    ai_crypto_config_t config;
    EVP_CIPHER_CTX* cipher_ctx;
    bool is_encrypt_mode;
    bool is_initialized;
    
    ai_crypto_context() : key_handle(nullptr), cipher_ctx(nullptr), 
                         is_encrypt_mode(false), is_initialized(false) {}
    ~ai_crypto_context() {
        if (cipher_ctx) {
            EVP_CIPHER_CTX_free(cipher_ctx);
        }
    }
};

struct ai_secure_buffer {
    void* data;
    size_t size;
    size_t capacity;
    bool is_locked;
    ai_data_level_t protection_level;
    
    ai_secure_buffer() : data(nullptr), size(0), capacity(0), 
                        is_locked(false), protection_level(AI_DATA_LEVEL_PUBLIC) {}
    ~ai_secure_buffer() {
        if (data) {
            if (is_locked) {
                munlock(data, capacity);
            }
            ai_secure_memwipe(data, capacity);
            free(data);
        }
    }
};

// Helper functions
const EVP_CIPHER* get_cipher_for_algorithm(ai_encryption_algorithm_t algorithm) {
    switch (algorithm) {
        case AI_ENCRYPTION_AES_256_GCM:
            return EVP_aes_256_gcm();
        case AI_ENCRYPTION_AES_128_GCM:
            return EVP_aes_128_gcm();
        case AI_ENCRYPTION_CHACHA20_POLY1305:
            return EVP_chacha20_poly1305();
        default:
            return EVP_aes_256_gcm();
    }
}

bool is_hardware_backed_available_impl(ai_storage_type_t storage_type) {
    switch (storage_type) {
        case AI_STORAGE_KEYSTORE:
            // Check if Android Keystore is available
            return access("/system/bin/keystore", F_OK) == 0;
        case AI_STORAGE_TEE:
            // Check for TEE availability
            return access("/dev/tee0", F_OK) == 0 || access("/dev/trusty-ipc-dev0", F_OK) == 0;
        case AI_STORAGE_SECURE_ELEMENT:
            // Check for Secure Element
            return access("/dev/ese", F_OK) == 0;
        default:
            return false;
    }
}

ai_security_result_t generate_key_impl(const ai_key_params_t* params, ai_key_handle_t** key_handle) {
    if (!params || !key_handle) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    auto new_key = std::make_unique<ai_key_handle_t>();
    
    new_key->alias = params->key_alias ? params->key_alias : "";
    new_key->protection_level = params->protection_level;
    new_key->exportable = params->exportable;
    new_key->key_size_bits = params->key_size_bits;
    new_key->creation_time = time(nullptr);
    new_key->validity_duration_seconds = params->validity_duration_seconds;
    
    // Determine if hardware backing is available and required
    new_key->hardware_backed = is_hardware_backed_available_impl(AI_STORAGE_KEYSTORE) &&
                              (params->protection_level >= AI_DATA_LEVEL_SENSITIVE);
    
    if (new_key->hardware_backed) {
        // Use Android Keystore for hardware-backed keys
        // In production, this would use the Keystore API
        ALOGD_IF_DEBUG(g_debug_logging, "Generating hardware-backed key: %s", new_key->alias.c_str());
    } else {
        // Generate software key
        size_t key_bytes = params->key_size_bits / 8;
        new_key->key_data = malloc(key_bytes);
        if (!new_key->key_data) {
            return AI_SECURITY_ERROR_OUT_OF_MEMORY;
        }
        
        new_key->key_data_size = key_bytes;
        
        // Generate random key material
        if (RAND_bytes(static_cast<unsigned char*>(new_key->key_data), key_bytes) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
        
        ALOGD_IF_DEBUG(g_debug_logging, "Generated software key: %s (%zu bits)", 
                       new_key->alias.c_str(), params->key_size_bits);
    }
    
    ai_key_handle_t* key_ptr = new_key.get();
    g_keys[key_ptr] = std::move(new_key);
    *key_handle = key_ptr;
    
    return AI_SECURITY_SUCCESS;
}

ai_security_result_t encrypt_impl(ai_crypto_context_t* context, const uint8_t* plaintext,
                                 size_t plaintext_size, const ai_cipher_params_t* params,
                                 uint8_t* ciphertext, size_t ciphertext_size, size_t* actual_size) {
    if (!context || !plaintext || !ciphertext || !actual_size) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    if (!context->is_initialized || !context->is_encrypt_mode) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    
    int len;
    int ciphertext_len = 0;
    
    // Set IV if provided
    if (params && params->iv) {
        if (EVP_EncryptInit_ex(context->cipher_ctx, nullptr, nullptr, nullptr, params->iv) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    // Set AAD if provided (for AEAD ciphers)
    if (params && params->aad && params->aad_size > 0) {
        if (EVP_EncryptUpdate(context->cipher_ctx, nullptr, &len, params->aad, params->aad_size) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    // Encrypt the plaintext
    if (EVP_EncryptUpdate(context->cipher_ctx, ciphertext, &len, plaintext, plaintext_size) != 1) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    ciphertext_len = len;
    
    // Finalize encryption
    if (EVP_EncryptFinal_ex(context->cipher_ctx, ciphertext + len, &len) != 1) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    ciphertext_len += len;
    
    // Get authentication tag for AEAD ciphers
    if (params && params->tag && params->tag_size > 0) {
        if (EVP_CIPHER_CTX_ctrl(context->cipher_ctx, EVP_CTRL_AEAD_GET_TAG, 
                               params->tag_size, params->tag) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    *actual_size = ciphertext_len;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Encrypted %zu bytes to %d bytes", plaintext_size, ciphertext_len);
    return AI_SECURITY_SUCCESS;
}

ai_security_result_t decrypt_impl(ai_crypto_context_t* context, const uint8_t* ciphertext,
                                 size_t ciphertext_size, const ai_cipher_params_t* params,
                                 uint8_t* plaintext, size_t plaintext_size, size_t* actual_size) {
    if (!context || !ciphertext || !plaintext || !actual_size) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    if (!context->is_initialized || context->is_encrypt_mode) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    
    int len;
    int plaintext_len = 0;
    
    // Set IV if provided
    if (params && params->iv) {
        if (EVP_DecryptInit_ex(context->cipher_ctx, nullptr, nullptr, nullptr, params->iv) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    // Set AAD if provided (for AEAD ciphers)
    if (params && params->aad && params->aad_size > 0) {
        if (EVP_DecryptUpdate(context->cipher_ctx, nullptr, &len, params->aad, params->aad_size) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    // Decrypt the ciphertext
    if (EVP_DecryptUpdate(context->cipher_ctx, plaintext, &len, ciphertext, ciphertext_size) != 1) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    plaintext_len = len;
    
    // Set authentication tag for AEAD ciphers
    if (params && params->tag && params->tag_size > 0) {
        if (EVP_CIPHER_CTX_ctrl(context->cipher_ctx, EVP_CTRL_AEAD_SET_TAG, 
                               params->tag_size, const_cast<uint8_t*>(params->tag)) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
    }
    
    // Finalize decryption
    int ret = EVP_DecryptFinal_ex(context->cipher_ctx, plaintext + len, &len);
    if (ret <= 0) {
        return AI_SECURITY_ERROR_INVALID_SIGNATURE;
    }
    plaintext_len += len;
    
    *actual_size = plaintext_len;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Decrypted %zu bytes to %d bytes", ciphertext_size, plaintext_len);
    return AI_SECURITY_SUCCESS;
}

} // anonymous namespace

// Public API implementation
extern "C" {

ai_security_result_t ai_security_init(const ai_crypto_config_t* config) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (g_initialized) {
        return AI_SECURITY_SUCCESS;
    }
    
    if (config) {
        g_config = *config;
    } else {
        // Set default configuration
        g_config.algorithm = AI_ENCRYPTION_AES_256_GCM;
        g_config.kdf = AI_KDF_PBKDF2_SHA256;
        g_config.key_storage = AI_STORAGE_KEYSTORE;
        g_config.use_hardware_backing = true;
        g_config.require_user_authentication = false;
        g_config.auth_timeout_seconds = 300;
    }
    
    // Initialize OpenSSL
    OpenSSL_add_all_algorithms();
    
    g_initialized = true;
    ALOGD("AI Security library initialized");
    return AI_SECURITY_SUCCESS;
}

void ai_security_cleanup(void) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (!g_initialized) {
        return;
    }
    
    // Clean up all contexts
    g_contexts.clear();
    
    // Clean up all secure buffers
    g_buffers.clear();
    
    // Clean up all keys
    g_keys.clear();
    
    // Cleanup OpenSSL
    EVP_cleanup();
    
    g_initialized = false;
    ALOGD("AI Security library cleaned up");
}

void ai_security_get_version(int* major, int* minor, int* patch) {
    if (major) *major = AI_SECURITY_VERSION_MAJOR;
    if (minor) *minor = AI_SECURITY_VERSION_MINOR;
    if (patch) *patch = AI_SECURITY_VERSION_PATCH;
}

ai_security_result_t ai_key_generate(const ai_key_params_t* params, ai_key_handle_t** key_handle) {
    if (!g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    return generate_key_impl(params, key_handle);
}

ai_security_result_t ai_key_load(const char* key_alias, ai_key_handle_t** key_handle) {
    if (!key_alias || !key_handle || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    // In production, this would load from Android Keystore
    // For now, return key not found
    return AI_SECURITY_ERROR_KEY_NOT_FOUND;
}

ai_security_result_t ai_key_delete(const char* key_alias) {
    if (!key_alias || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    // In production, this would delete from Android Keystore
    return AI_SECURITY_SUCCESS;
}

void ai_key_release(ai_key_handle_t* key_handle) {
    if (!key_handle) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_keys.find(key_handle);
    if (it != g_keys.end()) {
        g_keys.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Key released");
    }
}

ai_security_result_t ai_crypto_context_create(const ai_key_handle_t* key_handle,
                                             const ai_crypto_config_t* config,
                                             ai_crypto_context_t** context) {
    if (!key_handle || !context || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    if (g_keys.find(const_cast<ai_key_handle_t*>(key_handle)) == g_keys.end()) {
        return AI_SECURITY_ERROR_KEY_NOT_FOUND;
    }
    
    auto new_context = std::make_unique<ai_crypto_context_t>();
    new_context->key_handle = const_cast<ai_key_handle_t*>(key_handle);
    new_context->config = config ? *config : g_config;
    
    // Create OpenSSL cipher context
    new_context->cipher_ctx = EVP_CIPHER_CTX_new();
    if (!new_context->cipher_ctx) {
        return AI_SECURITY_ERROR_OUT_OF_MEMORY;
    }
    
    ai_crypto_context_t* context_ptr = new_context.get();
    g_contexts[context_ptr] = std::move(new_context);
    *context = context_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Crypto context created");
    return AI_SECURITY_SUCCESS;
}

void ai_crypto_context_destroy(ai_crypto_context_t* context) {
    if (!context) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_contexts.find(context);
    if (it != g_contexts.end()) {
        g_contexts.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Crypto context destroyed");
    }
}

ai_security_result_t ai_encrypt(ai_crypto_context_t* context, const uint8_t* plaintext,
                               size_t plaintext_size, const ai_cipher_params_t* params,
                               uint8_t* ciphertext, size_t ciphertext_size, size_t* actual_size) {
    if (!context || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    // Initialize context for encryption if not already done
    if (!context->is_initialized) {
        const EVP_CIPHER* cipher = get_cipher_for_algorithm(context->config.algorithm);
        
        // Use key material from key handle
        const unsigned char* key_data = nullptr;
        if (context->key_handle->key_data) {
            key_data = static_cast<const unsigned char*>(context->key_handle->key_data);
        }
        
        if (EVP_EncryptInit_ex(context->cipher_ctx, cipher, nullptr, key_data, nullptr) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
        
        context->is_encrypt_mode = true;
        context->is_initialized = true;
    }
    
    return encrypt_impl(context, plaintext, plaintext_size, params, ciphertext, ciphertext_size, actual_size);
}

ai_security_result_t ai_decrypt(ai_crypto_context_t* context, const uint8_t* ciphertext,
                               size_t ciphertext_size, const ai_cipher_params_t* params,
                               uint8_t* plaintext, size_t plaintext_size, size_t* actual_size) {
    if (!context || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    // Initialize context for decryption if not already done
    if (!context->is_initialized) {
        const EVP_CIPHER* cipher = get_cipher_for_algorithm(context->config.algorithm);
        
        // Use key material from key handle
        const unsigned char* key_data = nullptr;
        if (context->key_handle->key_data) {
            key_data = static_cast<const unsigned char*>(context->key_handle->key_data);
        }
        
        if (EVP_DecryptInit_ex(context->cipher_ctx, cipher, nullptr, key_data, nullptr) != 1) {
            return AI_SECURITY_ERROR_CRYPTO_FAILED;
        }
        
        context->is_encrypt_mode = false;
        context->is_initialized = true;
    }
    
    return decrypt_impl(context, ciphertext, ciphertext_size, params, plaintext, plaintext_size, actual_size);
}

ai_security_result_t ai_secure_buffer_create(size_t size, ai_data_level_t protection_level,
                                            ai_secure_buffer_t** buffer) {
    if (!buffer || size == 0 || !g_initialized) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    auto new_buffer = std::make_unique<ai_secure_buffer_t>();
    
    new_buffer->capacity = size;
    new_buffer->size = 0;
    new_buffer->protection_level = protection_level;
    
    // Allocate memory
    new_buffer->data = malloc(size);
    if (!new_buffer->data) {
        return AI_SECURITY_ERROR_OUT_OF_MEMORY;
    }
    
    // Clear the buffer
    ai_secure_memwipe(new_buffer->data, size);
    
    ai_secure_buffer_t* buffer_ptr = new_buffer.get();
    g_buffers[buffer_ptr] = std::move(new_buffer);
    *buffer = buffer_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Secure buffer created: %zu bytes", size);
    return AI_SECURITY_SUCCESS;
}

void ai_secure_buffer_destroy(ai_secure_buffer_t* buffer) {
    if (!buffer) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_buffers.find(buffer);
    if (it != g_buffers.end()) {
        g_buffers.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Secure buffer destroyed");
    }
}

ai_security_result_t ai_secure_buffer_lock(ai_secure_buffer_t* buffer) {
    if (!buffer || !buffer->data) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    if (buffer->is_locked) {
        return AI_SECURITY_SUCCESS;
    }
    
    if (mlock(buffer->data, buffer->capacity) != 0) {
        return AI_SECURITY_ERROR_HARDWARE_NOT_AVAILABLE;
    }
    
    buffer->is_locked = true;
    ALOGD_IF_DEBUG(g_debug_logging, "Secure buffer locked in memory");
    return AI_SECURITY_SUCCESS;
}

ai_security_result_t ai_secure_buffer_unlock(ai_secure_buffer_t* buffer) {
    if (!buffer || !buffer->data) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    if (!buffer->is_locked) {
        return AI_SECURITY_SUCCESS;
    }
    
    if (munlock(buffer->data, buffer->capacity) != 0) {
        return AI_SECURITY_ERROR_HARDWARE_NOT_AVAILABLE;
    }
    
    buffer->is_locked = false;
    ALOGD_IF_DEBUG(g_debug_logging, "Secure buffer unlocked");
    return AI_SECURITY_SUCCESS;
}

void ai_secure_memwipe(void* ptr, size_t size) {
    if (!ptr || size == 0) {
        return;
    }
    
    // Use volatile to prevent compiler optimization
    volatile unsigned char* p = static_cast<volatile unsigned char*>(ptr);
    for (size_t i = 0; i < size; i++) {
        p[i] = 0;
    }
    
    // Additional security: overwrite with random data
    RAND_bytes(static_cast<unsigned char*>(ptr), size);
    
    // Final wipe with zeros
    for (size_t i = 0; i < size; i++) {
        p[i] = 0;
    }
}

ai_security_result_t ai_random_bytes(uint8_t* buffer, size_t size) {
    if (!buffer || size == 0) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    if (RAND_bytes(buffer, size) != 1) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    
    return AI_SECURITY_SUCCESS;
}

ai_security_result_t ai_hmac_sha256(const uint8_t* key, size_t key_size,
                                   const uint8_t* message, size_t message_size,
                                   uint8_t* hmac, size_t hmac_size) {
    if (!key || !message || !hmac || hmac_size < 32) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    unsigned int len;
    if (!HMAC(EVP_sha256(), key, key_size, message, message_size, hmac, &len)) {
        return AI_SECURITY_ERROR_CRYPTO_FAILED;
    }
    
    return AI_SECURITY_SUCCESS;
}

ai_security_result_t ai_hmac_verify(const uint8_t* key, size_t key_size,
                                   const uint8_t* message, size_t message_size,
                                   const uint8_t* expected_hmac, size_t hmac_size) {
    if (!key || !message || !expected_hmac || hmac_size != 32) {
        return AI_SECURITY_ERROR_INVALID_PARAM;
    }
    
    uint8_t computed_hmac[32];
    ai_security_result_t result = ai_hmac_sha256(key, key_size, message, message_size, 
                                                computed_hmac, sizeof(computed_hmac));
    if (result != AI_SECURITY_SUCCESS) {
        return result;
    }
    
    // Constant-time comparison
    int diff = 0;
    for (size_t i = 0; i < hmac_size; i++) {
        diff |= expected_hmac[i] ^ computed_hmac[i];
    }
    
    ai_secure_memwipe(computed_hmac, sizeof(computed_hmac));
    
    return (diff == 0) ? AI_SECURITY_SUCCESS : AI_SECURITY_ERROR_INVALID_SIGNATURE;
}

bool ai_is_hardware_backed_available(ai_storage_type_t storage_type) {
    return is_hardware_backed_available_impl(storage_type);
}

const char* ai_security_get_error_message(ai_security_result_t result) {
    switch (result) {
        case AI_SECURITY_SUCCESS: return "Success";
        case AI_SECURITY_ERROR_INVALID_PARAM: return "Invalid parameter";
        case AI_SECURITY_ERROR_OUT_OF_MEMORY: return "Out of memory";
        case AI_SECURITY_ERROR_CRYPTO_FAILED: return "Cryptographic operation failed";
        case AI_SECURITY_ERROR_KEY_NOT_FOUND: return "Key not found";
        case AI_SECURITY_ERROR_PERMISSION_DENIED: return "Permission denied";
        case AI_SECURITY_ERROR_INVALID_SIGNATURE: return "Invalid signature";
        case AI_SECURITY_ERROR_HARDWARE_NOT_AVAILABLE: return "Hardware not available";
        case AI_SECURITY_ERROR_KEYSTORE_FAILED: return "Keystore operation failed";
        default: return "Unknown error";
    }
}

void ai_security_set_debug_logging(bool enable) {
    g_debug_logging = enable;
    ALOGD("Security debug logging %s", enable ? "enabled" : "disabled");
}

} // extern "C"
