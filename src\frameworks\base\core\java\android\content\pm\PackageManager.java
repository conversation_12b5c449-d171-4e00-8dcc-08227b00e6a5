/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.content.pm;

/**
 * Class for retrieving various kinds of information related to the application
 * packages that are currently installed on the device.
 */
public abstract class PackageManager {
    /**
     * Permission check result: this is returned by checkPermission()
     * if the permission has been granted to the given package.
     */
    public static final int PERMISSION_GRANTED = 0;

    /**
     * Permission check result: this is returned by checkPermission()
     * if the permission has not been granted to the given package.
     */
    public static final int PERMISSION_DENIED = -1;

    /**
     * Check whether a particular package has been granted a particular
     * permission.
     */
    public abstract int checkPermission(String permName, String pkgName);

    /**
     * Return the name of the package that was used to install the given
     * package.
     */
    public abstract String getInstallerPackageName(String packageName);

    /**
     * Return a "good" intent to launch a front-door activity in a package.
     */
    public abstract Object getLaunchIntentForPackage(String packageName);

    /**
     * Return an array of all of the OPSTR_* permission names.
     */
    public abstract String[] getPackagesForUid(int uid);

    /**
     * Retrieve the textual label associated with this package.
     */
    public abstract CharSequence getApplicationLabel(Object info);

    /**
     * Return the generic icon for all applications.
     */
    public abstract Object getDefaultActivityIcon();

    /**
     * Retrieve overall information about an application package that is
     * installed on the system.
     */
    public abstract Object getApplicationInfo(String packageName, int flags);

    /**
     * Return a List of all packages that are installed on the device.
     */
    public abstract java.util.List<Object> getInstalledPackages(int flags);

    /**
     * Return the name associated with a uid.
     */
    public String getNameForUid(int uid) {
        // Mock implementation
        return "com.example.app";
    }
}
