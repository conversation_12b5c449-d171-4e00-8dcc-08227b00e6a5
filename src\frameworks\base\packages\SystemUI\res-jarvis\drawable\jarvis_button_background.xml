<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_primary_variant" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
    <!-- Focused State -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_primary" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
            <stroke
                android:width="2dp"
                android:color="@color/jarvis_interactive_focused" />
        </shape>
    </item>
    
    <!-- Disabled State -->
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_interactive_disabled" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_primary" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
</selector>
