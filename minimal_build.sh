#!/bin/bash

# Minimal Build Script - Focus on Core Dependencies First
echo "🔧 Minimal Build: Fixing core dependencies first..."

# Create output directory
mkdir -p build/minimal
rm -rf build/minimal/* 2>/dev/null

echo "📦 Step 1: Building core Android framework (minimal set)..."

# Build just the essential classes in the right order
echo "  Building Parcelable interface..."
javac -d build/minimal src/frameworks/base/core/java/android/os/Parcelable.java 2>&1 | tee build_minimal.log

echo "  Building Parcel class..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/Parcel.java 2>&1 | tee -a build_minimal.log

echo "  Building Bundle class..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/Bundle.java 2>&1 | tee -a build_minimal.log

echo "  Building IBinder interface..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/IBinder.java 2>&1 | tee -a build_minimal.log

echo "  Building IInterface interface..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/IInterface.java 2>&1 | tee -a build_minimal.log

echo "  Building RemoteException..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/RemoteException.java 2>&1 | tee -a build_minimal.log

echo "  Building Binder class..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/os/Binder.java 2>&1 | tee -a build_minimal.log

echo "  Building Uri class..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/net/Uri.java 2>&1 | tee -a build_minimal.log

echo "📦 Step 2: Building AI framework classes..."

echo "  Building ActionResult..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/ai/ActionResult.java 2>&1 | tee -a build_minimal.log

echo "  Building ContextSnapshot..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/ai/ContextSnapshot.java 2>&1 | tee -a build_minimal.log

echo "  Building ActionRequest..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/ai/ActionRequest.java 2>&1 | tee -a build_minimal.log

echo "  Building ExecutionStatus..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/ai/ExecutionStatus.java 2>&1 | tee -a build_minimal.log

echo "  Building IActionProvider..."
javac -cp build/minimal -d build/minimal src/frameworks/base/core/java/android/ai/IActionProvider.java 2>&1 | tee -a build_minimal.log

echo "📊 Build Summary:"
echo "=================="

# Count compiled classes
compiled_classes=$(find build/minimal -name "*.class" 2>/dev/null | wc -l)
echo "Compiled classes: $compiled_classes"

if [ $compiled_classes -gt 10 ]; then
    echo "✅ MINIMAL BUILD SUCCESSFUL: Core dependencies compiled!"
    echo "📁 Compiled classes in: build/minimal"
    echo "📋 Build log: build_minimal.log"
    echo ""
    echo "🎯 Next steps:"
    echo "  1. Use build/minimal as classpath for remaining compilation"
    echo "  2. Gradually add more complex classes"
    echo "  3. Focus on fixing remaining dependency issues"
else
    echo "❌ MINIMAL BUILD FAILED: Core dependencies still have issues"
    echo "📋 Check build_minimal.log for errors"
fi
