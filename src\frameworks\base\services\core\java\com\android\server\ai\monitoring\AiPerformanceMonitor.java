/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.monitoring;

import android.content.Context;
import android.os.SystemClock;
import android.util.Slog;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Comprehensive performance monitoring system for Jarvis AI services.
 * 
 * Monitors and optimizes:
 * - Task planning performance
 * - Execution efficiency
 * - Resource utilization
 * - API response times
 * - System health metrics
 */
public class AiPerformanceMonitor {
    private static final String TAG = "AiPerformanceMonitor";
    private static final boolean DEBUG = true;
    
    // Monitoring intervals
    private static final long METRICS_COLLECTION_INTERVAL_MS = 30000; // 30 seconds
    private static final long PERFORMANCE_REPORT_INTERVAL_MS = 300000; // 5 minutes
    private static final long OPTIMIZATION_CHECK_INTERVAL_MS = 600000; // 10 minutes
    
    // Performance thresholds
    private static final long PLANNING_TIME_THRESHOLD_MS = 5000;
    private static final long EXECUTION_TIME_THRESHOLD_MS = 30000;
    private static final long API_RESPONSE_THRESHOLD_MS = 10000;
    private static final double CPU_USAGE_THRESHOLD = 0.8;
    private static final double MEMORY_USAGE_THRESHOLD = 0.9;
    
    private final Context mContext;
    private final ScheduledExecutorService mScheduledExecutor;
    
    // Performance metrics
    private final ConcurrentHashMap<String, PerformanceMetric> mMetrics = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> mCounters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> mTimestamps = new ConcurrentHashMap<>();
    
    // System resource tracking
    private final SystemResourceTracker mResourceTracker;
    private final ApiPerformanceTracker mApiTracker;
    private final TaskPerformanceTracker mTaskTracker;
    
    // Performance optimization
    private final PerformanceOptimizer mOptimizer;
    
    public AiPerformanceMonitor(Context context) {
        mContext = context;
        mScheduledExecutor = Executors.newScheduledThreadPool(3);
        
        mResourceTracker = new SystemResourceTracker();
        mApiTracker = new ApiPerformanceTracker();
        mTaskTracker = new TaskPerformanceTracker();
        mOptimizer = new PerformanceOptimizer();
        
        initializeMetrics();
        startMonitoring();
        
        if (DEBUG) Slog.d(TAG, "AiPerformanceMonitor initialized");
    }
    
    /**
     * Initialize performance metrics
     */
    private void initializeMetrics() {
        // Planning metrics
        mMetrics.put("planning_time", new PerformanceMetric("planning_time", "ms"));
        mMetrics.put("planning_success_rate", new PerformanceMetric("planning_success_rate", "%"));
        mMetrics.put("planning_requests_per_minute", new PerformanceMetric("planning_requests_per_minute", "count"));
        
        // Execution metrics
        mMetrics.put("execution_time", new PerformanceMetric("execution_time", "ms"));
        mMetrics.put("execution_success_rate", new PerformanceMetric("execution_success_rate", "%"));
        mMetrics.put("step_failure_rate", new PerformanceMetric("step_failure_rate", "%"));
        
        // API metrics
        mMetrics.put("gemini_api_response_time", new PerformanceMetric("gemini_api_response_time", "ms"));
        mMetrics.put("gemini_api_success_rate", new PerformanceMetric("gemini_api_success_rate", "%"));
        mMetrics.put("api_rate_limit_hits", new PerformanceMetric("api_rate_limit_hits", "count"));
        
        // System metrics
        mMetrics.put("cpu_usage", new PerformanceMetric("cpu_usage", "%"));
        mMetrics.put("memory_usage", new PerformanceMetric("memory_usage", "%"));
        mMetrics.put("network_latency", new PerformanceMetric("network_latency", "ms"));
        
        // Initialize counters
        mCounters.put("total_planning_requests", new AtomicLong(0));
        mCounters.put("successful_planning_requests", new AtomicLong(0));
        mCounters.put("total_execution_requests", new AtomicLong(0));
        mCounters.put("successful_execution_requests", new AtomicLong(0));
        mCounters.put("total_api_calls", new AtomicLong(0));
        mCounters.put("successful_api_calls", new AtomicLong(0));
    }
    
    /**
     * Start performance monitoring
     */
    private void startMonitoring() {
        // Schedule metrics collection
        mScheduledExecutor.scheduleAtFixedRate(
            this::collectMetrics,
            0,
            METRICS_COLLECTION_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        );
        
        // Schedule performance reporting
        mScheduledExecutor.scheduleAtFixedRate(
            this::generatePerformanceReport,
            PERFORMANCE_REPORT_INTERVAL_MS,
            PERFORMANCE_REPORT_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        );
        
        // Schedule optimization checks
        mScheduledExecutor.scheduleAtFixedRate(
            this::checkForOptimizations,
            OPTIMIZATION_CHECK_INTERVAL_MS,
            OPTIMIZATION_CHECK_INTERVAL_MS,
            TimeUnit.MILLISECONDS
        );
    }
    
    /**
     * Record task planning performance
     */
    public void recordPlanningPerformance(String taskId, long planningTime, boolean success) {
        mCounters.get("total_planning_requests").incrementAndGet();
        if (success) {
            mCounters.get("successful_planning_requests").incrementAndGet();
        }
        
        mMetrics.get("planning_time").addSample(planningTime);
        
        if (planningTime > PLANNING_TIME_THRESHOLD_MS) {
            Slog.w(TAG, "Slow planning detected: " + taskId + " took " + planningTime + "ms");
            mOptimizer.recordSlowPlanning(taskId, planningTime);
        }
        
        mTaskTracker.recordPlanningMetrics(taskId, planningTime, success);
    }
    
    /**
     * Record task execution performance
     */
    public void recordExecutionPerformance(String taskId, long executionTime, boolean success, 
                                         int totalSteps, int failedSteps) {
        mCounters.get("total_execution_requests").incrementAndGet();
        if (success) {
            mCounters.get("successful_execution_requests").incrementAndGet();
        }
        
        mMetrics.get("execution_time").addSample(executionTime);
        
        if (failedSteps > 0) {
            double failureRate = (double) failedSteps / totalSteps;
            mMetrics.get("step_failure_rate").addSample(failureRate * 100);
        }
        
        if (executionTime > EXECUTION_TIME_THRESHOLD_MS) {
            Slog.w(TAG, "Slow execution detected: " + taskId + " took " + executionTime + "ms");
            mOptimizer.recordSlowExecution(taskId, executionTime);
        }
        
        mTaskTracker.recordExecutionMetrics(taskId, executionTime, success, totalSteps, failedSteps);
    }
    
    /**
     * Record API performance
     */
    public void recordApiPerformance(String endpoint, long responseTime, boolean success) {
        mCounters.get("total_api_calls").incrementAndGet();
        if (success) {
            mCounters.get("successful_api_calls").incrementAndGet();
        }
        
        mMetrics.get("gemini_api_response_time").addSample(responseTime);
        
        if (responseTime > API_RESPONSE_THRESHOLD_MS) {
            Slog.w(TAG, "Slow API response: " + endpoint + " took " + responseTime + "ms");
            mOptimizer.recordSlowApiCall(endpoint, responseTime);
        }
        
        mApiTracker.recordApiMetrics(endpoint, responseTime, success);
    }
    
    /**
     * Record system resource usage
     */
    public void recordResourceUsage(double cpuUsage, double memoryUsage, long networkLatency) {
        mMetrics.get("cpu_usage").addSample(cpuUsage * 100);
        mMetrics.get("memory_usage").addSample(memoryUsage * 100);
        mMetrics.get("network_latency").addSample(networkLatency);
        
        if (cpuUsage > CPU_USAGE_THRESHOLD) {
            Slog.w(TAG, "High CPU usage detected: " + (cpuUsage * 100) + "%");
            mOptimizer.recordHighResourceUsage("cpu", cpuUsage);
        }
        
        if (memoryUsage > MEMORY_USAGE_THRESHOLD) {
            Slog.w(TAG, "High memory usage detected: " + (memoryUsage * 100) + "%");
            mOptimizer.recordHighResourceUsage("memory", memoryUsage);
        }
        
        mResourceTracker.recordResourceMetrics(cpuUsage, memoryUsage, networkLatency);
    }
    
    /**
     * Collect current performance metrics
     */
    private void collectMetrics() {
        try {
            // Update success rates
            updateSuccessRates();
            
            // Collect system resource metrics
            mResourceTracker.collectCurrentMetrics();
            
            // Update request rates
            updateRequestRates();
            
            if (DEBUG) Slog.d(TAG, "Metrics collection completed");
            
        } catch (Exception e) {
            Slog.e(TAG, "Error collecting metrics", e);
        }
    }
    
    /**
     * Update success rate metrics
     */
    private void updateSuccessRates() {
        // Planning success rate
        long totalPlanning = mCounters.get("total_planning_requests").get();
        long successfulPlanning = mCounters.get("successful_planning_requests").get();
        if (totalPlanning > 0) {
            double planningSuccessRate = (double) successfulPlanning / totalPlanning * 100;
            mMetrics.get("planning_success_rate").addSample(planningSuccessRate);
        }
        
        // Execution success rate
        long totalExecution = mCounters.get("total_execution_requests").get();
        long successfulExecution = mCounters.get("successful_execution_requests").get();
        if (totalExecution > 0) {
            double executionSuccessRate = (double) successfulExecution / totalExecution * 100;
            mMetrics.get("execution_success_rate").addSample(executionSuccessRate);
        }
        
        // API success rate
        long totalApi = mCounters.get("total_api_calls").get();
        long successfulApi = mCounters.get("successful_api_calls").get();
        if (totalApi > 0) {
            double apiSuccessRate = (double) successfulApi / totalApi * 100;
            mMetrics.get("gemini_api_success_rate").addSample(apiSuccessRate);
        }
    }
    
    /**
     * Update request rate metrics
     */
    private void updateRequestRates() {
        long currentTime = SystemClock.elapsedRealtime();
        Long lastUpdateTime = mTimestamps.get("last_rate_update");
        
        if (lastUpdateTime != null) {
            long timeDiff = currentTime - lastUpdateTime;
            if (timeDiff > 0) {
                // Calculate requests per minute
                long planningRequests = mCounters.get("total_planning_requests").get();
                Long lastPlanningCount = mTimestamps.get("last_planning_count");
                
                if (lastPlanningCount != null) {
                    long requestDiff = planningRequests - lastPlanningCount;
                    double requestsPerMinute = (double) requestDiff / timeDiff * 60000;
                    mMetrics.get("planning_requests_per_minute").addSample(requestsPerMinute);
                }
                
                mTimestamps.put("last_planning_count", planningRequests);
            }
        }
        
        mTimestamps.put("last_rate_update", currentTime);
    }
    
    /**
     * Generate performance report
     */
    private void generatePerformanceReport() {
        try {
            PerformanceReport report = new PerformanceReport();
            report.timestamp = System.currentTimeMillis();
            report.metrics = new HashMap<>();
            
            // Collect all metrics
            for (Map.Entry<String, PerformanceMetric> entry : mMetrics.entrySet()) {
                PerformanceMetric metric = entry.getValue();
                MetricSummary summary = new MetricSummary();
                summary.average = metric.getAverage();
                summary.min = metric.getMin();
                summary.max = metric.getMax();
                summary.sampleCount = metric.getSampleCount();
                
                report.metrics.put(entry.getKey(), summary);
            }
            
            // Log performance summary
            logPerformanceSummary(report);
            
            // Check for performance issues
            checkPerformanceIssues(report);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error generating performance report", e);
        }
    }
    
    /**
     * Log performance summary
     */
    private void logPerformanceSummary(PerformanceReport report) {
        if (DEBUG) {
            Slog.i(TAG, "=== PERFORMANCE REPORT ===");
            Slog.i(TAG, "Planning avg time: " + 
                   report.metrics.get("planning_time").average + "ms");
            Slog.i(TAG, "Execution avg time: " + 
                   report.metrics.get("execution_time").average + "ms");
            Slog.i(TAG, "API avg response time: " + 
                   report.metrics.get("gemini_api_response_time").average + "ms");
            Slog.i(TAG, "Planning success rate: " + 
                   report.metrics.get("planning_success_rate").average + "%");
            Slog.i(TAG, "Execution success rate: " + 
                   report.metrics.get("execution_success_rate").average + "%");
            Slog.i(TAG, "========================");
        }
    }
    
    /**
     * Check for performance issues
     */
    private void checkPerformanceIssues(PerformanceReport report) {
        // Check planning performance
        MetricSummary planningTime = report.metrics.get("planning_time");
        if (planningTime != null && planningTime.average > PLANNING_TIME_THRESHOLD_MS) {
            Slog.w(TAG, "Performance issue: Average planning time is " + 
                   planningTime.average + "ms (threshold: " + PLANNING_TIME_THRESHOLD_MS + "ms)");
            mOptimizer.triggerPlanningOptimization();
        }
        
        // Check execution performance
        MetricSummary executionTime = report.metrics.get("execution_time");
        if (executionTime != null && executionTime.average > EXECUTION_TIME_THRESHOLD_MS) {
            Slog.w(TAG, "Performance issue: Average execution time is " + 
                   executionTime.average + "ms (threshold: " + EXECUTION_TIME_THRESHOLD_MS + "ms)");
            mOptimizer.triggerExecutionOptimization();
        }
        
        // Check API performance
        MetricSummary apiTime = report.metrics.get("gemini_api_response_time");
        if (apiTime != null && apiTime.average > API_RESPONSE_THRESHOLD_MS) {
            Slog.w(TAG, "Performance issue: Average API response time is " + 
                   apiTime.average + "ms (threshold: " + API_RESPONSE_THRESHOLD_MS + "ms)");
            mOptimizer.triggerApiOptimization();
        }
    }
    
    /**
     * Check for optimization opportunities
     */
    private void checkForOptimizations() {
        try {
            mOptimizer.analyzeOptimizationOpportunities();
            
        } catch (Exception e) {
            Slog.e(TAG, "Error checking for optimizations", e);
        }
    }
    
    /**
     * Get current performance metrics
     */
    public Map<String, MetricSummary> getCurrentMetrics() {
        Map<String, MetricSummary> currentMetrics = new HashMap<>();
        
        for (Map.Entry<String, PerformanceMetric> entry : mMetrics.entrySet()) {
            PerformanceMetric metric = entry.getValue();
            MetricSummary summary = new MetricSummary();
            summary.average = metric.getAverage();
            summary.min = metric.getMin();
            summary.max = metric.getMax();
            summary.sampleCount = metric.getSampleCount();
            
            currentMetrics.put(entry.getKey(), summary);
        }
        
        return currentMetrics;
    }
    
    /**
     * Shutdown the performance monitor
     */
    public void shutdown() {
        if (mScheduledExecutor != null && !mScheduledExecutor.isShutdown()) {
            mScheduledExecutor.shutdown();
            try {
                if (!mScheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    mScheduledExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                mScheduledExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (DEBUG) Slog.d(TAG, "AiPerformanceMonitor shutdown");
    }
    
    // Helper classes for performance tracking
    
    private static class PerformanceMetric {
        private final String name;
        private final String unit;
        private double sum = 0;
        private double min = Double.MAX_VALUE;
        private double max = Double.MIN_VALUE;
        private int sampleCount = 0;
        
        public PerformanceMetric(String name, String unit) {
            this.name = name;
            this.unit = unit;
        }
        
        public synchronized void addSample(double value) {
            sum += value;
            min = Math.min(min, value);
            max = Math.max(max, value);
            sampleCount++;
        }
        
        public synchronized double getAverage() {
            return sampleCount > 0 ? sum / sampleCount : 0;
        }
        
        public synchronized double getMin() {
            return sampleCount > 0 ? min : 0;
        }
        
        public synchronized double getMax() {
            return sampleCount > 0 ? max : 0;
        }
        
        public synchronized int getSampleCount() {
            return sampleCount;
        }
    }
    
    public static class MetricSummary {
        public double average;
        public double min;
        public double max;
        public int sampleCount;
    }
    
    private static class PerformanceReport {
        public long timestamp;
        public Map<String, MetricSummary> metrics;
    }
    
    // Placeholder classes for tracking components
    
    private static class SystemResourceTracker {
        public void recordResourceMetrics(double cpuUsage, double memoryUsage, long networkLatency) {
            // Implementation for system resource tracking
        }
        
        public void collectCurrentMetrics() {
            // Implementation for collecting current system metrics
        }
    }
    
    private static class ApiPerformanceTracker {
        public void recordApiMetrics(String endpoint, long responseTime, boolean success) {
            // Implementation for API performance tracking
        }
    }
    
    private static class TaskPerformanceTracker {
        public void recordPlanningMetrics(String taskId, long planningTime, boolean success) {
            // Implementation for task planning performance tracking
        }
        
        public void recordExecutionMetrics(String taskId, long executionTime, boolean success, 
                                         int totalSteps, int failedSteps) {
            // Implementation for task execution performance tracking
        }
    }
    
    private static class PerformanceOptimizer {
        public void recordSlowPlanning(String taskId, long planningTime) {
            // Implementation for recording slow planning instances
        }
        
        public void recordSlowExecution(String taskId, long executionTime) {
            // Implementation for recording slow execution instances
        }
        
        public void recordSlowApiCall(String endpoint, long responseTime) {
            // Implementation for recording slow API calls
        }
        
        public void recordHighResourceUsage(String resourceType, double usage) {
            // Implementation for recording high resource usage
        }
        
        public void triggerPlanningOptimization() {
            // Implementation for planning optimization
        }
        
        public void triggerExecutionOptimization() {
            // Implementation for execution optimization
        }
        
        public void triggerApiOptimization() {
            // Implementation for API optimization
        }
        
        public void analyzeOptimizationOpportunities() {
            // Implementation for analyzing optimization opportunities
        }
    }
}
