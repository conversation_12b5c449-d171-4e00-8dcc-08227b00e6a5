/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.systemui.R;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * RecyclerView adapter for displaying AI suggestions.
 * 
 * Handles different suggestion types with appropriate styling,
 * interaction handling, and feedback collection.
 */
public class SuggestionAdapter extends RecyclerView.Adapter<SuggestionAdapter.SuggestionViewHolder> {
    
    private static final int VIEW_TYPE_NORMAL = 1;
    private static final int VIEW_TYPE_HIGH_PRIORITY = 2;
    private static final int VIEW_TYPE_ACTION = 3;
    
    private List<AiSuggestion> mSuggestions;
    private LayoutInflater mInflater;
    private SuggestionClickListener mClickListener;
    
    public interface SuggestionClickListener {
        void onSuggestionClick(AiSuggestion suggestion);
        void onSuggestionDismiss(AiSuggestion suggestion);
        void onSuggestionFeedback(AiSuggestion suggestion, boolean helpful);
    }
    
    public SuggestionAdapter(List<AiSuggestion> suggestions) {
        mSuggestions = suggestions;
    }
    
    public void setSuggestionClickListener(SuggestionClickListener listener) {
        mClickListener = listener;
    }
    
    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        mInflater = LayoutInflater.from(recyclerView.getContext());
    }
    
    @Override
    public int getItemViewType(int position) {
        AiSuggestion suggestion = mSuggestions.get(position);
        
        if (suggestion.getPriority() == AiSuggestion.Priority.URGENT || 
            suggestion.getPriority() == AiSuggestion.Priority.HIGH) {
            return VIEW_TYPE_HIGH_PRIORITY;
        } else if (suggestion.getType() == AiSuggestion.SuggestionType.ACTION ||
                   suggestion.getType() == AiSuggestion.SuggestionType.APP_LAUNCH) {
            return VIEW_TYPE_ACTION;
        } else {
            return VIEW_TYPE_NORMAL;
        }
    }
    
    @NonNull
    @Override
    public SuggestionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        
        switch (viewType) {
            case VIEW_TYPE_HIGH_PRIORITY:
                view = mInflater.inflate(R.layout.jarvis_suggestion_high_priority, parent, false);
                break;
            case VIEW_TYPE_ACTION:
                view = mInflater.inflate(R.layout.jarvis_suggestion_action, parent, false);
                break;
            case VIEW_TYPE_NORMAL:
            default:
                view = mInflater.inflate(R.layout.jarvis_suggestion_normal, parent, false);
                break;
        }
        
        return new SuggestionViewHolder(view, viewType);
    }
    
    @Override
    public void onBindViewHolder(@NonNull SuggestionViewHolder holder, int position) {
        AiSuggestion suggestion = mSuggestions.get(position);
        holder.bind(suggestion);
    }
    
    @Override
    public int getItemCount() {
        return mSuggestions.size();
    }
    
    public class SuggestionViewHolder extends RecyclerView.ViewHolder {
        private TextView mTitleText;
        private TextView mDescriptionText;
        private TextView mActionText;
        private TextView mTimeText;
        private ImageView mIconView;
        private ImageView mPriorityIndicator;
        private ImageButton mDismissButton;
        private ImageButton mThumbsUpButton;
        private ImageButton mThumbsDownButton;
        private View mSuggestionCard;
        private View mFeedbackContainer;
        
        private int mViewType;
        
        public SuggestionViewHolder(@NonNull View itemView, int viewType) {
            super(itemView);
            mViewType = viewType;
            
            mTitleText = itemView.findViewById(R.id.suggestion_title);
            mDescriptionText = itemView.findViewById(R.id.suggestion_description);
            mActionText = itemView.findViewById(R.id.suggestion_action_text);
            mTimeText = itemView.findViewById(R.id.suggestion_time);
            mIconView = itemView.findViewById(R.id.suggestion_icon);
            mPriorityIndicator = itemView.findViewById(R.id.priority_indicator);
            mDismissButton = itemView.findViewById(R.id.dismiss_button);
            mThumbsUpButton = itemView.findViewById(R.id.thumbs_up_button);
            mThumbsDownButton = itemView.findViewById(R.id.thumbs_down_button);
            mSuggestionCard = itemView.findViewById(R.id.suggestion_card);
            mFeedbackContainer = itemView.findViewById(R.id.feedback_container);
        }
        
        public void bind(AiSuggestion suggestion) {
            // Set basic content
            mTitleText.setText(suggestion.getTitle());
            
            if (mDescriptionText != null && suggestion.getDescription() != null) {
                mDescriptionText.setText(suggestion.getDescription());
                mDescriptionText.setVisibility(suggestion.getDescription().isEmpty() ? View.GONE : View.VISIBLE);
            }
            
            if (mActionText != null && suggestion.getActionText() != null) {
                mActionText.setText(suggestion.getActionText());
                mActionText.setVisibility(suggestion.getActionText().isEmpty() ? View.GONE : View.VISIBLE);
            }
            
            // Set time
            if (mTimeText != null) {
                mTimeText.setText(formatTime(suggestion.getCreatedTime()));
            }
            
            // Set icon
            configureIcon(suggestion);
            
            // Set priority indicator
            configurePriorityIndicator(suggestion);
            
            // Configure card appearance
            configureCardAppearance(suggestion);
            
            // Set up click handlers
            setupClickHandlers(suggestion);
            
            // Configure feedback buttons
            configureFeedbackButtons(suggestion);
        }
        
        private void configureIcon(AiSuggestion suggestion) {
            if (mIconView == null) return;
            
            Context context = itemView.getContext();
            
            // Set icon based on suggestion type
            int iconResource = getIconForSuggestionType(suggestion.getType());
            mIconView.setImageResource(iconResource);
            
            // Set icon color
            if (suggestion.getIconColor() != 0) {
                mIconView.setColorFilter(suggestion.getIconColor());
            }
        }
        
        private void configurePriorityIndicator(AiSuggestion suggestion) {
            if (mPriorityIndicator == null) return;
            
            switch (suggestion.getPriority()) {
                case URGENT:
                    mPriorityIndicator.setVisibility(View.VISIBLE);
                    mPriorityIndicator.setImageResource(R.drawable.ic_priority_urgent);
                    mPriorityIndicator.setColorFilter(Color.RED);
                    break;
                case HIGH:
                    mPriorityIndicator.setVisibility(View.VISIBLE);
                    mPriorityIndicator.setImageResource(R.drawable.ic_priority_high);
                    mPriorityIndicator.setColorFilter(Color.parseColor("#FF9800"));
                    break;
                case NORMAL:
                case LOW:
                default:
                    mPriorityIndicator.setVisibility(View.GONE);
                    break;
            }
        }
        
        private void configureCardAppearance(AiSuggestion suggestion) {
            if (mSuggestionCard == null) return;
            
            Context context = itemView.getContext();
            
            // Create background drawable
            GradientDrawable background = new GradientDrawable();
            background.setShape(GradientDrawable.RECTANGLE);
            background.setCornerRadius(16f);
            
            // Set background color based on suggestion type and priority
            String backgroundColor = suggestion.getBackgroundColor();
            if (backgroundColor != null && !backgroundColor.isEmpty()) {
                try {
                    background.setColor(Color.parseColor(backgroundColor));
                } catch (IllegalArgumentException e) {
                    background.setColor(context.getColor(R.color.jarvis_suggestion_background));
                }
            } else {
                background.setColor(getBackgroundColorForType(suggestion.getType(), context));
            }
            
            // Add stroke for high priority suggestions
            if (suggestion.getPriority() == AiSuggestion.Priority.HIGH ||
                suggestion.getPriority() == AiSuggestion.Priority.URGENT) {
                background.setStroke(4, suggestion.getPriority() == AiSuggestion.Priority.URGENT ? 
                    Color.RED : Color.parseColor("#FF9800"));
            }
            
            mSuggestionCard.setBackground(background);
            
            // Set elevation based on priority
            float elevation = getElevationForPriority(suggestion.getPriority());
            mSuggestionCard.setElevation(elevation);
        }
        
        private void setupClickHandlers(AiSuggestion suggestion) {
            // Main suggestion click
            itemView.setOnClickListener(v -> {
                if (mClickListener != null) {
                    mClickListener.onSuggestionClick(suggestion);
                }
            });
            
            // Dismiss button
            if (mDismissButton != null) {
                mDismissButton.setOnClickListener(v -> {
                    if (mClickListener != null) {
                        mClickListener.onSuggestionDismiss(suggestion);
                    }
                });
            }
        }
        
        private void configureFeedbackButtons(AiSuggestion suggestion) {
            if (mFeedbackContainer == null) return;
            
            // Show feedback buttons only for certain types
            boolean showFeedback = shouldShowFeedback(suggestion);
            mFeedbackContainer.setVisibility(showFeedback ? View.VISIBLE : View.GONE);
            
            if (!showFeedback) return;
            
            // Configure thumbs up button
            if (mThumbsUpButton != null) {
                mThumbsUpButton.setOnClickListener(v -> {
                    if (mClickListener != null) {
                        mClickListener.onSuggestionFeedback(suggestion, true);
                    }
                    updateFeedbackButtons(true, false);
                });
            }
            
            // Configure thumbs down button
            if (mThumbsDownButton != null) {
                mThumbsDownButton.setOnClickListener(v -> {
                    if (mClickListener != null) {
                        mClickListener.onSuggestionFeedback(suggestion, false);
                    }
                    updateFeedbackButtons(false, true);
                });
            }
            
            // Update button states if feedback already given
            if (suggestion.hasFeedback()) {
                boolean hasPositive = suggestion.getPositiveFeedback() > 0;
                boolean hasNegative = suggestion.getNegativeFeedback() > 0;
                updateFeedbackButtons(hasPositive, hasNegative);
            }
        }
        
        private void updateFeedbackButtons(boolean thumbsUpPressed, boolean thumbsDownPressed) {
            if (mThumbsUpButton != null) {
                mThumbsUpButton.setSelected(thumbsUpPressed);
                mThumbsUpButton.setEnabled(!thumbsUpPressed && !thumbsDownPressed);
            }
            
            if (mThumbsDownButton != null) {
                mThumbsDownButton.setSelected(thumbsDownPressed);
                mThumbsDownButton.setEnabled(!thumbsUpPressed && !thumbsDownPressed);
            }
        }
        
        private boolean shouldShowFeedback(AiSuggestion suggestion) {
            // Show feedback for information and routine suggestions
            return suggestion.getType() == AiSuggestion.SuggestionType.INFORMATION ||
                   suggestion.getType() == AiSuggestion.SuggestionType.ROUTINE ||
                   suggestion.getType() == AiSuggestion.SuggestionType.CONTEXT_AWARE;
        }
        
        private int getIconForSuggestionType(AiSuggestion.SuggestionType type) {
            switch (type) {
                case ACTION:
                    return R.drawable.ic_suggestion_action;
                case APP_LAUNCH:
                    return R.drawable.ic_suggestion_app;
                case SETTING_CHANGE:
                    return R.drawable.ic_suggestion_settings;
                case REMINDER:
                    return R.drawable.ic_suggestion_reminder;
                case INFORMATION:
                    return R.drawable.ic_suggestion_info;
                case ROUTINE:
                    return R.drawable.ic_suggestion_routine;
                case SHORTCUT:
                    return R.drawable.ic_suggestion_shortcut;
                case CONTEXT_AWARE:
                    return R.drawable.ic_suggestion_context;
                default:
                    return R.drawable.ic_suggestion_default;
            }
        }
        
        private int getBackgroundColorForType(AiSuggestion.SuggestionType type, Context context) {
            switch (type) {
                case ACTION:
                    return context.getColor(R.color.jarvis_suggestion_action_bg);
                case APP_LAUNCH:
                    return context.getColor(R.color.jarvis_suggestion_app_bg);
                case SETTING_CHANGE:
                    return context.getColor(R.color.jarvis_suggestion_settings_bg);
                case REMINDER:
                    return context.getColor(R.color.jarvis_suggestion_reminder_bg);
                case INFORMATION:
                    return context.getColor(R.color.jarvis_suggestion_info_bg);
                case ROUTINE:
                    return context.getColor(R.color.jarvis_suggestion_routine_bg);
                case SHORTCUT:
                    return context.getColor(R.color.jarvis_suggestion_shortcut_bg);
                case CONTEXT_AWARE:
                    return context.getColor(R.color.jarvis_suggestion_context_bg);
                default:
                    return context.getColor(R.color.jarvis_suggestion_background);
            }
        }
        
        private float getElevationForPriority(AiSuggestion.Priority priority) {
            switch (priority) {
                case URGENT:
                    return 12f;
                case HIGH:
                    return 8f;
                case NORMAL:
                    return 4f;
                case LOW:
                default:
                    return 2f;
            }
        }
        
        private String formatTime(long timestamp) {
            long now = System.currentTimeMillis();
            long diff = now - timestamp;
            
            if (diff < 60 * 1000) { // Less than 1 minute
                return "Just now";
            } else if (diff < 60 * 60 * 1000) { // Less than 1 hour
                int minutes = (int) (diff / (60 * 1000));
                return minutes + "m ago";
            } else if (diff < 24 * 60 * 60 * 1000) { // Less than 1 day
                int hours = (int) (diff / (60 * 60 * 1000));
                return hours + "h ago";
            } else {
                SimpleDateFormat formatter = new SimpleDateFormat("MMM dd", Locale.getDefault());
                return formatter.format(new Date(timestamp));
            }
        }
    }
    
    // Helper methods for adapter management
    public void updateSuggestion(int position, AiSuggestion suggestion) {
        if (position >= 0 && position < mSuggestions.size()) {
            mSuggestions.set(position, suggestion);
            notifyItemChanged(position);
        }
    }
    
    public void removeSuggestion(int position) {
        if (position >= 0 && position < mSuggestions.size()) {
            mSuggestions.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    public void addSuggestion(AiSuggestion suggestion) {
        mSuggestions.add(suggestion);
        notifyItemInserted(mSuggestions.size() - 1);
    }
    
    public AiSuggestion getSuggestion(int position) {
        if (position >= 0 && position < mSuggestions.size()) {
            return mSuggestions.get(position);
        }
        return null;
    }
    
    public int findSuggestionById(String suggestionId) {
        for (int i = 0; i < mSuggestions.size(); i++) {
            if (mSuggestions.get(i).getId().equals(suggestionId)) {
                return i;
            }
        }
        return -1;
    }
}
