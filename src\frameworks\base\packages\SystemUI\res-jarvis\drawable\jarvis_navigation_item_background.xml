<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Selected State -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Background -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/jarvis_nav_selected" />
                    <corners android:radius="@dimen/jarvis_corner_radius_lg" />
                </shape>
            </item>
            <!-- Glow Effect -->
            <item android:top="2dp" android:bottom="2dp" android:left="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:type="radial"
                        android:gradientRadius="24dp"
                        android:centerColor="#40FFFFFF"
                        android:endColor="#00FFFFFF" />
                    <corners android:radius="@dimen/jarvis_corner_radius_md" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_interactive_pressed" />
            <corners android:radius="@dimen/jarvis_corner_radius_lg" />
        </shape>
    </item>
    
    <!-- Focused State -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_interactive_focused" />
            <corners android:radius="@dimen/jarvis_corner_radius_lg" />
            <stroke
                android:width="1dp"
                android:color="@color/jarvis_nav_indicator" />
        </shape>
    </item>
    
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="@dimen/jarvis_corner_radius_lg" />
        </shape>
    </item>
    
</selector>
