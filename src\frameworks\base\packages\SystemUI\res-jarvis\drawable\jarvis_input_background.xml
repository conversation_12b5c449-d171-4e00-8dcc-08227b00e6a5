<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Focused State -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_surface" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
            <stroke
                android:width="2dp"
                android:color="@color/jarvis_primary" />
        </shape>
    </item>
    
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_surface" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
            <stroke
                android:width="1dp"
                android:color="@color/jarvis_card_border" />
        </shape>
    </item>
    
</selector>
