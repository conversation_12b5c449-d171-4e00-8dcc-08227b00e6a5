/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.ai.ContextSnapshot;
import android.ai.PlanResult;
import android.ai.TaskPlan;
import android.ai.TaskStep;
import android.ai.ValidationResult;
import android.content.Context;
import android.os.Bundle;
import android.util.Slog;

import com.android.server.ai.execution.ActionRegistry;
import com.android.server.ai.gemini.GeminiAPIClient;
import com.android.server.ai.gemini.GeminiResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Task planning engine that uses Gemini API for natural language goal parsing
 * and task decomposition into executable steps.
 */
public class TaskPlanner {
    private static final String TAG = "TaskPlanner";
    private static final boolean DEBUG = true;
    
    // Planning configuration
    private static final int MAX_PLANNING_TIME_MS = 30000;
    private static final int MAX_STEPS_PER_PLAN = 20;
    private static final double MIN_CONFIDENCE_THRESHOLD = 0.7;
    
    private final Context mContext;
    private final GeminiAPIClient mGeminiClient;
    private final ActionRegistry mActionRegistry;
    private boolean mAdvancedPlanningEnabled = false;
    
    public TaskPlanner(Context context, GeminiAPIClient geminiClient, ActionRegistry actionRegistry) {
        mContext = context;
        mGeminiClient = geminiClient;
        mActionRegistry = actionRegistry;
        
        if (DEBUG) Slog.d(TAG, "TaskPlanner initialized");
    }
    
    /**
     * Enable advanced planning capabilities (called after boot complete)
     */
    public void enableAdvancedPlanning() {
        mAdvancedPlanningEnabled = true;
        if (DEBUG) Slog.d(TAG, "Advanced planning enabled");
    }
    
    /**
     * Plan a task from natural language goal
     */
    public PlanResult planTask(String naturalLanguageGoal, ContextSnapshot context, String packageName) {
        if (DEBUG) Slog.d(TAG, "Planning task: " + naturalLanguageGoal + " for package: " + packageName);
        
        try {
            // Validate input
            if (naturalLanguageGoal == null || naturalLanguageGoal.trim().isEmpty()) {
                return createErrorResult("Goal cannot be empty");
            }
            
            // Build context string for API
            String contextString = buildContextString(context);
            
            // Call Gemini API for task planning
            CompletableFuture<GeminiResponse> future = mGeminiClient.generateTaskPlan(
                    naturalLanguageGoal, contextString, packageName);
            
            GeminiResponse response = future.get(MAX_PLANNING_TIME_MS, TimeUnit.MILLISECONDS);
            
            if (!response.isSuccess()) {
                String errorMsg = response.getErrorMessage();
                Slog.e(TAG, "Gemini API error: " + errorMsg);
                return createErrorResult("Planning failed: " + errorMsg);
            }
            
            // Parse the response into a TaskPlan
            JSONObject planJson = response.getTaskPlanJson();
            if (planJson == null) {
                return createErrorResult("Failed to parse plan from API response");
            }
            
            // Convert JSON to TaskPlan object
            TaskPlan taskPlan = parseTaskPlanFromJson(planJson, packageName);
            if (taskPlan == null) {
                return createErrorResult("Failed to create task plan from JSON");
            }
            
            // Validate the plan
            ValidationResult validation = validateTaskPlan(taskPlan, packageName);
            if (!validation.isValid) {
                return createErrorResult("Plan validation failed: " + validation.errors.toString());
            }
            
            // Create successful result
            PlanResult result = new PlanResult();
            result.success = true;
            result.plan = taskPlan;
            result.confidence = extractConfidence(planJson);
            result.estimatedDuration = taskPlan.estimatedDuration;
            
            if (DEBUG) Slog.d(TAG, "Task planning successful: " + taskPlan.taskId);
            return result;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error planning task", e);
            return createErrorResult("Planning failed: " + e.getMessage());
        }
    }
    
    /**
     * Validate a task plan for safety and feasibility
     */
    public ValidationResult validateTaskPlan(TaskPlan plan, String packageName) {
        if (DEBUG) Slog.d(TAG, "Validating task plan: " + plan.taskId);
        
        ValidationResult result = new ValidationResult();
        result.isValid = true;
        result.errors = new ArrayList<>();
        result.warnings = new ArrayList<>();
        
        try {
            // Basic validation checks
            if (plan.steps == null || plan.steps.isEmpty()) {
                result.isValid = false;
                result.errors.add("Plan has no steps");
                return result;
            }
            
            if (plan.steps.size() > MAX_STEPS_PER_PLAN) {
                result.isValid = false;
                result.errors.add("Plan has too many steps: " + plan.steps.size());
                return result;
            }
            
            // Validate each step
            Set<String> stepIds = new HashSet<>();
            for (TaskStep step : plan.steps) {
                // Check for duplicate step IDs
                if (stepIds.contains(step.stepId)) {
                    result.isValid = false;
                    result.errors.add("Duplicate step ID: " + step.stepId);
                    continue;
                }
                stepIds.add(step.stepId);
                
                // Validate step action
                if (!mActionRegistry.isActionSupported(step.action)) {
                    result.isValid = false;
                    result.errors.add("Unsupported action: " + step.action);
                }
                
                // Validate dependencies
                if (step.dependencies != null) {
                    for (String depId : step.dependencies) {
                        if (!stepIds.contains(depId)) {
                            // Check if dependency is in previous steps
                            boolean found = false;
                            for (TaskStep prevStep : plan.steps) {
                                if (prevStep.stepId.equals(depId)) {
                                    found = true;
                                    break;
                                }
                                if (prevStep.stepId.equals(step.stepId)) {
                                    break; // We've reached current step
                                }
                            }
                            if (!found) {
                                result.warnings.add("Step " + step.stepId + " depends on unknown step: " + depId);
                            }
                        }
                    }
                }
                
                // Validate timeout
                if (step.timeout <= 0 || step.timeout > 300000) { // Max 5 minutes per step
                    result.warnings.add("Step " + step.stepId + " has invalid timeout: " + step.timeout);
                }
            }
            
            // Advanced validation using Gemini API if enabled
            if (mAdvancedPlanningEnabled && result.isValid) {
                try {
                    String planJson = convertTaskPlanToJson(plan);
                    CompletableFuture<GeminiResponse> future = mGeminiClient.validateTaskPlan(
                            planJson, "", packageName);
                    
                    GeminiResponse response = future.get(15000, TimeUnit.MILLISECONDS);
                    if (response.isSuccess()) {
                        JSONObject validationJson = response.getValidationResultJson();
                        if (validationJson != null) {
                            enhanceValidationWithGeminiResult(result, validationJson);
                        }
                    }
                } catch (Exception e) {
                    Slog.w(TAG, "Advanced validation failed, using basic validation", e);
                }
            }
            
            if (DEBUG) Slog.d(TAG, "Plan validation completed: " + result.isValid);
            return result;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error validating task plan", e);
            result.isValid = false;
            result.errors.add("Validation error: " + e.getMessage());
            return result;
        }
    }
    
    /**
     * Build context string from ContextSnapshot
     */
    private String buildContextString(ContextSnapshot context) {
        if (context == null) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        
        if (context.currentApp != null) {
            sb.append("Current app: ").append(context.currentApp).append("\n");
        }
        
        if (context.screenContent != null && !context.screenContent.isEmpty()) {
            sb.append("Screen content: ").append(context.screenContent).append("\n");
        }
        
        if (context.recentNotifications != null && !context.recentNotifications.isEmpty()) {
            sb.append("Recent notifications: ").append(context.recentNotifications.size()).append(" items\n");
        }
        
        if (context.deviceState != null) {
            sb.append("Device state: ").append(context.deviceState.toString()).append("\n");
        }
        
        if (context.userActivity != null) {
            sb.append("User activity: ").append(context.userActivity).append("\n");
        }
        
        return sb.toString();
    }
    
    /**
     * Parse TaskPlan from JSON response
     */
    private TaskPlan parseTaskPlanFromJson(JSONObject json, String packageName) {
        try {
            TaskPlan plan = new TaskPlan();
            
            plan.taskId = json.optString("taskId", generateTaskId());
            plan.goal = json.optString("goal", "");
            plan.estimatedDuration = json.optLong("estimatedDuration", 60000);
            plan.packageName = packageName;
            plan.createdTime = System.currentTimeMillis();
            
            // Parse steps
            JSONArray stepsArray = json.optJSONArray("steps");
            if (stepsArray != null) {
                plan.steps = new ArrayList<>();
                for (int i = 0; i < stepsArray.length(); i++) {
                    JSONObject stepJson = stepsArray.getJSONObject(i);
                    TaskStep step = parseTaskStepFromJson(stepJson);
                    if (step != null) {
                        plan.steps.add(step);
                    }
                }
            }
            
            return plan;
            
        } catch (JSONException e) {
            Slog.e(TAG, "Error parsing task plan JSON", e);
            return null;
        }
    }
    
    /**
     * Parse TaskStep from JSON
     */
    private TaskStep parseTaskStepFromJson(JSONObject json) {
        try {
            TaskStep step = new TaskStep();
            
            step.stepId = json.optString("stepId", "");
            step.action = json.optString("action", "");
            step.description = json.optString("description", "");
            step.timeout = json.optLong("timeout", 30000);
            
            // Parse parameters
            JSONObject paramsJson = json.optJSONObject("parameters");
            if (paramsJson != null) {
                step.parameters = new Bundle();
                JSONArray names = paramsJson.names();
                if (names != null) {
                    for (int i = 0; i < names.length(); i++) {
                        String key = names.getString(i);
                        String value = paramsJson.optString(key, "");
                        step.parameters.putString(key, value);
                    }
                }
            }
            
            // Parse dependencies
            JSONArray depsArray = json.optJSONArray("dependencies");
            if (depsArray != null) {
                step.dependencies = new ArrayList<>();
                for (int i = 0; i < depsArray.length(); i++) {
                    step.dependencies.add(depsArray.getString(i));
                }
            }
            
            return step;
            
        } catch (JSONException e) {
            Slog.e(TAG, "Error parsing task step JSON", e);
            return null;
        }
    }
    
    /**
     * Extract confidence from plan JSON
     */
    private double extractConfidence(JSONObject json) {
        return json.optDouble("confidence", 0.8);
    }
    
    /**
     * Convert TaskPlan to JSON string for validation
     */
    private String convertTaskPlanToJson(TaskPlan plan) {
        try {
            JSONObject json = new JSONObject();
            json.put("taskId", plan.taskId);
            json.put("goal", plan.goal);
            json.put("estimatedDuration", plan.estimatedDuration);
            
            JSONArray stepsArray = new JSONArray();
            if (plan.steps != null) {
                for (TaskStep step : plan.steps) {
                    JSONObject stepJson = new JSONObject();
                    stepJson.put("stepId", step.stepId);
                    stepJson.put("action", step.action);
                    stepJson.put("description", step.description);
                    stepJson.put("timeout", step.timeout);
                    
                    if (step.parameters != null) {
                        JSONObject paramsJson = new JSONObject();
                        for (String key : step.parameters.keySet()) {
                            paramsJson.put(key, step.parameters.getString(key));
                        }
                        stepJson.put("parameters", paramsJson);
                    }
                    
                    if (step.dependencies != null) {
                        JSONArray depsArray = new JSONArray();
                        for (String dep : step.dependencies) {
                            depsArray.put(dep);
                        }
                        stepJson.put("dependencies", depsArray);
                    }
                    
                    stepsArray.put(stepJson);
                }
            }
            json.put("steps", stepsArray);
            
            return json.toString();
            
        } catch (JSONException e) {
            Slog.e(TAG, "Error converting task plan to JSON", e);
            return "{}";
        }
    }
    
    /**
     * Enhance validation result with Gemini API response
     */
    private void enhanceValidationWithGeminiResult(ValidationResult result, JSONObject geminiResult) {
        try {
            boolean geminiValid = geminiResult.optBoolean("isValid", true);
            if (!geminiValid) {
                result.isValid = false;
            }
            
            JSONArray issues = geminiResult.optJSONArray("issues");
            if (issues != null) {
                for (int i = 0; i < issues.length(); i++) {
                    result.errors.add("AI validation: " + issues.getString(i));
                }
            }
            
            JSONArray suggestions = geminiResult.optJSONArray("suggestions");
            if (suggestions != null) {
                for (int i = 0; i < suggestions.length(); i++) {
                    result.warnings.add("AI suggestion: " + suggestions.getString(i));
                }
            }
            
        } catch (JSONException e) {
            Slog.w(TAG, "Error parsing Gemini validation result", e);
        }
    }
    
    /**
     * Create error result
     */
    private PlanResult createErrorResult(String errorMessage) {
        PlanResult result = new PlanResult();
        result.success = false;
        result.errorMessage = errorMessage;
        result.confidence = 0.0;
        return result;
    }
    
    /**
     * Generate unique task ID
     */
    private String generateTaskId() {
        return "task_" + System.currentTimeMillis() + "_" + hashCode();
    }
}
