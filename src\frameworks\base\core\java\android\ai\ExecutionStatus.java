/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Status of task execution
 */
public class ExecutionStatus implements Parcelable {
    // Status constants
    public static final int STATUS_PENDING = 0;
    public static final int STATUS_RUNNING = 1;
    public static final int STATUS_COMPLETED = 2;
    public static final int STATUS_FAILED = 3;
    public static final int STATUS_CANCELLED = 4;

    public String taskId;
    public int status;
    public int progress; // 0-100
    public String currentAction;
    public Bundle statusData;
    public long startTime;
    public long estimatedCompletion;

    public ExecutionStatus() {
        this.taskId = null;
        this.status = STATUS_PENDING;
        this.progress = 0;
        this.currentAction = null;
        this.statusData = new Bundle();
        this.startTime = System.currentTimeMillis();
        this.estimatedCompletion = 0;
    }

    public ExecutionStatus(String taskId) {
        this();
        this.taskId = taskId;
    }

    protected ExecutionStatus(Parcel in) {
        taskId = in.readString();
        status = in.readInt();
        progress = in.readInt();
        currentAction = in.readString();
        statusData = in.readParcelable(Bundle.class.getClassLoader());
        startTime = in.readLong();
        estimatedCompletion = in.readLong();
    }

    public static final Creator<ExecutionStatus> CREATOR = new Creator<ExecutionStatus>() {
        @Override
        public ExecutionStatus createFromParcel(Parcel in) {
            return new ExecutionStatus(in);
        }

        @Override
        public ExecutionStatus[] newArray(int size) {
            return new ExecutionStatus[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeInt(status);
        dest.writeInt(progress);
        dest.writeString(currentAction);
        dest.writeParcelable(statusData, flags);
        dest.writeLong(startTime);
        dest.writeLong(estimatedCompletion);
    }

    public boolean isPending() {
        return status == STATUS_PENDING;
    }

    public boolean isRunning() {
        return status == STATUS_RUNNING;
    }

    public boolean isCompleted() {
        return status == STATUS_COMPLETED;
    }

    public boolean isFailed() {
        return status == STATUS_FAILED;
    }

    public boolean isCancelled() {
        return status == STATUS_CANCELLED;
    }

    public boolean isFinished() {
        return status == STATUS_COMPLETED || status == STATUS_FAILED || status == STATUS_CANCELLED;
    }

    public String getStatusString() {
        switch (status) {
            case STATUS_PENDING: return "PENDING";
            case STATUS_RUNNING: return "RUNNING";
            case STATUS_COMPLETED: return "COMPLETED";
            case STATUS_FAILED: return "FAILED";
            case STATUS_CANCELLED: return "CANCELLED";
            default: return "UNKNOWN";
        }
    }

    public long getElapsedTime() {
        return System.currentTimeMillis() - startTime;
    }

    public long getRemainingTime() {
        if (estimatedCompletion > 0) {
            return Math.max(0, estimatedCompletion - System.currentTimeMillis());
        }
        return 0;
    }

    public void setStatusData(String key, String value) {
        if (statusData == null) {
            statusData = new Bundle();
        }
        statusData.putString(key, value);
    }

    public void setStatusData(String key, int value) {
        if (statusData == null) {
            statusData = new Bundle();
        }
        statusData.putInt(key, value);
    }

    public String getStatusData(String key) {
        return statusData != null ? statusData.getString(key) : null;
    }

    @Override
    public String toString() {
        return "ExecutionStatus{" +
                "taskId='" + taskId + '\'' +
                ", status=" + getStatusString() +
                ", progress=" + progress +
                ", currentAction='" + currentAction + '\'' +
                ", startTime=" + startTime +
                ", estimatedCompletion=" + estimatedCompletion +
                '}';
    }
}
