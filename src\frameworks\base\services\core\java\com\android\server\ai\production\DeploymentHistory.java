/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;

/**
 * Historical record of a deployment operation
 */
public class DeploymentHistory {
    private final String mDeploymentId;
    private final DeploymentConfiguration mConfiguration;
    private final DeploymentResult mResult;
    private final long mStartTime;
    private final long mEndTime;
    private final long mDuration;
    private final boolean mSuccess;
    
    public DeploymentHistory(DeploymentSession session, DeploymentResult result) {
        mDeploymentId = session.getId();
        mConfiguration = session.getConfiguration();
        mResult = result;
        mStartTime = session.getStartTime();
        mEndTime = session.getEndTime();
        mDuration = session.getDuration();
        mSuccess = result.isSuccess();
    }
    
    public String getDeploymentId() {
        return mDeploymentId;
    }
    
    public DeploymentConfiguration getConfiguration() {
        return mConfiguration;
    }
    
    public DeploymentResult getResult() {
        return mResult;
    }
    
    public long getStartTime() {
        return mStartTime;
    }
    
    public long getEndTime() {
        return mEndTime;
    }
    
    public long getDuration() {
        return mDuration;
    }
    
    public boolean isSuccess() {
        return mSuccess;
    }
    
    public Bundle toBundle() {
        Bundle bundle = new Bundle();
        bundle.putString("deployment_id", mDeploymentId);
        bundle.putLong("start_time", mStartTime);
        bundle.putLong("end_time", mEndTime);
        bundle.putLong("duration", mDuration);
        bundle.putBoolean("success", mSuccess);
        
        if (mConfiguration != null) {
            bundle.putString("version", mConfiguration.getVersion());
            bundle.putString("environment", mConfiguration.getEnvironment());
        }
        
        if (mResult != null) {
            bundle.putString("message", mResult.getMessage());
            if (mResult.getErrorMessage() != null) {
                bundle.putString("error_message", mResult.getErrorMessage());
            }
        }
        
        return bundle;
    }
}
