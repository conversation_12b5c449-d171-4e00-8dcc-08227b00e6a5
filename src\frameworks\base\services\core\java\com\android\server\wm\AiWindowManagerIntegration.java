/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.wm;

import android.content.ComponentName;
import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.util.Slog;
import android.view.Display;
import android.view.WindowManager;

import com.android.server.ai.AiContextEngineService;
import com.android.server.ai.AiPlanningOrchestrationService;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI integration layer for WindowManagerService in Jarvis OS.
 * 
 * Provides AI-aware window management, display state tracking, UI interaction monitoring,
 * and intelligent window placement suggestions for the AI context engine.
 */
public class AiWindowManagerIntegration {
    private static final String TAG = "AiWindowManagerIntegration";
    private static final boolean DEBUG = true;

    // Window state tracking for AI context
    private static final int AI_WINDOW_STATE_CREATED = 1;
    private static final int AI_WINDOW_STATE_SHOWN = 2;
    private static final int AI_WINDOW_STATE_HIDDEN = 3;
    private static final int AI_WINDOW_STATE_FOCUSED = 4;
    private static final int AI_WINDOW_STATE_UNFOCUSED = 5;
    private static final int AI_WINDOW_STATE_RESIZED = 6;
    private static final int AI_WINDOW_STATE_MOVED = 7;
    private static final int AI_WINDOW_STATE_DESTROYED = 8;

    // Display orientation tracking
    private static final int AI_ORIENTATION_PORTRAIT = 1;
    private static final int AI_ORIENTATION_LANDSCAPE = 2;
    private static final int AI_ORIENTATION_REVERSE_PORTRAIT = 3;
    private static final int AI_ORIENTATION_REVERSE_LANDSCAPE = 4;

    private final Context mContext;
    private final WindowManagerService mWindowManagerService;
    private final Handler mHandler;
    
    // AI service references
    private AiContextEngineService mContextEngine;
    private AiPlanningOrchestrationService mPlanningService;
    
    // Window and display tracking
    private final ConcurrentHashMap<WindowState, WindowInfo> mTrackedWindows = new ConcurrentHashMap<>();
    private final List<DisplayStateChange> mRecentDisplayChanges = new ArrayList<>();
    private final ConcurrentHashMap<Integer, DisplayInfo> mDisplayStates = new ConcurrentHashMap<>();
    
    // Configuration
    private boolean mAiIntegrationEnabled = true;
    private boolean mWindowTrackingEnabled = true;
    private boolean mDisplayTrackingEnabled = true;
    private boolean mUiInteractionTrackingEnabled = true;
    private boolean mWindowPlacementPredictionEnabled = true;
    private int mMaxRecentDisplayChanges = 50;
    
    // Statistics
    private long mTotalWindowOperations = 0;
    private long mAiPredictedPlacements = 0;
    private long mCorrectPlacements = 0;
    private long mDisplayStateChanges = 0;

    public AiWindowManagerIntegration(Context context, WindowManagerService wms, Handler handler) {
        mContext = context;
        mWindowManagerService = wms;
        mHandler = handler;
        
        if (DEBUG) Slog.d(TAG, "AiWindowManagerIntegration created");
    }

    /**
     * Initialize AI service connections
     */
    public void initializeAiServices() {
        // Get references to AI services
        mContextEngine = (AiContextEngineService) mContext.getSystemService(Context.AI_CONTEXT_ENGINE_SERVICE);
        mPlanningService = (AiPlanningOrchestrationService) mContext.getSystemService(Context.AI_PLANNING_ORCHESTRATION_SERVICE);
        
        // Initialize display state tracking
        initializeDisplayTracking();
        
        if (DEBUG) Slog.d(TAG, "AI services initialized");
    }

    /**
     * Called when a window is being added to the system
     */
    public void onWindowAdded(WindowState window) {
        if (!mAiIntegrationEnabled || !mWindowTrackingEnabled || window == null) {
            return;
        }
        
        try {
            // Create window info for tracking
            WindowInfo windowInfo = new WindowInfo(window, AI_WINDOW_STATE_CREATED, 
                SystemClock.elapsedRealtime());
            mTrackedWindows.put(window, windowInfo);
            
            // Collect window context for AI
            if (mContextEngine != null) {
                Bundle contextData = createWindowContextData(window, "window_added");
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            // Check for AI-suggested window placement
            if (mWindowPlacementPredictionEnabled && mPlanningService != null) {
                suggestWindowPlacement(window);
            }
            
            mTotalWindowOperations++;
            
            if (DEBUG) Slog.d(TAG, "Window added: " + window.toString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onWindowAdded", e);
        }
    }

    /**
     * Called when a window is being removed from the system
     */
    public void onWindowRemoved(WindowState window) {
        if (!mAiIntegrationEnabled || !mWindowTrackingEnabled || window == null) {
            return;
        }
        
        try {
            // Update window state
            WindowInfo windowInfo = mTrackedWindows.get(window);
            if (windowInfo != null) {
                windowInfo.updateState(AI_WINDOW_STATE_DESTROYED, SystemClock.elapsedRealtime());
            }
            
            // Collect window context for AI
            if (mContextEngine != null) {
                Bundle contextData = createWindowContextData(window, "window_removed");
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            // Clean up tracking
            mTrackedWindows.remove(window);
            mTotalWindowOperations++;
            
            if (DEBUG) Slog.d(TAG, "Window removed: " + window.toString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onWindowRemoved", e);
        }
    }

    /**
     * Called when a window gains focus
     */
    public void onWindowFocusChanged(WindowState window, boolean hasFocus) {
        if (!mAiIntegrationEnabled || !mWindowTrackingEnabled || window == null) {
            return;
        }
        
        try {
            // Update window state
            WindowInfo windowInfo = mTrackedWindows.get(window);
            if (windowInfo != null) {
                int newState = hasFocus ? AI_WINDOW_STATE_FOCUSED : AI_WINDOW_STATE_UNFOCUSED;
                windowInfo.updateState(newState, SystemClock.elapsedRealtime());
            }
            
            // Collect focus context for AI
            if (mContextEngine != null) {
                Bundle contextData = createWindowContextData(window, 
                    hasFocus ? "window_focused" : "window_unfocused");
                contextData.putBoolean("has_focus", hasFocus);
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            // Notify AI services of focus change
            if (hasFocus && mPlanningService != null) {
                ComponentName component = getWindowComponent(window);
                if (component != null) {
                    mPlanningService.onWindowFocusChanged(component.getPackageName(), 
                        component.getClassName(), true);
                }
            }
            
            if (DEBUG) Slog.d(TAG, "Window focus changed: " + window.toString() + 
                " focus=" + hasFocus);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onWindowFocusChanged", e);
        }
    }

    /**
     * Called when a window is resized or moved
     */
    public void onWindowConfigurationChanged(WindowState window, Rect oldBounds, Rect newBounds) {
        if (!mAiIntegrationEnabled || !mWindowTrackingEnabled || window == null) {
            return;
        }
        
        try {
            // Update window state
            WindowInfo windowInfo = mTrackedWindows.get(window);
            if (windowInfo != null) {
                windowInfo.updateState(AI_WINDOW_STATE_RESIZED, SystemClock.elapsedRealtime());
                windowInfo.updateBounds(newBounds);
            }
            
            // Collect configuration context for AI
            if (mContextEngine != null) {
                Bundle contextData = createWindowContextData(window, "window_configuration_changed");
                contextData.putParcelable("old_bounds", oldBounds);
                contextData.putParcelable("new_bounds", newBounds);
                
                // Calculate size and position changes
                if (oldBounds != null && newBounds != null) {
                    int widthChange = newBounds.width() - oldBounds.width();
                    int heightChange = newBounds.height() - oldBounds.height();
                    int xChange = newBounds.left - oldBounds.left;
                    int yChange = newBounds.top - oldBounds.top;
                    
                    contextData.putInt("width_change", widthChange);
                    contextData.putInt("height_change", heightChange);
                    contextData.putInt("x_change", xChange);
                    contextData.putInt("y_change", yChange);
                    
                    boolean isResize = widthChange != 0 || heightChange != 0;
                    boolean isMove = xChange != 0 || yChange != 0;
                    
                    contextData.putBoolean("is_resize", isResize);
                    contextData.putBoolean("is_move", isMove);
                }
                
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            mTotalWindowOperations++;
            
            if (DEBUG) Slog.d(TAG, "Window configuration changed: " + window.toString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onWindowConfigurationChanged", e);
        }
    }

    /**
     * Called when display configuration changes (rotation, resolution, etc.)
     */
    public void onDisplayConfigurationChanged(int displayId, int newOrientation, Rect newBounds) {
        if (!mAiIntegrationEnabled || !mDisplayTrackingEnabled) {
            return;
        }
        
        try {
            // Update display state
            DisplayInfo displayInfo = mDisplayStates.get(displayId);
            if (displayInfo == null) {
                displayInfo = new DisplayInfo(displayId);
                mDisplayStates.put(displayId, displayInfo);
            }
            
            int oldOrientation = displayInfo.orientation;
            displayInfo.updateConfiguration(newOrientation, newBounds, SystemClock.elapsedRealtime());
            
            // Record display state change
            DisplayStateChange change = new DisplayStateChange(displayId, oldOrientation, 
                newOrientation, SystemClock.elapsedRealtime());
            recordDisplayStateChange(change);
            
            // Collect display context for AI
            if (mContextEngine != null) {
                Bundle contextData = createDisplayContextData(displayId, "display_configuration_changed");
                contextData.putInt("old_orientation", oldOrientation);
                contextData.putInt("new_orientation", newOrientation);
                contextData.putParcelable("new_bounds", newBounds);
                contextData.putBoolean("orientation_changed", oldOrientation != newOrientation);
                
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            // Notify AI services of display change
            if (mPlanningService != null) {
                mPlanningService.onDisplayConfigurationChanged(displayId, newOrientation);
            }
            
            mDisplayStateChanges++;
            
            if (DEBUG) Slog.d(TAG, "Display configuration changed: display=" + displayId + 
                " orientation=" + newOrientation);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onDisplayConfigurationChanged", e);
        }
    }

    /**
     * Called when user interacts with a window (touch, key events, etc.)
     */
    public void onWindowInteraction(WindowState window, String interactionType, Bundle extras) {
        if (!mAiIntegrationEnabled || !mUiInteractionTrackingEnabled || window == null) {
            return;
        }
        
        try {
            // Collect interaction context for AI
            if (mContextEngine != null) {
                Bundle contextData = createWindowContextData(window, "window_interaction");
                contextData.putString("interaction_type", interactionType);
                contextData.putLong("interaction_timestamp", System.currentTimeMillis());
                
                if (extras != null) {
                    contextData.putBundle("interaction_extras", extras);
                }
                
                mContextEngine.updateContext("window_manager", contextData);
            }
            
            if (DEBUG) Slog.d(TAG, "Window interaction: " + window.toString() + 
                " type=" + interactionType);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onWindowInteraction", e);
        }
    }

    /**
     * Get AI-suggested window placement for a new window
     */
    public Rect getAiSuggestedWindowPlacement(WindowState window, Rect defaultBounds) {
        if (!mWindowPlacementPredictionEnabled || mPlanningService == null || window == null) {
            return defaultBounds;
        }
        
        try {
            // Create placement request
            Bundle placementRequest = new Bundle();
            placementRequest.putString("window_type", getWindowTypeString(window));
            placementRequest.putParcelable("default_bounds", defaultBounds);
            placementRequest.putLong("timestamp", System.currentTimeMillis());
            
            ComponentName component = getWindowComponent(window);
            if (component != null) {
                placementRequest.putString("package_name", component.getPackageName());
                placementRequest.putString("activity_name", component.getClassName());
            }
            
            // Add current display state
            addCurrentDisplayStateToBundle(placementRequest);
            
            // Add recent window operations
            addRecentWindowOperationsToBundle(placementRequest);
            
            // Get AI suggestion
            Bundle suggestion = mPlanningService.suggestWindowPlacement(placementRequest);
            if (suggestion != null) {
                Rect suggestedBounds = suggestion.getParcelable("suggested_bounds");
                float confidence = suggestion.getFloat("confidence", 0.0f);
                
                if (suggestedBounds != null && confidence > 0.7f) {
                    mAiPredictedPlacements++;
                    
                    if (DEBUG) Slog.d(TAG, "AI suggested window placement: " + suggestedBounds + 
                        " (confidence: " + confidence + ")");
                    
                    return suggestedBounds;
                }
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error getting AI window placement suggestion", e);
        }
        
        return defaultBounds;
    }

    /**
     * Enable or disable AI integration
     */
    public void setAiIntegrationEnabled(boolean enabled) {
        mAiIntegrationEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "AI integration " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Enable or disable window tracking
     */
    public void setWindowTrackingEnabled(boolean enabled) {
        mWindowTrackingEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "Window tracking " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Get integration statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("ai_integration_enabled", mAiIntegrationEnabled);
        stats.putBoolean("window_tracking_enabled", mWindowTrackingEnabled);
        stats.putBoolean("display_tracking_enabled", mDisplayTrackingEnabled);
        stats.putBoolean("ui_interaction_tracking_enabled", mUiInteractionTrackingEnabled);
        stats.putBoolean("window_placement_prediction_enabled", mWindowPlacementPredictionEnabled);
        
        stats.putInt("tracked_windows", mTrackedWindows.size());
        stats.putInt("tracked_displays", mDisplayStates.size());
        stats.putInt("recent_display_changes", mRecentDisplayChanges.size());
        
        stats.putLong("total_window_operations", mTotalWindowOperations);
        stats.putLong("ai_predicted_placements", mAiPredictedPlacements);
        stats.putLong("correct_placements", mCorrectPlacements);
        stats.putLong("display_state_changes", mDisplayStateChanges);
        
        if (mTotalWindowOperations > 0) {
            float predictionRate = (float) mAiPredictedPlacements / mTotalWindowOperations * 100.0f;
            stats.putFloat("prediction_rate", predictionRate);
        }
        
        if (mAiPredictedPlacements > 0) {
            float accuracy = (float) mCorrectPlacements / mAiPredictedPlacements * 100.0f;
            stats.putFloat("prediction_accuracy", accuracy);
        }
        
        return stats;
    }

    /**
     * Dump integration state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("  AiWindowManagerIntegration State:");
        pw.println("    AI Integration Enabled: " + mAiIntegrationEnabled);
        pw.println("    Window Tracking Enabled: " + mWindowTrackingEnabled);
        pw.println("    Display Tracking Enabled: " + mDisplayTrackingEnabled);
        pw.println("    UI Interaction Tracking Enabled: " + mUiInteractionTrackingEnabled);
        pw.println("    Window Placement Prediction Enabled: " + mWindowPlacementPredictionEnabled);
        pw.println("    Tracked Windows: " + mTrackedWindows.size());
        pw.println("    Tracked Displays: " + mDisplayStates.size());
        pw.println("    Recent Display Changes: " + mRecentDisplayChanges.size());
        pw.println("    Total Window Operations: " + mTotalWindowOperations);
        pw.println("    AI Predicted Placements: " + mAiPredictedPlacements);
        pw.println("    Correct Placements: " + mCorrectPlacements);
        pw.println("    Display State Changes: " + mDisplayStateChanges);
        
        if (mTotalWindowOperations > 0) {
            float predictionRate = (float) mAiPredictedPlacements / mTotalWindowOperations * 100.0f;
            pw.println("    Prediction Rate: " + predictionRate + "%");
        }
        
        if (mAiPredictedPlacements > 0) {
            float accuracy = (float) mCorrectPlacements / mAiPredictedPlacements * 100.0f;
            pw.println("    Prediction Accuracy: " + accuracy + "%");
        }
    }

    // Private helper methods

    private void initializeDisplayTracking() {
        // Initialize tracking for all current displays
        // This would integrate with the actual display manager
        DisplayInfo primaryDisplay = new DisplayInfo(Display.DEFAULT_DISPLAY);
        primaryDisplay.updateConfiguration(AI_ORIENTATION_PORTRAIT, 
            new Rect(0, 0, 1080, 1920), SystemClock.elapsedRealtime());
        mDisplayStates.put(Display.DEFAULT_DISPLAY, primaryDisplay);
    }

    private Bundle createWindowContextData(WindowState window, String event) {
        Bundle contextData = new Bundle();
        contextData.putString("event_type", event);
        contextData.putLong("timestamp", System.currentTimeMillis());
        contextData.putString("window_type", getWindowTypeString(window));
        
        ComponentName component = getWindowComponent(window);
        if (component != null) {
            contextData.putString("package_name", component.getPackageName());
            contextData.putString("activity_name", component.getClassName());
        }
        
        // Add window bounds if available
        Rect bounds = getWindowBounds(window);
        if (bounds != null) {
            contextData.putParcelable("window_bounds", bounds);
            contextData.putInt("window_width", bounds.width());
            contextData.putInt("window_height", bounds.height());
        }
        
        // Add display information
        int displayId = getWindowDisplayId(window);
        contextData.putInt("display_id", displayId);
        
        DisplayInfo displayInfo = mDisplayStates.get(displayId);
        if (displayInfo != null) {
            contextData.putInt("display_orientation", displayInfo.orientation);
        }
        
        return contextData;
    }

    private Bundle createDisplayContextData(int displayId, String event) {
        Bundle contextData = new Bundle();
        contextData.putString("event_type", event);
        contextData.putLong("timestamp", System.currentTimeMillis());
        contextData.putInt("display_id", displayId);
        
        DisplayInfo displayInfo = mDisplayStates.get(displayId);
        if (displayInfo != null) {
            contextData.putInt("orientation", displayInfo.orientation);
            contextData.putParcelable("bounds", displayInfo.bounds);
            contextData.putLong("last_change_time", displayInfo.lastChangeTime);
        }
        
        return contextData;
    }

    private void suggestWindowPlacement(WindowState window) {
        // This would be called during window creation to get AI suggestions
        Rect defaultBounds = getWindowBounds(window);
        Rect suggestedBounds = getAiSuggestedWindowPlacement(window, defaultBounds);
        
        if (suggestedBounds != null && !suggestedBounds.equals(defaultBounds)) {
            // Apply the AI suggestion (in a real implementation)
            if (DEBUG) Slog.d(TAG, "Applying AI window placement suggestion");
        }
    }

    private void recordDisplayStateChange(DisplayStateChange change) {
        synchronized (mRecentDisplayChanges) {
            mRecentDisplayChanges.add(change);
            
            // Keep only recent changes
            if (mRecentDisplayChanges.size() > mMaxRecentDisplayChanges) {
                mRecentDisplayChanges.remove(0);
            }
        }
    }

    private ComponentName getWindowComponent(WindowState window) {
        // Extract component name from window (simplified)
        // In real implementation, this would access window.mSession.mPackageName, etc.
        return new ComponentName("com.example.app", "com.example.app.MainActivity");
    }

    private String getWindowTypeString(WindowState window) {
        // Determine window type (simplified)
        return "application";
    }

    private Rect getWindowBounds(WindowState window) {
        // Get window bounds (simplified)
        return new Rect(100, 100, 500, 800);
    }

    private int getWindowDisplayId(WindowState window) {
        // Get display ID for window (simplified)
        return Display.DEFAULT_DISPLAY;
    }

    private void addCurrentDisplayStateToBundle(Bundle bundle) {
        // Add current display state information
        bundle.putInt("current_display_count", mDisplayStates.size());
        
        DisplayInfo primaryDisplay = mDisplayStates.get(Display.DEFAULT_DISPLAY);
        if (primaryDisplay != null) {
            bundle.putInt("primary_display_orientation", primaryDisplay.orientation);
            bundle.putParcelable("primary_display_bounds", primaryDisplay.bounds);
        }
    }

    private void addRecentWindowOperationsToBundle(Bundle bundle) {
        // Add recent window operations for context
        bundle.putLong("recent_window_operations", Math.min(mTotalWindowOperations, 10));
    }

    // Inner classes

    private static class WindowInfo {
        final WindowState window;
        int currentState;
        long lastStateChange;
        Rect currentBounds;
        
        WindowInfo(WindowState window, int initialState, long timestamp) {
            this.window = window;
            this.currentState = initialState;
            this.lastStateChange = timestamp;
        }
        
        void updateState(int newState, long timestamp) {
            this.currentState = newState;
            this.lastStateChange = timestamp;
        }
        
        void updateBounds(Rect newBounds) {
            this.currentBounds = new Rect(newBounds);
        }
    }

    private static class DisplayInfo {
        final int displayId;
        int orientation;
        Rect bounds;
        long lastChangeTime;
        
        DisplayInfo(int displayId) {
            this.displayId = displayId;
            this.orientation = AI_ORIENTATION_PORTRAIT;
            this.bounds = new Rect();
            this.lastChangeTime = SystemClock.elapsedRealtime();
        }
        
        void updateConfiguration(int newOrientation, Rect newBounds, long timestamp) {
            this.orientation = newOrientation;
            this.bounds = new Rect(newBounds);
            this.lastChangeTime = timestamp;
        }
    }

    private static class DisplayStateChange {
        final int displayId;
        final int oldOrientation;
        final int newOrientation;
        final long timestamp;
        
        DisplayStateChange(int displayId, int oldOrientation, int newOrientation, long timestamp) {
            this.displayId = displayId;
            this.oldOrientation = oldOrientation;
            this.newOrientation = newOrientation;
            this.timestamp = timestamp;
        }
    }
}
