<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="12dp">

    <LinearLayout
        android:id="@+id/suggestion_card"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/jarvis_suggestion_high_priority_background"
        android:padding="20dp"
        android:elevation="8dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <!-- Priority Indicator Strip -->
        <View
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="@color/jarvis_priority_high"
            android:layout_marginEnd="16dp" />

        <!-- Icon -->
        <ImageView
            android:id="@+id/suggestion_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="top"
            android:layout_marginEnd="16dp"
            android:contentDescription="@string/suggestion_icon" />

        <!-- Content -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Title and Priority -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/suggestion_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/jarvis_suggestion_title_high_priority"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <ImageView
                    android:id="@+id/priority_indicator"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="8dp"
                    android:src="@drawable/ic_priority_high"
                    android:contentDescription="@string/high_priority" />

            </LinearLayout>

            <!-- Description -->
            <TextView
                android:id="@+id/suggestion_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textSize="15sp"
                android:textColor="@color/jarvis_suggestion_description_high_priority"
                android:maxLines="3"
                android:ellipsize="end" />

            <!-- Action Text -->
            <TextView
                android:id="@+id/suggestion_action_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:textSize="15sp"
                android:textStyle="bold"
                android:textColor="@color/jarvis_suggestion_action_high_priority"
                android:background="@drawable/jarvis_action_text_high_priority_background"
                android:padding="12dp"
                android:visibility="gone" />

            <!-- Bottom Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/suggestion_time"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="12sp"
                    android:textColor="@color/jarvis_suggestion_time_high_priority" />

                <!-- Feedback Buttons -->
                <LinearLayout
                    android:id="@+id/feedback_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <ImageButton
                        android:id="@+id/thumbs_up_button"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:src="@drawable/ic_thumbs_up"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:layout_marginEnd="8dp"
                        android:contentDescription="@string/helpful_suggestion" />

                    <ImageButton
                        android:id="@+id/thumbs_down_button"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:src="@drawable/ic_thumbs_down"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/not_helpful_suggestion" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Dismiss Button -->
        <ImageButton
            android:id="@+id/dismiss_button"
            android:layout_width="36dp"
            android:layout_height="36dp"
            android:layout_gravity="top"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_close_small"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/dismiss_suggestion" />

    </LinearLayout>

</LinearLayout>
