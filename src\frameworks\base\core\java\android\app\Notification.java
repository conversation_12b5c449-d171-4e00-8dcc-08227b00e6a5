/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.app;

import android.os.Bundle;

/**
 * Mock implementation of Notification for Jarvis OS compilation.
 * This is a stub implementation for development and testing.
 */
public class Notification {
    // Notification categories
    public static final String CATEGORY_CALL = "call";
    public static final String CATEGORY_MESSAGE = "msg";
    public static final String CATEGORY_EMAIL = "email";
    public static final String CATEGORY_EVENT = "event";
    public static final String CATEGORY_PROMO = "promo";
    public static final String CATEGORY_ALARM = "alarm";
    public static final String CATEGORY_PROGRESS = "progress";
    public static final String CATEGORY_SOCIAL = "social";
    public static final String CATEGORY_ERROR = "err";
    public static final String CATEGORY_TRANSPORT = "transport";
    public static final String CATEGORY_SYSTEM = "sys";
    public static final String CATEGORY_SERVICE = "service";
    public static final String CATEGORY_RECOMMENDATION = "recommendation";
    public static final String CATEGORY_STATUS = "status";
    public static final String CATEGORY_REMINDER = "reminder";

    // Notification priorities
    public static final int PRIORITY_MIN = -2;
    public static final int PRIORITY_LOW = -1;
    public static final int PRIORITY_DEFAULT = 0;
    public static final int PRIORITY_HIGH = 1;
    public static final int PRIORITY_MAX = 2;

    // Notification flags
    public static final int FLAG_SHOW_LIGHTS = 0x00000001;
    public static final int FLAG_ONGOING_EVENT = 0x00000002;
    public static final int FLAG_INSISTENT = 0x00000004;
    public static final int FLAG_ONLY_ALERT_ONCE = 0x00000008;
    public static final int FLAG_AUTO_CANCEL = 0x00000010;
    public static final int FLAG_NO_CLEAR = 0x00000020;
    public static final int FLAG_FOREGROUND_SERVICE = 0x00000040;
    public static final int FLAG_HIGH_PRIORITY = 0x00000080;
    public static final int FLAG_LOCAL_ONLY = 0x00000100;
    public static final int FLAG_GROUP_SUMMARY = 0x00000200;

    // Extra keys
    public static final String EXTRA_TITLE = "android.title";
    public static final String EXTRA_TITLE_BIG = "android.title.big";
    public static final String EXTRA_TEXT = "android.text";
    public static final String EXTRA_SUB_TEXT = "android.subText";
    public static final String EXTRA_INFO_TEXT = "android.infoText";
    public static final String EXTRA_SUMMARY_TEXT = "android.summaryText";
    public static final String EXTRA_BIG_TEXT = "android.bigText";
    public static final String EXTRA_PROGRESS = "android.progress";
    public static final String EXTRA_PROGRESS_MAX = "android.progressMax";
    public static final String EXTRA_PROGRESS_INDETERMINATE = "android.progressIndeterminate";
    public static final String EXTRA_SHOW_CHRONOMETER = "android.showChronometer";
    public static final String EXTRA_SHOW_WHEN = "android.showWhen";
    public static final String EXTRA_SMALL_ICON = "android.icon";
    public static final String EXTRA_LARGE_ICON = "android.largeIcon";
    public static final String EXTRA_LARGE_ICON_BIG = "android.largeIcon.big";
    public static final String EXTRA_PICTURE = "android.picture";

    // Visibility constants
    public static final int VISIBILITY_PUBLIC = 1;
    public static final int VISIBILITY_PRIVATE = 0;
    public static final int VISIBILITY_SECRET = -1;

    // Default values
    public static final int DEFAULT_ALL = ~0;
    public static final int DEFAULT_SOUND = 1;
    public static final int DEFAULT_VIBRATE = 2;
    public static final int DEFAULT_LIGHTS = 4;

    // Stream types
    public static final int STREAM_DEFAULT = -1;

    // Fields
    public long when;
    public int icon;
    public int iconLevel;
    public int number;
    public CharSequence tickerText;
    public int priority = PRIORITY_DEFAULT;
    public String category;
    public String group;
    public String sortKey;
    public Bundle extras;
    public int flags;
    public int defaults;
    public int ledARGB;
    public int ledOnMS;
    public int ledOffMS;
    public int visibility = VISIBILITY_PRIVATE;
    public String channelId;
    public long timeoutAfter;
    public String shortcutId;
    public int badgeIconType;
    public String settingsText;
    public int groupAlertBehavior;

    // Deprecated fields (kept for compatibility)
    @Deprecated
    public int audioStreamType = STREAM_DEFAULT;

    public Notification() {
        this.when = System.currentTimeMillis();
        this.priority = PRIORITY_DEFAULT;
        this.extras = new Bundle();
    }

    public Notification(int icon, CharSequence tickerText, long when) {
        this();
        this.icon = icon;
        this.tickerText = tickerText;
        this.when = when;
    }

    /**
     * Builder class for creating Notification objects
     */
    public static class Builder {
        private Notification mNotification;

        public Builder(android.content.Context context) {
            mNotification = new Notification();
        }

        public Builder(android.content.Context context, String channelId) {
            this(context);
            mNotification.channelId = channelId;
        }

        public Builder setContentTitle(CharSequence title) {
            mNotification.extras.putCharSequence(EXTRA_TITLE, title);
            return this;
        }

        public Builder setContentText(CharSequence text) {
            mNotification.extras.putCharSequence(EXTRA_TEXT, text);
            return this;
        }

        public Builder setSubText(CharSequence text) {
            mNotification.extras.putCharSequence(EXTRA_SUB_TEXT, text);
            return this;
        }

        public Builder setSmallIcon(int icon) {
            mNotification.icon = icon;
            return this;
        }

        public Builder setWhen(long when) {
            mNotification.when = when;
            return this;
        }

        public Builder setPriority(int priority) {
            mNotification.priority = priority;
            return this;
        }

        public Builder setCategory(String category) {
            mNotification.category = category;
            return this;
        }

        public Builder setGroup(String groupKey) {
            mNotification.group = groupKey;
            return this;
        }

        public Builder setSortKey(String sortKey) {
            mNotification.sortKey = sortKey;
            return this;
        }

        public Builder setAutoCancel(boolean autoCancel) {
            if (autoCancel) {
                mNotification.flags |= FLAG_AUTO_CANCEL;
            } else {
                mNotification.flags &= ~FLAG_AUTO_CANCEL;
            }
            return this;
        }

        public Builder setOngoing(boolean ongoing) {
            if (ongoing) {
                mNotification.flags |= FLAG_ONGOING_EVENT;
            } else {
                mNotification.flags &= ~FLAG_ONGOING_EVENT;
            }
            return this;
        }

        public Builder setDefaults(int defaults) {
            mNotification.defaults = defaults;
            return this;
        }

        public Builder setVisibility(int visibility) {
            mNotification.visibility = visibility;
            return this;
        }

        public Builder setChannelId(String channelId) {
            mNotification.channelId = channelId;
            return this;
        }

        public Builder setTimeoutAfter(long durationMs) {
            mNotification.timeoutAfter = durationMs;
            return this;
        }

        public Builder setShortcutId(String shortcutId) {
            mNotification.shortcutId = shortcutId;
            return this;
        }

        public Builder setBadgeIconType(int icon) {
            mNotification.badgeIconType = icon;
            return this;
        }

        public Builder setSettingsText(CharSequence text) {
            mNotification.settingsText = String.valueOf(text);
            return this;
        }

        public Builder setGroupAlertBehavior(int groupAlertBehavior) {
            mNotification.groupAlertBehavior = groupAlertBehavior;
            return this;
        }

        public Notification build() {
            return mNotification;
        }
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("Notification(");
        sb.append("pri=").append(priority);
        sb.append(" contentView=null");
        sb.append(" vibrate=null");
        sb.append(" sound=null");
        sb.append(" defaults=0x").append(Integer.toHexString(defaults));
        sb.append(" flags=0x").append(Integer.toHexString(flags));
        sb.append(" color=0x00000000");
        if (category != null) {
            sb.append(" category=").append(category);
        }
        if (group != null) {
            sb.append(" groupKey=").append(group);
        }
        if (sortKey != null) {
            sb.append(" sortKey=").append(sortKey);
        }
        sb.append(")");
        return sb.toString();
    }
}
