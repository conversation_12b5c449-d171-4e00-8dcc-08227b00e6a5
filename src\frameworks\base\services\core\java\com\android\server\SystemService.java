/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server;

import android.content.Context;
import android.os.IBinder;

/**
 * Base class for system services
 */
public abstract class SystemService {
    public static final int PHASE_WAIT_FOR_DEFAULT_DISPLAY = 100;
    public static final int PHASE_LOCK_SETTINGS_READY = 480;
    public static final int PHASE_SYSTEM_SERVICES_READY = 500;
    public static final int PHASE_ACTIVITY_MANAGER_READY = 550;
    public static final int PHASE_THIRD_PARTY_APPS_CAN_START = 600;
    public static final int PHASE_BOOT_COMPLETED = 1000;

    private final Context mContext;

    public SystemService(Context context) {
        mContext = context;
    }

    public final Context getContext() {
        return mContext;
    }

    public abstract void onStart();

    public void onBootPhase(int phase) {
        // Default implementation does nothing
    }

    public void onStartUser(int userHandle) {
        // Default implementation does nothing
    }

    public void onUnlockUser(int userHandle) {
        // Default implementation does nothing
    }

    public void onSwitchUser(int userHandle) {
        // Default implementation does nothing
    }

    public void onStopUser(int userHandle) {
        // Default implementation does nothing
    }

    public void onCleanupUser(int userHandle) {
        // Default implementation does nothing
    }

    protected final void publishBinderService(String name, IBinder service) {
        publishBinderService(name, service, false);
    }

    protected final void publishBinderService(String name, IBinder service, boolean allowIsolated) {
        ServiceManager.addService(name, service, allowIsolated);
    }

    protected final IBinder getBinderService(String name) {
        return ServiceManager.getService(name);
    }

    protected final void publishLocalService(Class<? extends Object> type, Object service) {
        LocalServices.addService(type, service);
    }

    protected final <T> T getLocalService(Class<T> type) {
        return LocalServices.getService(type);
    }

    /**
     * Mock ServiceManager for compilation
     */
    private static class ServiceManager {
        public static void addService(String name, IBinder service) {
            addService(name, service, false);
        }

        public static void addService(String name, IBinder service, boolean allowIsolated) {
            // Mock implementation
        }

        public static IBinder getService(String name) {
            // Mock implementation
            return null;
        }
    }

    /**
     * Mock LocalServices for compilation
     */
    private static class LocalServices {
        public static <T> void addService(Class<T> type, T service) {
            // Mock implementation
        }

        public static <T> T getService(Class<T> type) {
            // Mock implementation
            return null;
        }
    }
}
