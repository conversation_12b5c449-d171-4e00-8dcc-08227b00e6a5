/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Request for action execution
 */
public class ActionRequest implements Parcelable {
    public String action;
    public String actionType;
    public Bundle parameters;
    public ContextSnapshot context;
    public String requestId;
    public String packageName;
    public String taskId;
    public String stepId;
    public int priority;
    public long timeout;

    public ActionRequest() {
        this.action = null;
        this.actionType = null;
        this.parameters = new Bundle();
        this.context = null;
        this.requestId = null;
        this.packageName = null;
        this.taskId = null;
        this.stepId = null;
        this.priority = 0;
        this.timeout = 30000; // 30 seconds default
    }

    public ActionRequest(String action) {
        this();
        this.action = action;
        this.actionType = action;
    }

    protected ActionRequest(Parcel in) {
        action = in.readString();
        actionType = in.readString();
        parameters = in.readParcelable(Bundle.class.getClassLoader());
        context = in.readParcelable(ContextSnapshot.class.getClassLoader());
        requestId = in.readString();
        packageName = in.readString();
        taskId = in.readString();
        stepId = in.readString();
        priority = in.readInt();
        timeout = in.readLong();
    }

    public static final Creator<ActionRequest> CREATOR = new Creator<ActionRequest>() {
        @Override
        public ActionRequest createFromParcel(Parcel in) {
            return new ActionRequest(in);
        }

        @Override
        public ActionRequest[] newArray(int size) {
            return new ActionRequest[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(action);
        dest.writeString(actionType);
        dest.writeParcelable(parameters, flags);
        dest.writeParcelable(context, flags);
        dest.writeString(requestId);
        dest.writeString(packageName);
        dest.writeString(taskId);
        dest.writeString(stepId);
        dest.writeInt(priority);
        dest.writeLong(timeout);
    }

    public void setParameter(String key, String value) {
        if (parameters == null) {
            parameters = new Bundle();
        }
        parameters.putString(key, value);
    }

    public void setParameter(String key, int value) {
        if (parameters == null) {
            parameters = new Bundle();
        }
        parameters.putInt(key, value);
    }

    public void setParameter(String key, boolean value) {
        if (parameters == null) {
            parameters = new Bundle();
        }
        parameters.putBoolean(key, value);
    }

    public String getStringParameter(String key) {
        return parameters != null ? parameters.getString(key) : null;
    }

    public int getIntParameter(String key, int defaultValue) {
        return parameters != null ? parameters.getInt(key, defaultValue) : defaultValue;
    }

    public boolean getBooleanParameter(String key, boolean defaultValue) {
        return parameters != null ? parameters.getBoolean(key, defaultValue) : defaultValue;
    }

    public boolean hasParameter(String key) {
        return parameters != null && parameters.containsKey(key);
    }

    @Override
    public String toString() {
        return "ActionRequest{" +
                "action='" + action + '\'' +
                ", actionType='" + actionType + '\'' +
                ", requestId='" + requestId + '\'' +
                ", packageName='" + packageName + '\'' +
                ", taskId='" + taskId + '\'' +
                ", stepId='" + stepId + '\'' +
                ", priority=" + priority +
                ", timeout=" + timeout +
                '}';
    }
}
