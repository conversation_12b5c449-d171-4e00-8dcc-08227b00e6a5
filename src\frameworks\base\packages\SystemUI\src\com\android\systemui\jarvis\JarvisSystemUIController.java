/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;

import com.android.systemui.CoreStartable;
import com.android.systemui.dagger.SysUISingleton;
import com.android.systemui.statusbar.CommandQueue;

import javax.inject.Inject;

/**
 * Main controller for Jarvis AI integration in SystemUI.
 * 
 * Manages:
 * - AI service connections
 * - Conversation overlay
 * - Status bar integration
 * - Quick settings integration
 * - Voice activation
 */
@SysUISingleton
public class JarvisSystemUIController implements CoreStartable, 
        JarvisOverlayController.OverlayListener,
        CommandQueue.Callbacks {
    
    private static final String TAG = "JarvisSystemUIController";
    private static final boolean DEBUG = true;
    
    private final Context mContext;
    private final CommandQueue mCommandQueue;
    private final Handler mMainHandler;
    
    // AI Service connections
    private JarvisServiceConnection mServiceConnection;
    
    // UI Components
    private JarvisOverlayController mOverlayController;
    private JarvisSuggestionPanel mSuggestionPanel;
    private SuggestionEngine mSuggestionEngine;
    private JarvisStatusBarController mStatusBarController;
    private JarvisStatusBarIntegration mStatusBarIntegration;
    private JarvisNotificationEnhancer mNotificationEnhancer;
    private JarvisQuickSettingsController mQuickSettingsController;
    
    // State
    private boolean mJarvisEnabled = true;
    private boolean mVoiceActivationEnabled = true;
    private boolean mIsProcessingRequest = false;
    
    @Inject
    public JarvisSystemUIController(Context context, CommandQueue commandQueue) {
        mContext = context;
        mCommandQueue = commandQueue;
        mMainHandler = new Handler(Looper.getMainLooper());
    }
    
    @Override
    public void start() {
        if (DEBUG) Log.d(TAG, "Starting Jarvis SystemUI Controller");
        
        // Initialize AI service connection
        initializeServiceConnection();
        
        // Initialize UI controllers
        initializeUIControllers();
        
        // Register with CommandQueue for system callbacks
        mCommandQueue.addCallback(this);
        
        if (DEBUG) Log.d(TAG, "Jarvis SystemUI Controller started");
    }
    
    private void initializeServiceConnection() {
        mServiceConnection = new JarvisServiceConnection(mContext);
        mServiceConnection.setConnectionListener(new JarvisServiceConnection.ConnectionListener() {
            @Override
            public void onServiceConnected() {
                if (DEBUG) Log.d(TAG, "AI services connected");
                updateJarvisAvailability(true);
            }
            
            @Override
            public void onServiceDisconnected() {
                if (DEBUG) Log.d(TAG, "AI services disconnected");
                updateJarvisAvailability(false);
            }
            
            @Override
            public void onAiResponse(String response) {
                handleAiResponse(response);
            }
            
            @Override
            public void onError(String error) {
                handleAiError(error);
            }
        });
        
        mServiceConnection.connect();
    }
    
    private void initializeUIControllers() {
        // Initialize overlay controller
        mOverlayController = new JarvisOverlayController(mContext);
        mOverlayController.setOverlayListener(this);

        // Initialize suggestion panel
        mSuggestionPanel = new JarvisSuggestionPanel(mContext);
        mSuggestionPanel.setSuggestionListener(new JarvisSuggestionPanel.SuggestionListener() {
            @Override
            public void onSuggestionSelected(AiSuggestion suggestion) {
                handleSuggestionSelection(suggestion);
            }

            @Override
            public void onSuggestionDismissed(AiSuggestion suggestion) {
                handleSuggestionDismissal(suggestion);
            }

            @Override
            public void onPanelDismissed() {
                hideSuggestionPanel();
            }

            @Override
            public void onRefreshRequested() {
                refreshSuggestions();
            }

            @Override
            public void onSuggestionFeedback(AiSuggestion suggestion, boolean helpful) {
                handleSuggestionFeedback(suggestion, helpful);
            }
        });

        // Initialize suggestion engine
        mSuggestionEngine = new SuggestionEngine(mContext);
        mSuggestionEngine.setSuggestionGenerationListener(new SuggestionEngine.SuggestionGenerationListener() {
            @Override
            public void onSuggestionsGenerated(List<AiSuggestion> suggestions) {
                mSuggestionPanel.updateSuggestions(suggestions);
            }

            @Override
            public void onSuggestionGenerationFailed(String error) {
                if (DEBUG) Log.d(TAG, "Suggestion generation failed: " + error);
            }
        });
        
        // Initialize status bar controller
        mStatusBarController = new JarvisStatusBarController(mContext);
        mStatusBarController.setStatusBarListener(new JarvisStatusBarController.StatusBarListener() {
            @Override
            public void onJarvisIconClicked() {
                showJarvisOverlay();
            }

            @Override
            public void onVoiceActivationTriggered() {
                if (mVoiceActivationEnabled) {
                    showJarvisOverlay();
                }
            }

            @Override
            public void onStatusBarLongPress() {
                showSuggestionPanel();
            }

            @Override
            public void onQuickSuggestionsRequested() {
                showSuggestionPanel();
            }
        });

        // Initialize status bar integration
        mStatusBarIntegration = new JarvisStatusBarIntegration(mContext, mStatusBarController);
        mStatusBarIntegration.setIntegrationListener(new JarvisStatusBarIntegration.IntegrationListener() {
            @Override
            public void onStatusBarIntegrated() {
                if (DEBUG) Log.d(TAG, "Status bar integrated successfully");
            }

            @Override
            public void onStatusBarDisintegrated() {
                if (DEBUG) Log.d(TAG, "Status bar disintegrated");
            }

            @Override
            public void onIconVisibilityChanged(boolean visible) {
                if (DEBUG) Log.d(TAG, "Status bar icons visibility: " + visible);
            }
        });

        // Initialize notification enhancer
        mNotificationEnhancer = new JarvisNotificationEnhancer(mContext);
        mNotificationEnhancer.setServiceConnection(mServiceConnection);
        mNotificationEnhancer.setEnhancementListener(new JarvisNotificationEnhancer.NotificationEnhancementListener() {
            @Override
            public void onNotificationEnhanced(String key, JarvisNotificationEnhancer.EnhancedNotification enhanced) {
                updateStatusBarForNotification(enhanced);
            }

            @Override
            public void onNotificationGroupCreated(JarvisNotificationEnhancer.NotificationGroup group) {
                if (DEBUG) Log.d(TAG, "Notification group created: " + group.groupTitle);
            }

            @Override
            public void onSmartActionGenerated(String key, List<JarvisNotificationEnhancer.SmartAction> actions) {
                if (DEBUG) Log.d(TAG, "Smart actions generated for: " + key);
            }

            @Override
            public void onNotificationSummaryGenerated(String summary) {
                updateStatusBarSummary(summary);
            }
        });
        
        // Initialize quick settings controller
        mQuickSettingsController = new JarvisQuickSettingsController(mContext);
        mQuickSettingsController.setQuickSettingsListener(new JarvisQuickSettingsController.QuickSettingsListener() {
            @Override
            public void onJarvisToggled(boolean enabled) {
                setJarvisEnabled(enabled);
            }
            
            @Override
            public void onVoiceActivationToggled(boolean enabled) {
                setVoiceActivationEnabled(enabled);
            }
            
            @Override
            public void onJarvisSettingsRequested() {
                openJarvisSettings();
            }
        });
    }
    
    public void showJarvisOverlay() {
        if (!mJarvisEnabled) {
            if (DEBUG) Log.d(TAG, "Jarvis is disabled, not showing overlay");
            return;
        }
        
        if (mOverlayController != null) {
            mOverlayController.showOverlay();
        }
    }
    
    public void hideJarvisOverlay() {
        if (mOverlayController != null) {
            mOverlayController.hideOverlay();
        }
    }
    
    public void setJarvisEnabled(boolean enabled) {
        mJarvisEnabled = enabled;
        
        // Update UI controllers
        if (mStatusBarController != null) {
            mStatusBarController.setJarvisEnabled(enabled);
        }
        
        if (mQuickSettingsController != null) {
            mQuickSettingsController.setJarvisEnabled(enabled);
        }
        
        // Hide overlay if disabled
        if (!enabled && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis enabled: " + enabled);
    }
    
    public void setVoiceActivationEnabled(boolean enabled) {
        mVoiceActivationEnabled = enabled;
        
        if (mStatusBarController != null) {
            mStatusBarController.setVoiceActivationEnabled(enabled);
        }
        
        if (DEBUG) Log.d(TAG, "Voice activation enabled: " + enabled);
    }
    
    private void updateJarvisAvailability(boolean available) {
        if (mStatusBarController != null) {
            mStatusBarController.setJarvisAvailable(available);
        }
        
        if (mQuickSettingsController != null) {
            mQuickSettingsController.setJarvisAvailable(available);
        }
        
        if (!available && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
    }
    
    private void handleAiResponse(String response) {
        mIsProcessingRequest = false;
        
        if (mOverlayController != null) {
            mOverlayController.addAiResponse(response);
            mOverlayController.setProcessingState(false);
        }
        
        if (DEBUG) Log.d(TAG, "AI response received: " + response);
    }
    
    private void handleAiError(String error) {
        mIsProcessingRequest = false;
        
        if (mOverlayController != null) {
            mOverlayController.addAiResponse("Sorry, I encountered an error: " + error);
            mOverlayController.setProcessingState(false);
        }
        
        Log.e(TAG, "AI error: " + error);
    }
    
    private void openJarvisSettings() {
        Intent intent = new Intent("android.settings.AI_ASSISTANT_SETTINGS");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        
        try {
            mContext.startActivity(intent);
        } catch (Exception e) {
            Log.w(TAG, "Could not open Jarvis settings", e);
        }
    }
    
    // JarvisOverlayController.OverlayListener implementation
    @Override
    public void onOverlayShown() {
        if (mStatusBarController != null) {
            mStatusBarController.setOverlayVisible(true);
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis overlay shown");
    }
    
    @Override
    public void onOverlayHidden() {
        if (mStatusBarController != null) {
            mStatusBarController.setOverlayVisible(false);
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis overlay hidden");
    }
    
    @Override
    public void onMessageSent(String message, boolean isVoice) {
        if (mServiceConnection == null || !mServiceConnection.isConnected()) {
            handleAiError("AI services not available");
            return;
        }
        
        mIsProcessingRequest = true;
        
        if (mOverlayController != null) {
            mOverlayController.setProcessingState(true);
        }
        
        // Send message to AI services
        mServiceConnection.sendMessage(message, isVoice);
        
        if (DEBUG) Log.d(TAG, "Message sent to AI: " + message + " (voice: " + isVoice + ")");
    }
    
    @Override
    public void onQuickActionTriggered(String actionId) {
        if (mServiceConnection == null || !mServiceConnection.isConnected()) {
            handleAiError("AI services not available");
            return;
        }
        
        // Handle quick actions
        switch (actionId) {
            case "help":
                onMessageSent("What can you do?", false);
                break;
            case "open_settings":
                openJarvisSettings();
                break;
            case "check_weather":
                onMessageSent("What's the weather like?", false);
                break;
            case "set_reminder":
                onMessageSent("Set a reminder", false);
                break;
            default:
                if (DEBUG) Log.d(TAG, "Unknown quick action: " + actionId);
                break;
        }
    }
    
    // CommandQueue.Callbacks implementation
    @Override
    public void onRecentsAnimationStateChanged(boolean running) {
        // Hide Jarvis overlay when recents animation starts
        if (running && mOverlayController != null && mOverlayController.isShowing()) {
            mOverlayController.hideOverlay();
        }
    }
    
    // Public API for other SystemUI components
    public boolean isJarvisEnabled() {
        return mJarvisEnabled;
    }
    
    public boolean isVoiceActivationEnabled() {
        return mVoiceActivationEnabled;
    }
    
    public boolean isJarvisAvailable() {
        return mServiceConnection != null && mServiceConnection.isConnected();
    }
    
    public boolean isOverlayShowing() {
        return mOverlayController != null && mOverlayController.isShowing();
    }
    
    public void triggerVoiceActivation() {
        if (mVoiceActivationEnabled && mJarvisEnabled) {
            showJarvisOverlay();
        }
    }

    // Suggestion panel management
    public void showSuggestionPanel() {
        if (mSuggestionPanel != null && mJarvisEnabled) {
            mSuggestionPanel.showPanel();
            refreshSuggestions();
        }
    }

    public void hideSuggestionPanel() {
        if (mSuggestionPanel != null) {
            mSuggestionPanel.hidePanel();
        }
    }

    public void refreshSuggestions() {
        if (mSuggestionEngine != null && mServiceConnection != null && mServiceConnection.isConnected()) {
            // Set AI services for suggestion engine
            try {
                mSuggestionEngine.setAiServices(
                    mServiceConnection.getContextService(),
                    mServiceConnection.getPersonalizationService()
                );
                mSuggestionEngine.generateSuggestions();
            } catch (Exception e) {
                if (DEBUG) Log.d(TAG, "Error refreshing suggestions: " + e.getMessage());
            }
        }
    }

    private void handleSuggestionSelection(AiSuggestion suggestion) {
        if (suggestion == null) return;

        // Execute the suggestion action
        String actionId = suggestion.getActionId();
        if (actionId != null) {
            executeSuggestionAction(actionId, suggestion.getActionData());
        }

        // Record usage for learning
        if (mServiceConnection != null && mServiceConnection.isConnected()) {
            Bundle interactionData = new Bundle();
            interactionData.putString("suggestion_id", suggestion.getId());
            interactionData.putString("action_id", actionId);
            interactionData.putString("suggestion_type", suggestion.getType().name());
            mServiceConnection.recordUserInteraction("suggestion_selected", interactionData);
        }

        if (DEBUG) Log.d(TAG, "Suggestion selected: " + suggestion.getTitle());
    }

    private void handleSuggestionDismissal(AiSuggestion suggestion) {
        if (suggestion == null) return;

        // Record dismissal for learning
        if (mServiceConnection != null && mServiceConnection.isConnected()) {
            Bundle interactionData = new Bundle();
            interactionData.putString("suggestion_id", suggestion.getId());
            interactionData.putString("suggestion_type", suggestion.getType().name());
            mServiceConnection.recordUserInteraction("suggestion_dismissed", interactionData);
        }

        if (DEBUG) Log.d(TAG, "Suggestion dismissed: " + suggestion.getTitle());
    }

    private void handleSuggestionFeedback(AiSuggestion suggestion, boolean helpful) {
        if (suggestion == null) return;

        // Record feedback for learning
        if (mServiceConnection != null && mServiceConnection.isConnected()) {
            Bundle interactionData = new Bundle();
            interactionData.putString("suggestion_id", suggestion.getId());
            interactionData.putString("suggestion_type", suggestion.getType().name());
            interactionData.putBoolean("helpful", helpful);
            mServiceConnection.recordUserInteraction("suggestion_feedback", interactionData);
        }

        if (DEBUG) Log.d(TAG, "Suggestion feedback: " + suggestion.getTitle() + " helpful=" + helpful);
    }

    private void executeSuggestionAction(String actionId, Bundle actionData) {
        if (actionId == null) return;

        switch (actionId) {
            case "check_weather":
                onMessageSent("What's the weather like?", false);
                break;
            case "open_calendar":
                openApp("com.android.calendar");
                break;
            case "open_messages":
                openApp("com.android.messaging");
                break;
            case "open_music":
                openApp("com.android.music");
                break;
            case "open_photos":
                openApp("com.google.android.apps.photos");
                break;
            case "enable_dnd":
                toggleDoNotDisturb(true);
                break;
            case "enable_battery_saver":
                toggleBatterySaver(true);
                break;
            case "open_wifi_settings":
                openSettings("android.settings.WIFI_SETTINGS");
                break;
            case "enable_focus_mode":
                toggleFocusMode(true);
                break;
            case "create_reminder":
                onMessageSent("Create a reminder", false);
                break;
            case "clean_storage":
                onMessageSent("Clean up storage", false);
                break;
            case "show_tips":
                onMessageSent("Show me some tips", false);
                break;
            default:
                if (DEBUG) Log.d(TAG, "Unknown suggestion action: " + actionId);
                break;
        }
    }

    private void openApp(String packageName) {
        try {
            Intent intent = mContext.getPackageManager().getLaunchIntentForPackage(packageName);
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                mContext.startActivity(intent);
            }
        } catch (Exception e) {
            Log.w(TAG, "Could not open app: " + packageName, e);
        }
    }

    private void openSettings(String action) {
        try {
            Intent intent = new Intent(action);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            mContext.startActivity(intent);
        } catch (Exception e) {
            Log.w(TAG, "Could not open settings: " + action, e);
        }
    }

    private void toggleDoNotDisturb(boolean enable) {
        // Implementation would interact with NotificationManager
        if (DEBUG) Log.d(TAG, "Toggle DND: " + enable);
    }

    private void toggleBatterySaver(boolean enable) {
        // Implementation would interact with PowerManager
        if (DEBUG) Log.d(TAG, "Toggle battery saver: " + enable);
    }

    private void toggleFocusMode(boolean enable) {
        // Implementation would interact with focus mode settings
        if (DEBUG) Log.d(TAG, "Toggle focus mode: " + enable);
    }

    // Status bar integration methods
    public void integrateWithStatusBar(Object statusBar) {
        if (mStatusBarIntegration != null) {
            // This would integrate with the actual StatusBar instance
            // mStatusBarIntegration.integrateWithStatusBar((StatusBar) statusBar);
            if (DEBUG) Log.d(TAG, "Integrating with status bar");
        }
    }

    private void updateStatusBarForNotification(JarvisNotificationEnhancer.EnhancedNotification enhanced) {
        if (mStatusBarController != null && enhanced != null) {
            // Update status bar based on notification priority
            if (enhanced.isImportant) {
                mStatusBarController.showAiActivity("processing");
            }

            // Update suggestion count if notification generates suggestions
            if (enhanced.smartActions != null && !enhanced.smartActions.isEmpty()) {
                mStatusBarController.setSuggestionCount(enhanced.smartActions.size());
            }
        }
    }

    private void updateStatusBarSummary(String summary) {
        if (mStatusBarController != null) {
            mStatusBarController.updateContextualInfo(summary);
        }
    }

    // Notification enhancement integration
    public void enhanceNotification(Object statusBarNotification) {
        if (mNotificationEnhancer != null) {
            // This would enhance the actual StatusBarNotification
            // mNotificationEnhancer.enhanceNotification((StatusBarNotification) statusBarNotification);
            if (DEBUG) Log.d(TAG, "Enhancing notification");
        }
    }

    public void removeNotification(String key) {
        if (mNotificationEnhancer != null) {
            mNotificationEnhancer.removeNotification(key);
        }
    }

    // Status bar state management
    public void onStatusBarStateChanged(int state) {
        if (mStatusBarIntegration != null) {
            mStatusBarIntegration.notifyStatusBarStateChanged(state);
        }

        // Update suggestion panel visibility based on status bar state
        if (state == 1 && mSuggestionPanel != null && mSuggestionPanel.isVisible()) {
            // Hide suggestions when notification shade is expanded
            mSuggestionPanel.hidePanel();
        }
    }

    public void onDarkModeChanged(boolean isDark) {
        if (mStatusBarIntegration != null) {
            mStatusBarIntegration.notifyDarkModeChanged(isDark);
        }
    }
    
    @Override
    public void dump(java.io.PrintWriter pw, String[] args) {
        pw.println("JarvisSystemUIController state:");
        pw.println("  mJarvisEnabled=" + mJarvisEnabled);
        pw.println("  mVoiceActivationEnabled=" + mVoiceActivationEnabled);
        pw.println("  mIsProcessingRequest=" + mIsProcessingRequest);
        pw.println("  isJarvisAvailable=" + isJarvisAvailable());
        pw.println("  isOverlayShowing=" + isOverlayShowing());
    }
}
