/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution.providers;

import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Slog;

/**
 * Base class for all action providers in Jarvis OS.
 * 
 * Provides common functionality and utilities for action execution,
 * parameter handling, and result creation.
 */
public abstract class BaseActionProvider implements IActionProvider {
    private static final String TAG = "BaseActionProvider";
    
    protected final Context mContext;
    private final String mActionType;
    
    public BaseActionProvider(Context context, String actionType) {
        mContext = context;
        mActionType = actionType;
    }
    
    /**
     * Get the action type this provider handles
     */
    protected String getActionType() {
        return mActionType;
    }
    
    /**
     * Abstract methods that must be implemented by subclasses
     */
    public abstract ActionResult executeAction(ActionRequest request) throws RemoteException;
    public abstract boolean canExecuteAction(ActionRequest request);
    public abstract ActionCapability getCapability();
    
    /**
     * Helper method to get string parameter from request
     */
    protected String getStringParameter(ActionRequest request, String key) {
        return getStringParameter(request, key, null);
    }
    
    /**
     * Helper method to get string parameter with default value
     */
    protected String getStringParameter(ActionRequest request, String key, String defaultValue) {
        if (request.parameters == null) {
            return defaultValue;
        }
        return request.parameters.getString(key, defaultValue);
    }
    
    /**
     * Helper method to get boolean parameter from request
     */
    protected boolean getBooleanParameter(ActionRequest request, String key) {
        return getBooleanParameter(request, key, false);
    }
    
    /**
     * Helper method to get boolean parameter with default value
     */
    protected boolean getBooleanParameter(ActionRequest request, String key, boolean defaultValue) {
        if (request.parameters == null) {
            return defaultValue;
        }
        return request.parameters.getBoolean(key, defaultValue);
    }
    
    /**
     * Helper method to get integer parameter from request
     */
    protected int getIntParameter(ActionRequest request, String key) {
        return getIntParameter(request, key, 0);
    }
    
    /**
     * Helper method to get integer parameter with default value
     */
    protected int getIntParameter(ActionRequest request, String key, int defaultValue) {
        if (request.parameters == null) {
            return defaultValue;
        }
        return request.parameters.getInt(key, defaultValue);
    }
    
    /**
     * Helper method to get long parameter from request
     */
    protected long getLongParameter(ActionRequest request, String key) {
        return getLongParameter(request, key, 0L);
    }
    
    /**
     * Helper method to get long parameter with default value
     */
    protected long getLongParameter(ActionRequest request, String key, long defaultValue) {
        if (request.parameters == null) {
            return defaultValue;
        }
        return request.parameters.getLong(key, defaultValue);
    }
    
    /**
     * Helper method to get bundle parameter from request
     */
    protected Bundle getBundleParameter(ActionRequest request, String key) {
        if (request.parameters == null) {
            return null;
        }
        return request.parameters.getBundle(key);
    }
    
    /**
     * Helper method to create successful action result
     */
    protected ActionResult createSuccessResult(String message) {
        return createSuccessResult(message, null);
    }
    
    /**
     * Helper method to create successful action result with data
     */
    protected ActionResult createSuccessResult(String message, Bundle results) {
        ActionResult result = new ActionResult();
        result.success = true;
        result.message = message;
        result.results = results != null ? results : new Bundle();
        result.executionTime = System.currentTimeMillis();
        return result;
    }
    
    /**
     * Helper method to create error action result
     */
    protected ActionResult createErrorResult(String errorMessage) {
        return createErrorResult(errorMessage, null);
    }
    
    /**
     * Helper method to create error action result with details
     */
    protected ActionResult createErrorResult(String errorMessage, Bundle errorDetails) {
        ActionResult result = new ActionResult();
        result.success = false;
        result.errorMessage = errorMessage;
        result.results = errorDetails != null ? errorDetails : new Bundle();
        result.executionTime = System.currentTimeMillis();
        return result;
    }
    
    /**
     * Helper method to validate required parameters
     */
    protected boolean validateRequiredParameters(ActionRequest request, String... requiredParams) {
        if (request.parameters == null) {
            return requiredParams.length == 0;
        }
        
        for (String param : requiredParams) {
            if (!request.parameters.containsKey(param)) {
                Slog.w(TAG, "Missing required parameter: " + param);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Helper method to log action execution
     */
    protected void logActionExecution(ActionRequest request, ActionResult result) {
        if (result.success) {
            Slog.i(TAG, "Action executed successfully: " + mActionType + 
                   " for package: " + request.packageName);
        } else {
            Slog.w(TAG, "Action execution failed: " + mActionType + 
                   " for package: " + request.packageName + 
                   ", error: " + result.errorMessage);
        }
    }
    
    /**
     * Helper method to check if action requires user confirmation
     */
    protected boolean requiresUserConfirmation(ActionRequest request) {
        // Default implementation - can be overridden by subclasses
        return false;
    }
    
    /**
     * Helper method to get user confirmation
     */
    protected boolean getUserConfirmation(ActionRequest request, String confirmationMessage) {
        // Default implementation - always returns true
        // In a real implementation, this would show a user dialog
        return true;
    }
    
    /**
     * Helper method to check permissions for action execution
     */
    protected boolean checkPermissions(ActionRequest request, String... permissions) {
        if (permissions == null || permissions.length == 0) {
            return true;
        }
        
        for (String permission : permissions) {
            if (mContext.checkCallingPermission(permission) != 
                android.content.pm.PackageManager.PERMISSION_GRANTED) {
                Slog.w(TAG, "Permission denied: " + permission + 
                       " for package: " + request.packageName);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Helper method to create action capability with common fields
     */
    protected ActionCapability createBaseCapability(String description, String[] permissions) {
        return createBaseCapability(description, permissions, false);
    }
    
    /**
     * Helper method to create action capability with all common fields
     */
    protected ActionCapability createBaseCapability(String description, String[] permissions, 
                                                   boolean requiresConfirmation) {
        ActionCapability capability = new ActionCapability();
        capability.actionType = mActionType;
        capability.description = description;
        capability.requiredPermissions = permissions != null ? permissions : new String[0];
        capability.requiresUserConfirmation = requiresConfirmation;
        capability.isSystemAction = true;
        capability.supportedParameters = new String[0]; // Override in subclasses
        return capability;
    }
    
    /**
     * Helper method to measure execution time
     */
    protected long measureExecutionTime(Runnable action) {
        long startTime = System.currentTimeMillis();
        action.run();
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * Helper method to safely execute action with timeout
     */
    protected ActionResult executeWithTimeout(ActionRequest request, long timeoutMs, 
                                            ActionExecutor executor) {
        try {
            // Simple timeout implementation
            // In production, this would use proper timeout mechanisms
            long startTime = System.currentTimeMillis();
            ActionResult result = executor.execute(request);
            long executionTime = System.currentTimeMillis() - startTime;
            
            if (executionTime > timeoutMs) {
                Slog.w(TAG, "Action execution exceeded timeout: " + executionTime + "ms > " + timeoutMs + "ms");
            }
            
            return result;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error executing action with timeout", e);
            return createErrorResult("Action execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Interface for action execution with timeout
     */
    protected interface ActionExecutor {
        ActionResult execute(ActionRequest request) throws Exception;
    }
    
    /**
     * Helper method to create parameter validation error
     */
    protected ActionResult createParameterValidationError(String parameterName, String expectedType) {
        return createErrorResult("Invalid parameter: " + parameterName + 
                               " (expected: " + expectedType + ")");
    }
    
    /**
     * Helper method to create permission error
     */
    protected ActionResult createPermissionError(String permission) {
        return createErrorResult("Permission denied: " + permission);
    }
    
    /**
     * Helper method to create timeout error
     */
    protected ActionResult createTimeoutError(long timeoutMs) {
        return createErrorResult("Action execution timed out after " + timeoutMs + "ms");
    }
}
