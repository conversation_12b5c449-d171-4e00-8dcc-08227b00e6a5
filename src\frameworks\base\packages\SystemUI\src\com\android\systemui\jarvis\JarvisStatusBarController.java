/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.android.systemui.R;
import com.android.systemui.statusbar.phone.StatusBar;

/**
 * Controller for Jarvis AI integration in the status bar.
 * 
 * Manages:
 * - AI status indicators and availability
 * - Quick access to Jarvis interface
 * - Context-aware status bar modifications
 * - AI activity visualization
 * - Voice activation triggers
 */
public class JarvisStatusBarController {
    private static final String TAG = "JarvisStatusBarController";
    private static final boolean DEBUG = true;
    
    private static final int ANIMATION_DURATION = 200;
    private static final int PULSE_ANIMATION_DURATION = 1000;
    private static final int STATUS_UPDATE_INTERVAL = 5000; // 5 seconds
    
    private final Context mContext;
    private final Handler mMainHandler;
    
    // Status bar components
    private ViewGroup mStatusBarContainer;
    private LinearLayout mJarvisStatusContainer;
    private ImageView mJarvisIcon;
    private ImageView mActivityIndicator;
    private TextView mStatusText;
    private View mVoiceActivationZone;
    
    // State management
    private boolean mJarvisEnabled = true;
    private boolean mJarvisAvailable = false;
    private boolean mVoiceActivationEnabled = true;
    private boolean mOverlayVisible = false;
    private boolean mIsProcessing = false;
    private boolean mHasSuggestions = false;
    private int mSuggestionCount = 0;
    
    // Animation state
    private ObjectAnimator mPulseAnimator;
    private ObjectAnimator mActivityAnimator;
    private boolean mIsAnimating = false;
    
    // Callbacks
    private StatusBarListener mStatusBarListener;
    
    public interface StatusBarListener {
        void onJarvisIconClicked();
        void onVoiceActivationTriggered();
        void onStatusBarLongPress();
        void onQuickSuggestionsRequested();
    }
    
    public JarvisStatusBarController(Context context) {
        mContext = context;
        mMainHandler = new Handler(Looper.getMainLooper());
        
        initializeStatusBarComponents();
        setupAnimations();
        
        if (DEBUG) Log.d(TAG, "JarvisStatusBarController initialized");
    }
    
    public void setStatusBarListener(StatusBarListener listener) {
        mStatusBarListener = listener;
    }
    
    private void initializeStatusBarComponents() {
        // This would typically be injected or obtained from StatusBar
        // For now, we'll create the components programmatically
        createJarvisStatusComponents();
        setupClickHandlers();
        updateVisibility();
    }
    
    private void createJarvisStatusComponents() {
        // Create main container for Jarvis status elements
        mJarvisStatusContainer = new LinearLayout(mContext);
        mJarvisStatusContainer.setOrientation(LinearLayout.HORIZONTAL);
        mJarvisStatusContainer.setPadding(8, 0, 8, 0);
        
        // Create Jarvis icon
        mJarvisIcon = new ImageView(mContext);
        mJarvisIcon.setImageResource(R.drawable.ic_jarvis_status);
        mJarvisIcon.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        mJarvisIcon.setContentDescription(mContext.getString(R.string.jarvis_status_icon));
        
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(
            mContext.getResources().getDimensionPixelSize(R.dimen.status_bar_icon_size),
            mContext.getResources().getDimensionPixelSize(R.dimen.status_bar_icon_size)
        );
        iconParams.setMarginEnd(4);
        
        // Create activity indicator
        mActivityIndicator = new ImageView(mContext);
        mActivityIndicator.setImageResource(R.drawable.ic_jarvis_activity);
        mActivityIndicator.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        mActivityIndicator.setVisibility(View.GONE);
        mActivityIndicator.setContentDescription(mContext.getString(R.string.jarvis_activity_indicator));
        
        LinearLayout.LayoutParams activityParams = new LinearLayout.LayoutParams(
            mContext.getResources().getDimensionPixelSize(R.dimen.status_bar_icon_size_small),
            mContext.getResources().getDimensionPixelSize(R.dimen.status_bar_icon_size_small)
        );
        activityParams.setMarginEnd(2);
        
        // Create status text (for suggestion count, etc.)
        mStatusText = new TextView(mContext);
        mStatusText.setTextSize(10);
        mStatusText.setTextColor(mContext.getColor(R.color.status_bar_text_color));
        mStatusText.setVisibility(View.GONE);
        
        // Create voice activation zone (invisible touch area)
        mVoiceActivationZone = new View(mContext);
        mVoiceActivationZone.setBackground(null);
        
        LinearLayout.LayoutParams voiceParams = new LinearLayout.LayoutParams(
            mContext.getResources().getDimensionPixelSize(R.dimen.jarvis_voice_zone_width),
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        
        // Add components to container
        mJarvisStatusContainer.addView(mJarvisIcon, iconParams);
        mJarvisStatusContainer.addView(mActivityIndicator, activityParams);
        mJarvisStatusContainer.addView(mStatusText);
        mJarvisStatusContainer.addView(mVoiceActivationZone, voiceParams);
        
        if (DEBUG) Log.d(TAG, "Jarvis status components created");
    }
    
    private void setupClickHandlers() {
        // Jarvis icon click
        mJarvisIcon.setOnClickListener(v -> {
            if (mStatusBarListener != null && mJarvisEnabled && mJarvisAvailable) {
                mStatusBarListener.onJarvisIconClicked();
                animateIconPress();
            }
        });
        
        // Jarvis icon long press
        mJarvisIcon.setOnLongClickListener(v -> {
            if (mStatusBarListener != null && mJarvisEnabled && mJarvisAvailable) {
                mStatusBarListener.onStatusBarLongPress();
                return true;
            }
            return false;
        });
        
        // Voice activation zone
        mVoiceActivationZone.setOnClickListener(v -> {
            if (mStatusBarListener != null && mVoiceActivationEnabled && mJarvisEnabled) {
                mStatusBarListener.onVoiceActivationTriggered();
                animateVoiceActivation();
            }
        });
        
        // Status text click (for quick suggestions)
        mStatusText.setOnClickListener(v -> {
            if (mStatusBarListener != null && mHasSuggestions) {
                mStatusBarListener.onQuickSuggestionsRequested();
            }
        });
    }
    
    private void setupAnimations() {
        // Create pulse animation for activity indicator
        mPulseAnimator = ObjectAnimator.ofFloat(mActivityIndicator, "alpha", 0.3f, 1.0f);
        mPulseAnimator.setDuration(PULSE_ANIMATION_DURATION);
        mPulseAnimator.setRepeatCount(ObjectAnimator.INFINITE);
        mPulseAnimator.setRepeatMode(ObjectAnimator.REVERSE);
        
        // Create activity animation for processing state
        mActivityAnimator = ObjectAnimator.ofFloat(mActivityIndicator, "rotation", 0f, 360f);
        mActivityAnimator.setDuration(1500);
        mActivityAnimator.setRepeatCount(ObjectAnimator.INFINITE);
        mActivityAnimator.setRepeatMode(ObjectAnimator.RESTART);
    }
    
    public void setJarvisEnabled(boolean enabled) {
        mJarvisEnabled = enabled;
        updateVisibility();
        updateIconState();
        
        if (DEBUG) Log.d(TAG, "Jarvis enabled: " + enabled);
    }
    
    public void setJarvisAvailable(boolean available) {
        mJarvisAvailable = available;
        updateIconState();
        
        if (!available) {
            setProcessingState(false);
        }
        
        if (DEBUG) Log.d(TAG, "Jarvis available: " + available);
    }
    
    public void setVoiceActivationEnabled(boolean enabled) {
        mVoiceActivationEnabled = enabled;
        updateVoiceZoneState();
        
        if (DEBUG) Log.d(TAG, "Voice activation enabled: " + enabled);
    }
    
    public void setOverlayVisible(boolean visible) {
        mOverlayVisible = visible;
        updateIconState();
        
        if (DEBUG) Log.d(TAG, "Overlay visible: " + visible);
    }
    
    public void setProcessingState(boolean processing) {
        mIsProcessing = processing;
        updateActivityIndicator();
        
        if (DEBUG) Log.d(TAG, "Processing state: " + processing);
    }
    
    public void setSuggestionCount(int count) {
        mSuggestionCount = count;
        mHasSuggestions = count > 0;
        updateStatusText();
        
        if (DEBUG) Log.d(TAG, "Suggestion count: " + count);
    }
    
    public void showAiActivity(String activityType) {
        if (!mJarvisEnabled || !mJarvisAvailable) return;
        
        // Show activity indicator with appropriate animation
        mActivityIndicator.setVisibility(View.VISIBLE);
        
        switch (activityType) {
            case "thinking":
                startPulseAnimation();
                break;
            case "processing":
                startActivityAnimation();
                break;
            case "listening":
                startListeningAnimation();
                break;
            default:
                startPulseAnimation();
                break;
        }
        
        if (DEBUG) Log.d(TAG, "Showing AI activity: " + activityType);
    }
    
    public void hideAiActivity() {
        stopAllAnimations();
        mActivityIndicator.setVisibility(View.GONE);
        
        if (DEBUG) Log.d(TAG, "Hiding AI activity");
    }
    
    private void updateVisibility() {
        if (mJarvisStatusContainer != null) {
            mJarvisStatusContainer.setVisibility(mJarvisEnabled ? View.VISIBLE : View.GONE);
        }
    }
    
    private void updateIconState() {
        if (mJarvisIcon == null) return;
        
        // Update icon based on state
        int iconResource;
        int iconColor;
        
        if (!mJarvisEnabled) {
            iconResource = R.drawable.ic_jarvis_disabled;
            iconColor = mContext.getColor(R.color.status_bar_icon_disabled);
        } else if (!mJarvisAvailable) {
            iconResource = R.drawable.ic_jarvis_unavailable;
            iconColor = mContext.getColor(R.color.status_bar_icon_unavailable);
        } else if (mOverlayVisible) {
            iconResource = R.drawable.ic_jarvis_active;
            iconColor = mContext.getColor(R.color.jarvis_active_color);
        } else {
            iconResource = R.drawable.ic_jarvis_status;
            iconColor = mContext.getColor(R.color.status_bar_icon_color);
        }
        
        mJarvisIcon.setImageResource(iconResource);
        mJarvisIcon.setColorFilter(iconColor);
        
        // Update alpha based on availability
        float alpha = (mJarvisEnabled && mJarvisAvailable) ? 1.0f : 0.5f;
        mJarvisIcon.setAlpha(alpha);
    }
    
    private void updateActivityIndicator() {
        if (mActivityIndicator == null) return;
        
        if (mIsProcessing && mJarvisEnabled && mJarvisAvailable) {
            mActivityIndicator.setVisibility(View.VISIBLE);
            startActivityAnimation();
        } else {
            stopAllAnimations();
            mActivityIndicator.setVisibility(View.GONE);
        }
    }
    
    private void updateStatusText() {
        if (mStatusText == null) return;
        
        if (mHasSuggestions && mJarvisEnabled && mJarvisAvailable) {
            mStatusText.setText(String.valueOf(mSuggestionCount));
            mStatusText.setVisibility(View.VISIBLE);
        } else {
            mStatusText.setVisibility(View.GONE);
        }
    }
    
    private void updateVoiceZoneState() {
        if (mVoiceActivationZone == null) return;
        
        mVoiceActivationZone.setEnabled(mVoiceActivationEnabled && mJarvisEnabled);
    }
    
    private void animateIconPress() {
        if (mIsAnimating) return;
        
        mIsAnimating = true;
        
        ObjectAnimator scaleDown = ObjectAnimator.ofFloat(mJarvisIcon, "scaleX", 1.0f, 0.8f);
        scaleDown.setDuration(100);
        
        ObjectAnimator scaleUp = ObjectAnimator.ofFloat(mJarvisIcon, "scaleX", 0.8f, 1.0f);
        scaleUp.setDuration(100);
        scaleUp.setStartDelay(100);
        
        scaleDown.start();
        scaleUp.start();
        
        scaleUp.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                mIsAnimating = false;
            }
        });
    }
    
    private void animateVoiceActivation() {
        if (mVoiceActivationZone == null) return;
        
        // Create ripple effect for voice activation
        ObjectAnimator ripple = ObjectAnimator.ofFloat(mVoiceActivationZone, "alpha", 0.0f, 0.3f, 0.0f);
        ripple.setDuration(300);
        ripple.start();
    }
    
    private void startPulseAnimation() {
        stopAllAnimations();
        if (mPulseAnimator != null) {
            mPulseAnimator.start();
        }
    }
    
    private void startActivityAnimation() {
        stopAllAnimations();
        if (mActivityAnimator != null) {
            mActivityAnimator.start();
        }
    }
    
    private void startListeningAnimation() {
        stopAllAnimations();
        
        // Create listening animation (scale pulse)
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(mActivityIndicator, "scaleX", 1.0f, 1.2f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(mActivityIndicator, "scaleY", 1.0f, 1.2f);
        
        scaleX.setDuration(800);
        scaleY.setDuration(800);
        scaleX.setRepeatCount(ObjectAnimator.INFINITE);
        scaleY.setRepeatCount(ObjectAnimator.INFINITE);
        scaleX.setRepeatMode(ObjectAnimator.REVERSE);
        scaleY.setRepeatMode(ObjectAnimator.REVERSE);
        
        scaleX.start();
        scaleY.start();
    }
    
    private void stopAllAnimations() {
        if (mPulseAnimator != null && mPulseAnimator.isRunning()) {
            mPulseAnimator.cancel();
        }
        if (mActivityAnimator != null && mActivityAnimator.isRunning()) {
            mActivityAnimator.cancel();
        }
        
        // Reset scales
        if (mActivityIndicator != null) {
            mActivityIndicator.setScaleX(1.0f);
            mActivityIndicator.setScaleY(1.0f);
            mActivityIndicator.setRotation(0f);
            mActivityIndicator.setAlpha(1.0f);
        }
    }
    
    // Public API for external status updates
    public void notifyAiThinking() {
        showAiActivity("thinking");
    }
    
    public void notifyAiProcessing() {
        showAiActivity("processing");
    }
    
    public void notifyAiListening() {
        showAiActivity("listening");
    }
    
    public void notifyAiIdle() {
        hideAiActivity();
    }
    
    public void updateContextualInfo(String info) {
        // Could be used to show contextual information in status bar
        if (DEBUG) Log.d(TAG, "Contextual info: " + info);
    }
    
    public ViewGroup getStatusBarContainer() {
        return mJarvisStatusContainer;
    }
    
    public void destroy() {
        stopAllAnimations();
        
        if (DEBUG) Log.d(TAG, "JarvisStatusBarController destroyed");
    }
}
