/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ANDROID_AI_PERFORMANCE_OPTIMIZER_H
#define ANDROID_AI_PERFORMANCE_OPTIMIZER_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Performance metrics structure
 */
typedef struct {
    uint64_t totalInferences;
    float averageProcessingTime;
    uint64_t peakMemoryUsage;
    float averageCpuUsage;
    uint32_t thermalThrottleEvents;
    float currentTemperature;
    uint64_t availableMemory;
    uint32_t cpuFrequency;
    bool thermalThrottling;
} PerformanceMetrics;

/**
 * Initialize the performance optimizer
 * 
 * @return true if successful, false otherwise
 */
bool ai_performance_optimizer_init(void);

/**
 * Cleanup the performance optimizer
 */
void ai_performance_optimizer_cleanup(void);

/**
 * Optimize system performance for AI inference
 * 
 * @return true if optimizations were applied, false otherwise
 */
bool ai_performance_optimize_for_inference(void);

/**
 * Record performance metrics from an inference operation
 * 
 * @param processing_time Processing time in milliseconds
 * @param memory_used Memory used in bytes
 * @param cpu_usage CPU usage percentage (0.0-100.0)
 */
void ai_performance_record_metrics(uint64_t processing_time, uint64_t memory_used, float cpu_usage);

/**
 * Get current performance metrics
 * 
 * @return Performance metrics structure
 */
PerformanceMetrics ai_performance_get_metrics(void);

/**
 * Enable or disable performance optimization
 * 
 * @param enable true to enable optimization, false to disable
 */
void ai_performance_enable_optimization(bool enable);

#ifdef __cplusplus
}
#endif

#endif // ANDROID_AI_PERFORMANCE_OPTIMIZER_H
