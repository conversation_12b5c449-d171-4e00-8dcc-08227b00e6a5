/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

/**
 * Base class for a remotable object, the core part of a lightweight
 * remote procedure call mechanism designed for high performance when
 * performing in-process and cross-process calls.
 */
public class Binder implements IBinder {
    
    /**
     * Return the ID of the process that sent you the current transaction
     * that is being processed.
     */
    public static final int getCallingPid() {
        return 1000; // Mock implementation
    }

    /**
     * Return the Linux uid assigned to the process that sent you the
     * current transaction that is being processed.
     */
    public static final int getCallingUid() {
        return 1000; // Mock implementation
    }

    /**
     * Return the UserHandle assigned to the process that sent you the
     * current transaction that is being processed.
     */
    public static final UserHandle getCallingUserHandle() {
        return new UserHandle(0); // Mock implementation
    }

    /**
     * Reset the identity of the incoming IPC on the current thread.
     */
    public static final long clearCallingIdentity() {
        return 0; // Mock implementation
    }

    /**
     * Restore the identity of the incoming IPC on the current thread
     * back to a previously identity that was returned by clearCallingIdentity().
     */
    public static final void restoreCallingIdentity(long token) {
        // Mock implementation
    }

    /**
     * Flush any Binder commands pending in the current thread to the kernel
     * driver.
     */
    public static final void flushPendingCommands() {
        // Mock implementation
    }

    /**
     * Add the calling thread to the IPC thread pool.
     */
    public static final void joinThreadPool() {
        // Mock implementation
    }

    @Override
    public String getInterfaceDescriptor() throws RemoteException {
        return null;
    }

    @Override
    public boolean pingBinder() {
        return true;
    }

    @Override
    public boolean isBinderAlive() {
        return true;
    }

    @Override
    public IInterface queryLocalInterface(String descriptor) {
        return null;
    }

    @Override
    public void dump(java.io.FileDescriptor fd, String[] args) throws RemoteException {
        // Mock implementation
    }

    @Override
    public void dumpAsync(java.io.FileDescriptor fd, String[] args) throws RemoteException {
        // Mock implementation
    }

    @Override
    public boolean transact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
        return false; // Mock implementation
    }

    @Override
    public void linkToDeath(DeathRecipient recipient, int flags) throws RemoteException {
        // Mock implementation
    }

    @Override
    public boolean unlinkToDeath(DeathRecipient recipient, int flags) {
        return false; // Mock implementation
    }

    /**
     * Mock UserHandle class
     */
    public static class UserHandle {
        private final int mHandle;

        public UserHandle(int handle) {
            mHandle = handle;
        }

        public int getIdentifier() {
            return mHandle;
        }
    }

    /**
     * Interface for receiving a callback when the process hosting an IBinder has gone away.
     */
    public interface DeathRecipient {
        public void binderDied();
    }
}
