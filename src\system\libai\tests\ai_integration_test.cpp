/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <thread>
#include <chrono>

#include "ai_inference.h"
#include "ai_security.h"
#include "ai_ipc.h"
#include "ai_context.h"

using namespace std::chrono_literals;

class AiIntegrationTest : public ::testing::Test {
protected:
    void SetUp() override {
        // Initialize all AI libraries
        ai_inference_config_t inference_config = {};
        inference_config.hardware = AI_HARDWARE_AUTO;
        inference_config.use_quantization = true;
        inference_config.max_batch_size = 1;
        inference_config.num_threads = 2;
        inference_config.confidence_threshold = 0.5f;
        inference_config.enable_profiling = true;
        
        ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_inference_init(&inference_config));
        
        ai_crypto_config_t security_config = {};
        security_config.algorithm = AI_ENCRYPTION_AES_256_GCM;
        security_config.kdf = AI_KDF_PBKDF2_SHA256;
        security_config.key_storage = AI_STORAGE_KEYSTORE;
        security_config.use_hardware_backing = false; // Use software for testing
        
        ASSERT_EQ(AI_SECURITY_SUCCESS, ai_security_init(&security_config));
        ASSERT_EQ(AI_IPC_SUCCESS, ai_ipc_init());
        ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_init());
    }
    
    void TearDown() override {
        ai_context_cleanup();
        ai_ipc_cleanup();
        ai_security_cleanup();
        ai_inference_cleanup();
    }
};

TEST_F(AiIntegrationTest, InferenceBasicOperations) {
    // Test basic inference operations
    ai_model_t* model = nullptr;
    
    // Load a test model
    const char test_model_data[] = "test_model_data_placeholder";
    ASSERT_EQ(AI_INFERENCE_SUCCESS, 
              ai_model_load_from_buffer(test_model_data, sizeof(test_model_data), 
                                       AI_MODEL_TYPE_CONTEXT_ANALYSIS, &model));
    ASSERT_NE(nullptr, model);
    
    // Get model info
    ai_model_info_t model_info;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_model_get_info(model, &model_info));
    EXPECT_EQ(AI_MODEL_TYPE_CONTEXT_ANALYSIS, model_info.type);
    
    // Create inference session
    ai_inference_session_t* session = nullptr;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_create(model, nullptr, &session));
    ASSERT_NE(nullptr, session);
    
    // Run inference
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_run(session));
    
    // Get performance metrics
    ai_performance_metrics_t metrics;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_get_metrics(session, &metrics));
    EXPECT_GT(metrics.inference_time_ms, 0.0f);
    
    // Cleanup
    ai_session_destroy(session);
    ai_model_unload(model);
}

TEST_F(AiIntegrationTest, SecurityOperations) {
    // Test security operations
    ai_key_params_t key_params = {};
    key_params.key_size_bits = 256;
    key_params.protection_level = AI_DATA_LEVEL_PERSONAL;
    key_params.exportable = false;
    key_params.key_alias = "test_key";
    key_params.validity_duration_seconds = 3600;
    
    ai_key_handle_t* key_handle = nullptr;
    ASSERT_EQ(AI_SECURITY_SUCCESS, ai_key_generate(&key_params, &key_handle));
    ASSERT_NE(nullptr, key_handle);
    
    // Create crypto context
    ai_crypto_config_t crypto_config = {};
    crypto_config.algorithm = AI_ENCRYPTION_AES_256_GCM;
    
    ai_crypto_context_t* crypto_context = nullptr;
    ASSERT_EQ(AI_SECURITY_SUCCESS, 
              ai_crypto_context_create(key_handle, &crypto_config, &crypto_context));
    ASSERT_NE(nullptr, crypto_context);
    
    // Test encryption/decryption
    const char plaintext[] = "Hello, Jarvis OS AI Security!";
    uint8_t ciphertext[256];
    uint8_t decrypted[256];
    size_t ciphertext_size, decrypted_size;
    
    uint8_t iv[16];
    ASSERT_EQ(AI_SECURITY_SUCCESS, ai_random_bytes(iv, sizeof(iv)));
    
    ai_cipher_params_t cipher_params = {};
    cipher_params.iv = iv;
    cipher_params.iv_size = sizeof(iv);
    
    // Encrypt
    ASSERT_EQ(AI_SECURITY_SUCCESS, 
              ai_encrypt(crypto_context, 
                        reinterpret_cast<const uint8_t*>(plaintext), 
                        strlen(plaintext),
                        &cipher_params,
                        ciphertext, sizeof(ciphertext), &ciphertext_size));
    
    // Decrypt
    ASSERT_EQ(AI_SECURITY_SUCCESS,
              ai_decrypt(crypto_context,
                        ciphertext, ciphertext_size,
                        &cipher_params,
                        decrypted, sizeof(decrypted), &decrypted_size));
    
    // Verify decryption
    ASSERT_EQ(strlen(plaintext), decrypted_size);
    EXPECT_EQ(0, memcmp(plaintext, decrypted, decrypted_size));
    
    // Cleanup
    ai_crypto_context_destroy(crypto_context);
    ai_key_release(key_handle);
}

TEST_F(AiIntegrationTest, IpcCommunication) {
    // Test IPC communication
    ai_ipc_config_t ipc_config = {};
    ipc_config.type = AI_IPC_CONNECTION_SOCKET;
    ipc_config.endpoint_name = "/tmp/test_ai_ipc";
    ipc_config.max_message_size = 1024;
    ipc_config.queue_size = 10;
    ipc_config.timeout_ms = 1000;
    
    ai_ipc_connection_t* connection = nullptr;
    ASSERT_EQ(AI_IPC_SUCCESS, ai_ipc_connection_create(&ipc_config, &connection));
    ASSERT_NE(nullptr, connection);
    
    // Create test message
    const char test_payload[] = "Test AI IPC message";
    ai_ipc_message_t* message = nullptr;
    ASSERT_EQ(AI_IPC_SUCCESS, 
              ai_ipc_message_create(AI_IPC_MSG_CONTEXT_UPDATE, AI_IPC_PRIORITY_NORMAL,
                                   test_payload, strlen(test_payload), &message));
    ASSERT_NE(nullptr, message);
    
    // Test message properties
    EXPECT_EQ(AI_IPC_MSG_CONTEXT_UPDATE, message->type);
    EXPECT_EQ(AI_IPC_PRIORITY_NORMAL, message->priority);
    EXPECT_EQ(strlen(test_payload), message->payload_size);
    
    // Get metrics
    ai_ipc_metrics_t metrics;
    ASSERT_EQ(AI_IPC_SUCCESS, ai_ipc_get_metrics(connection, &metrics));
    
    // Cleanup
    ai_ipc_message_destroy(message);
    ai_ipc_connection_destroy(connection);
}

TEST_F(AiIntegrationTest, ContextProcessing) {
    // Test context processing
    ai_context_processor_t* processor = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_processor_create(&processor));
    ASSERT_NE(nullptr, processor);
    
    // Create test context data
    const char test_context[] = R"({
        "event_type": "app_launch",
        "package_name": "com.example.test",
        "timestamp": 1234567890,
        "confidence": 0.95
    })";
    
    ai_context_data_t* processed_data = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS,
              ai_context_process(processor, test_context, strlen(test_context),
                               AI_CONTEXT_TYPE_APP_STATE, &processed_data));
    ASSERT_NE(nullptr, processed_data);
    
    // Verify processed data
    EXPECT_EQ(AI_CONTEXT_TYPE_APP_STATE, processed_data->type);
    EXPECT_EQ(AI_CONTEXT_FORMAT_JSON, processed_data->format);
    EXPECT_GT(processed_data->timestamp, 0);
    
    // Test context fusion
    ai_context_fusion_config_t fusion_config = {};
    fusion_config.confidence_threshold = 0.5f;
    fusion_config.max_context_age_ms = 60000;
    fusion_config.enable_temporal_fusion = true;
    fusion_config.temporal_weight = 1.0f;
    
    ai_context_fusion_t* fusion = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_fusion_create(&fusion_config, &fusion));
    ASSERT_NE(nullptr, fusion);
    
    // Add context data to fusion
    ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_fusion_add_data(fusion, processed_data));
    
    // Perform analysis
    ai_context_analysis_t* analysis = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_fusion_analyze(fusion, &analysis));
    ASSERT_NE(nullptr, analysis);
    
    EXPECT_GT(analysis->overall_confidence, 0.0f);
    EXPECT_EQ(1, analysis->num_sources);
    
    // Cleanup
    ai_context_fusion_destroy(fusion);
    ai_context_data_destroy(processed_data);
    ai_context_processor_destroy(processor);
}

TEST_F(AiIntegrationTest, CrossLibraryIntegration) {
    // Test integration between multiple libraries
    
    // 1. Create secure context data using security library
    ai_secure_buffer_t* secure_buffer = nullptr;
    ASSERT_EQ(AI_SECURITY_SUCCESS, 
              ai_secure_buffer_create(1024, AI_DATA_LEVEL_SENSITIVE, &secure_buffer));
    ASSERT_NE(nullptr, secure_buffer);
    
    // 2. Process context data
    const char context_json[] = R"({
        "sensor_data": "encrypted_sensor_readings",
        "location": "home",
        "activity": "working"
    })";
    
    ai_context_processor_t* processor = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_processor_create(&processor));
    
    ai_context_data_t* context_data = nullptr;
    ASSERT_EQ(AI_CONTEXT_SUCCESS,
              ai_context_process(processor, context_json, strlen(context_json),
                               AI_CONTEXT_TYPE_SENSOR, &context_data));
    
    // 3. Use inference for context analysis
    ai_model_t* model = nullptr;
    const char model_data[] = "context_analysis_model";
    ASSERT_EQ(AI_INFERENCE_SUCCESS,
              ai_model_load_from_buffer(model_data, sizeof(model_data),
                                       AI_MODEL_TYPE_CONTEXT_ANALYSIS, &model));
    
    ai_inference_session_t* session = nullptr;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_create(model, nullptr, &session));
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_run(session));
    
    // 4. Send results via IPC
    ai_ipc_config_t ipc_config = {};
    ipc_config.type = AI_IPC_CONNECTION_SOCKET;
    ipc_config.endpoint_name = "/tmp/ai_integration_test";
    ipc_config.max_message_size = 2048;
    
    ai_ipc_connection_t* connection = nullptr;
    ASSERT_EQ(AI_IPC_SUCCESS, ai_ipc_connection_create(&ipc_config, &connection));
    
    ai_ipc_message_t* message = nullptr;
    const char result_data[] = "AI analysis complete";
    ASSERT_EQ(AI_IPC_SUCCESS,
              ai_ipc_message_create(AI_IPC_MSG_TASK_RESPONSE, AI_IPC_PRIORITY_HIGH,
                                   result_data, strlen(result_data), &message));
    
    // Verify cross-library integration worked
    EXPECT_NE(nullptr, secure_buffer);
    EXPECT_NE(nullptr, context_data);
    EXPECT_NE(nullptr, session);
    EXPECT_NE(nullptr, message);
    
    // Cleanup
    ai_ipc_message_destroy(message);
    ai_ipc_connection_destroy(connection);
    ai_session_destroy(session);
    ai_model_unload(model);
    ai_context_data_destroy(context_data);
    ai_context_processor_destroy(processor);
    ai_secure_buffer_destroy(secure_buffer);
}

TEST_F(AiIntegrationTest, PerformanceBenchmark) {
    // Performance benchmark test
    const int num_iterations = 100;
    
    // Benchmark inference performance
    ai_model_t* model = nullptr;
    const char model_data[] = "benchmark_model";
    ASSERT_EQ(AI_INFERENCE_SUCCESS,
              ai_model_load_from_buffer(model_data, sizeof(model_data),
                                       AI_MODEL_TYPE_CONTEXT_ANALYSIS, &model));
    
    ai_inference_session_t* session = nullptr;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_create(model, nullptr, &session));
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < num_iterations; i++) {
        ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_run(session));
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    float avg_inference_time = static_cast<float>(duration.count()) / num_iterations;
    
    // Performance expectations
    EXPECT_LT(avg_inference_time, 100.0f); // Should be under 100ms average
    
    std::cout << "Average inference time: " << avg_inference_time << "ms" << std::endl;
    
    // Get final metrics
    ai_performance_metrics_t metrics;
    ASSERT_EQ(AI_INFERENCE_SUCCESS, ai_session_get_metrics(session, &metrics));
    
    std::cout << "Final metrics:" << std::endl;
    std::cout << "  Inference time: " << metrics.inference_time_ms << "ms" << std::endl;
    std::cout << "  Memory used: " << metrics.memory_used_bytes << " bytes" << std::endl;
    std::cout << "  CPU usage: " << metrics.cpu_usage_percent << "%" << std::endl;
    
    // Cleanup
    ai_session_destroy(session);
    ai_model_unload(model);
}

TEST_F(AiIntegrationTest, ConcurrentOperations) {
    // Test concurrent operations across libraries
    const int num_threads = 4;
    std::vector<std::thread> threads;
    std::atomic<int> success_count{0};
    
    for (int i = 0; i < num_threads; i++) {
        threads.emplace_back([&, i]() {
            // Each thread performs different operations
            switch (i % 4) {
                case 0: {
                    // Inference operations
                    ai_model_t* model = nullptr;
                    const char model_data[] = "concurrent_model";
                    if (ai_model_load_from_buffer(model_data, sizeof(model_data),
                                                 AI_MODEL_TYPE_CONTEXT_ANALYSIS, &model) == AI_INFERENCE_SUCCESS) {
                        ai_inference_session_t* session = nullptr;
                        if (ai_session_create(model, nullptr, &session) == AI_INFERENCE_SUCCESS) {
                            if (ai_session_run(session) == AI_INFERENCE_SUCCESS) {
                                success_count++;
                            }
                            ai_session_destroy(session);
                        }
                        ai_model_unload(model);
                    }
                    break;
                }
                case 1: {
                    // Security operations
                    uint8_t random_data[32];
                    if (ai_random_bytes(random_data, sizeof(random_data)) == AI_SECURITY_SUCCESS) {
                        success_count++;
                    }
                    break;
                }
                case 2: {
                    // Context processing
                    ai_context_processor_t* processor = nullptr;
                    if (ai_context_processor_create(&processor) == AI_CONTEXT_SUCCESS) {
                        const char context[] = R"({"test": "concurrent"})";
                        ai_context_data_t* data = nullptr;
                        if (ai_context_process(processor, context, strlen(context),
                                             AI_CONTEXT_TYPE_APP_STATE, &data) == AI_CONTEXT_SUCCESS) {
                            success_count++;
                            ai_context_data_destroy(data);
                        }
                        ai_context_processor_destroy(processor);
                    }
                    break;
                }
                case 3: {
                    // IPC operations
                    ai_ipc_config_t config = {};
                    config.type = AI_IPC_CONNECTION_SOCKET;
                    config.endpoint_name = "/tmp/concurrent_test";
                    config.max_message_size = 512;
                    
                    ai_ipc_connection_t* connection = nullptr;
                    if (ai_ipc_connection_create(&config, &connection) == AI_IPC_SUCCESS) {
                        ai_ipc_message_t* message = nullptr;
                        const char payload[] = "concurrent test";
                        if (ai_ipc_message_create(AI_IPC_MSG_CUSTOM, AI_IPC_PRIORITY_NORMAL,
                                                 payload, strlen(payload), &message) == AI_IPC_SUCCESS) {
                            success_count++;
                            ai_ipc_message_destroy(message);
                        }
                        ai_ipc_connection_destroy(connection);
                    }
                    break;
                }
            }
        });
    }
    
    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }
    
    // All operations should succeed
    EXPECT_EQ(num_threads, success_count.load());
}

// Test fixture for stress testing
class AiStressTest : public AiIntegrationTest {
protected:
    static constexpr int STRESS_ITERATIONS = 1000;
    static constexpr int STRESS_THREADS = 8;
};

TEST_F(AiStressTest, MemoryLeakTest) {
    // Test for memory leaks under stress
    for (int i = 0; i < STRESS_ITERATIONS; i++) {
        // Create and destroy objects rapidly
        ai_context_processor_t* processor = nullptr;
        ASSERT_EQ(AI_CONTEXT_SUCCESS, ai_context_processor_create(&processor));
        
        ai_secure_buffer_t* buffer = nullptr;
        ASSERT_EQ(AI_SECURITY_SUCCESS, 
                  ai_secure_buffer_create(1024, AI_DATA_LEVEL_PUBLIC, &buffer));
        
        // Cleanup
        ai_secure_buffer_destroy(buffer);
        ai_context_processor_destroy(processor);
        
        // Periodic check (every 100 iterations)
        if (i % 100 == 0) {
            std::this_thread::sleep_for(1ms); // Allow cleanup
        }
    }
    
    // Test should complete without crashes or excessive memory usage
    SUCCEED();
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
