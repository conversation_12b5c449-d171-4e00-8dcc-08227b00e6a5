/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai;

import android.ai.FeedbackData;
import android.ai.IAiPersonalization;
import android.ai.LearningModel;
import android.ai.Recommendation;
import android.ai.UserInteraction;
import android.ai.UserProfile;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.RemoteException;
import android.util.Log;
import android.util.Slog;

import com.android.server.SystemService;
import com.android.server.ai.personalization.UserProfileManager;
import com.android.server.ai.personalization.OnDeviceLearning;
import com.android.server.ai.personalization.PreferenceStorage;
import com.android.server.ai.personalization.ModelManager;
import com.android.server.ai.personalization.RecommendationEngine;
import com.android.server.ai.security.AiSecurityManager;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * System service for AI personalization in Jarvis OS.
 * 
 * Manages user profiles, learns from interactions, adapts AI behavior,
 * and provides personalized recommendations while preserving privacy.
 */
public class AiPersonalizationService extends SystemService {
    private static final String TAG = "AiPersonalizationService";
    private static final boolean DEBUG = true;

    private final Object mLock = new Object();
    
    private UserProfileManager mProfileManager;
    private OnDeviceLearning mLearningEngine;
    private PreferenceStorage mPreferenceStorage;
    private ModelManager mModelManager;
    private RecommendationEngine mRecommendationEngine;
    private AiSecurityManager mSecurityManager;
    private Handler mHandler;
    private HandlerThread mHandlerThread;
    
    // Learning state - Fixed type declarations
    private final ConcurrentHashMap<String, UserInteraction> mPendingInteractions = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LearningModel> mActiveModels = new ConcurrentHashMap<>();
    
    // Statistics
    private long mTotalInteractions = 0;
    private long mTotalRecommendations = 0;
    private long mSuccessfulRecommendations = 0;
    private long mLastModelUpdate = 0;
    
    private volatile boolean mServiceEnabled = true;
    private volatile boolean mLearningEnabled = true;

    public AiPersonalizationService(Context context) {
        super(context);
    }

    @Override
    public void onStart() {
        if (DEBUG) Slog.d(TAG, "Starting AiPersonalizationService");
        
        // Initialize handler thread for background processing
        mHandlerThread = new HandlerThread("AiPersonalization");
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper());
        
        // Initialize core components
        mSecurityManager = new AiSecurityManager(getContext());
        mPreferenceStorage = new PreferenceStorage(getContext(), mSecurityManager);
        mProfileManager = new UserProfileManager(getContext(), mPreferenceStorage, mSecurityManager);
        mModelManager = new ModelManager(getContext(), mSecurityManager);
        mLearningEngine = new OnDeviceLearning(getContext(), mModelManager, mSecurityManager);
        mRecommendationEngine = new RecommendationEngine(getContext(), mProfileManager, mModelManager);
        
        // Publish the service
        publishBinderService(Context.AI_PERSONALIZATION_SERVICE, new AiPersonalizationImpl());
        
        // Initialize user profiles and models
        mHandler.post(() -> {
            initializePersonalization();
            if (DEBUG) Slog.d(TAG, "Personalization initialized");
        });
    }

    @Override
    public void onBootPhase(int phase) {
        if (phase == PHASE_SYSTEM_SERVICES_READY) {
            // Initialize connections to other system services
            mProfileManager.initializeSystemServiceConnections();
        } else if (phase == PHASE_BOOT_COMPLETED) {
            // Start learning and recommendation engines
            mLearningEngine.startLearning();
            mRecommendationEngine.startRecommendationGeneration();
        }
    }

    private final class AiPersonalizationImpl extends IAiPersonalization.Stub {
        
        @Override
        public UserProfile getUserProfile() throws RemoteException {
            enforcePersonalizationPermission("getUserProfile");
            
            String callingPackage = getCallingPackage();
            return mProfileManager.getUserProfile(callingPackage);
        }

        @Override
        public void updatePreference(String key, Bundle value) throws RemoteException {
            enforcePersonalizationPermission("updatePreference");
            
            String callingPackage = getCallingPackage();
            
            // Validate preference key and value
            if (key == null || value == null) {
                throw new IllegalArgumentException("Preference key and value cannot be null");
            }
            
            // Update preference
            mPreferenceStorage.updatePreference(callingPackage, key, value);
            
            // Trigger model update if needed
            mHandler.post(() -> {
                if (shouldUpdateModels(key)) {
                    mLearningEngine.updateModelsForPreferenceChange(key, value);
                }
            });
            
            if (DEBUG) Slog.d(TAG, "Preference updated: " + key + " for package: " + callingPackage);
        }

        @Override
        public LearningModel getPersonalizedModel(String modelType) throws RemoteException {
            enforcePersonalizationPermission("getPersonalizedModel");
            
            String callingPackage = getCallingPackage();
            
            // Get or create personalized model
            Object modelObj = mModelManager.getPersonalizedModel(modelType, callingPackage);
            LearningModel model = (LearningModel) modelObj;

            if (model == null) {
                // Create new model if it doesn't exist
                Object newModelObj = mLearningEngine.createPersonalizedModel(modelType, callingPackage);
                model = (LearningModel) newModelObj;
                if (model != null) {
                    mActiveModels.put(modelType + "_" + callingPackage, model);
                }
            }
            
            return model;
        }

        @Override
        public void recordUserInteraction(UserInteraction interaction) throws RemoteException {
            enforcePersonalizationPermission("recordUserInteraction");
            
            if (interaction == null) {
                throw new IllegalArgumentException("User interaction cannot be null");
            }
            
            String callingPackage = getCallingPackage();
            String interactionId = generateInteractionId(callingPackage, interaction);
            
            // Store interaction for processing
            mPendingInteractions.put(interactionId, interaction);
            
            // Process interaction asynchronously
            mHandler.post(() -> {
                processUserInteraction(interaction, callingPackage);
                mPendingInteractions.remove(interactionId);
            });
            
            mTotalInteractions++;
            
            if (DEBUG) Slog.d(TAG, "User interaction recorded: " + interaction.interactionType);
        }

        @Override
        public List<Recommendation> getRecommendations(String category, android.ai.ContextSnapshot context) 
                throws RemoteException {
            enforcePersonalizationPermission("getRecommendations");
            
            String callingPackage = getCallingPackage();
            
            // Generate personalized recommendations
            List<Recommendation> recommendations = mRecommendationEngine.generateRecommendations(
                category, context, callingPackage);
            
            mTotalRecommendations += recommendations.size();
            
            if (DEBUG) Slog.d(TAG, "Generated " + recommendations.size() + 
                " recommendations for category: " + category);
            
            return recommendations;
        }

        @Override
        public void provideFeedback(String modelType, FeedbackData feedback) throws RemoteException {
            enforcePersonalizationPermission("provideFeedback");
            
            if (feedback == null) {
                throw new IllegalArgumentException("Feedback data cannot be null");
            }
            
            String callingPackage = getCallingPackage();
            
            // Process feedback asynchronously
            mHandler.post(() -> {
                processFeedback(modelType, feedback, callingPackage);
            });
            
            // Update recommendation success rate
            if (feedback.rating >= 4) { // Consider rating 4+ as successful
                mSuccessfulRecommendations++;
            }
            
            if (DEBUG) Slog.d(TAG, "Feedback received for model: " + modelType + 
                ", rating: " + feedback.rating);
        }

        @Override
        public void resetPersonalizationData(String dataType) throws RemoteException {
            enforcePersonalizationPermission("resetPersonalizationData");
            
            String callingPackage = getCallingPackage();
            
            // Reset specified data type
            mHandler.post(() -> {
                switch (dataType) {
                    case "preferences":
                        mPreferenceStorage.resetPreferences(callingPackage);
                        break;
                    case "models":
                        mModelManager.resetModels(callingPackage);
                        break;
                    case "interactions":
                        mLearningEngine.resetInteractionHistory(callingPackage);
                        break;
                    case "all":
                        resetAllPersonalizationData(callingPackage);
                        break;
                    default:
                        Slog.w(TAG, "Unknown data type for reset: " + dataType);
                }
            });
            
            if (DEBUG) Slog.d(TAG, "Personalization data reset: " + dataType + 
                " for package: " + callingPackage);
        }

        @Override
        public Bundle exportPersonalizationData() throws RemoteException {
            enforcePersonalizationPermission("exportPersonalizationData");
            
            String callingPackage = getCallingPackage();
            
            Bundle exportData = new Bundle();
            
            // Export user profile
            UserProfile profile = mProfileManager.getUserProfile(callingPackage);
            if (profile != null) {
                exportData.putBundle("user_profile", profile.preferences);
            }
            
            // Export preferences
            Bundle preferences = mPreferenceStorage.exportPreferences(callingPackage);
            exportData.putBundle("preferences", preferences);
            
            // Export model metadata (not the actual models for security)
            Bundle modelMetadata = mModelManager.exportModelMetadata(callingPackage);
            exportData.putBundle("model_metadata", modelMetadata);
            
            exportData.putLong("export_timestamp", System.currentTimeMillis());
            
            if (DEBUG) Slog.d(TAG, "Personalization data exported for package: " + callingPackage);
            
            return exportData;
        }

        @Override
        public boolean importPersonalizationData(Bundle data) throws RemoteException {
            enforcePersonalizationPermission("importPersonalizationData");
            
            if (data == null) {
                throw new IllegalArgumentException("Import data cannot be null");
            }
            
            String callingPackage = getCallingPackage();
            
            try {
                // Import preferences
                Bundle preferences = data.getBundle("preferences");
                if (preferences != null) {
                    mPreferenceStorage.importPreferences(callingPackage, preferences);
                }
                
                // Import user profile
                Bundle userProfile = data.getBundle("user_profile");
                if (userProfile != null) {
                    mProfileManager.importUserProfile(callingPackage, userProfile);
                }
                
                // Trigger model retraining
                mHandler.post(() -> {
                    mLearningEngine.retrainModelsForUser(callingPackage);
                });
                
                if (DEBUG) Slog.d(TAG, "Personalization data imported for package: " + callingPackage);
                
                return true;
            } catch (Exception e) {
                Slog.e(TAG, "Error importing personalization data", e);
                return false;
            }
        }

        private void enforcePersonalizationPermission(String operation) {
            String callingPackage = getCallingPackage();
            if (!mSecurityManager.hasPersonalizationPermission(callingPackage, Binder.getCallingUid())) {
                throw new SecurityException("Package " + callingPackage + 
                        " does not have permission for operation: " + operation);
            }
        }

        private String getCallingPackage() {
            try {
                Object pmObj = getContext().getPackageManager();
                PackageManager pm = (PackageManager) pmObj;
                return pm.getNameForUid(Binder.getCallingUid());
            } catch (Exception e) {
                return "unknown";
            }
        }
    }

    // Private methods

    private void initializePersonalization() {
        // Initialize default user profiles
        mProfileManager.initializeDefaultProfiles();
        
        // Load existing models
        mModelManager.loadExistingModels();
        
        // Start background learning
        if (mLearningEnabled) {
            mLearningEngine.startBackgroundLearning();
        }
    }

    private boolean shouldUpdateModels(String preferenceKey) {
        // Determine if preference change should trigger model updates
        return preferenceKey.startsWith("ai_") || 
               preferenceKey.contains("behavior") || 
               preferenceKey.contains("style") ||
               preferenceKey.contains("preference");
    }

    private void processUserInteraction(UserInteraction interaction, String packageName) {
        try {
            // Learn from the interaction
            mLearningEngine.learnFromInteraction(interaction, packageName);
            
            // Update user profile
            mProfileManager.updateProfileFromInteraction(interaction, packageName);
            
            // Log for security audit
            Bundle interactionBundle = new Bundle();
            interactionBundle.putString("interaction_type", interaction.interactionType);
            interactionBundle.putString("outcome", interaction.outcome);
            mSecurityManager.logSecurityEvent("USER_INTERACTION", packageName, 
                Binder.getCallingUid(), interactionBundle);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error processing user interaction", e);
        }
    }

    private void processFeedback(String modelType, FeedbackData feedback, String packageName) {
        try {
            // Update model with feedback
            mLearningEngine.updateModelWithFeedback(modelType, feedback, packageName);
            
            // Update recommendation engine
            mRecommendationEngine.updateWithFeedback(feedback, packageName);
            
            mLastModelUpdate = System.currentTimeMillis();
            
        } catch (Exception e) {
            Slog.e(TAG, "Error processing feedback", e);
        }
    }

    private void resetAllPersonalizationData(String packageName) {
        mPreferenceStorage.resetPreferences(packageName);
        mModelManager.resetModels(packageName);
        mLearningEngine.resetInteractionHistory(packageName);
        mProfileManager.resetUserProfile(packageName);
    }

    private String generateInteractionId(String packageName, UserInteraction interaction) {
        return packageName + "_" + interaction.interactionType + "_" + System.currentTimeMillis();
    }

    /**
     * Record behavior event for learning
     */
    public void recordBehaviorEvent(String eventType, Bundle eventData) {
        if (!mServiceEnabled || !mLearningEnabled || eventData == null) {
            return;
        }

        mHandler.post(() -> {
            try {
                // Create user interaction from behavior event
                Bundle interaction = new Bundle();
                interaction.putString("interaction_type", eventType);
                interaction.putLong("timestamp", System.currentTimeMillis());
                interaction.putBundle("data", eventData);
                String packageName = eventData.getString("package_name", "system");
                interaction.putString("package_name", packageName);

                // Record the interaction
                mTotalInteractions++;

                // Process with learning engine
                if (mLearningEngine != null) {
                    mLearningEngine.processInteraction(packageName, interaction);
                }

                if (DEBUG) Slog.d(TAG, "Behavior event recorded: " + eventType);

            } catch (Exception e) {
                Slog.e(TAG, "Error recording behavior event: " + eventType, e);
            }
        });
    }

    /**
     * Get personalized recommendations for a specific category
     */
    public List<Bundle> getPersonalizedRecommendations(String category, int maxRecommendations) {
        if (!mServiceEnabled || category == null || maxRecommendations <= 0) {
            return new ArrayList<>();
        }

        try {
            return mRecommendationEngine.getRecommendations(category, maxRecommendations);
        } catch (Exception e) {
            Slog.e(TAG, "Error getting personalized recommendations for category: " + category, e);
            return new ArrayList<>();
        }
    }

    /**
     * Get service statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();

        stats.putBoolean("service_enabled", mServiceEnabled);
        stats.putBoolean("learning_enabled", mLearningEnabled);
        stats.putLong("total_interactions", mTotalInteractions);
        stats.putLong("total_recommendations", mTotalRecommendations);
        stats.putLong("successful_recommendations", mSuccessfulRecommendations);
        stats.putLong("last_model_update", mLastModelUpdate);
        stats.putInt("active_models", mActiveModels.size());
        stats.putInt("pending_interactions", mPendingInteractions.size());

        // Calculate success rate
        float successRate = mTotalRecommendations > 0 ?
            (float) mSuccessfulRecommendations / mTotalRecommendations * 100.0f : 0.0f;
        stats.putFloat("success_rate", successRate);

        // Add component statistics
        if (mProfileManager != null) {
            try {
                Bundle profileStats = new Bundle();
                profileStats.putString("status", "active");
                stats.putBundle("profile_manager", profileStats);
            } catch (Exception e) {
                Slog.w(TAG, "Error getting profile manager statistics", e);
            }
        }

        if (mModelManager != null) {
            Bundle modelStats = mModelManager.getStatistics();
            stats.putBundle("model_manager", modelStats);
        }

        if (mLearningEngine != null) {
            Bundle learningStats = mLearningEngine.getStatistics();
            stats.putBundle("learning_engine", learningStats);
        }

        return stats;
    }

    public void dump(java.io.FileDescriptor fd, java.io.PrintWriter pw, String[] args) {
        pw.println("AiPersonalizationService State:");
        pw.println("  Service Enabled: " + mServiceEnabled);
        pw.println("  Learning Enabled: " + mLearningEnabled);
        pw.println("  Total Interactions: " + mTotalInteractions);
        pw.println("  Total Recommendations: " + mTotalRecommendations);
        pw.println("  Successful Recommendations: " + mSuccessfulRecommendations);
        pw.println("  Success Rate: " + 
            (mTotalRecommendations > 0 ? (mSuccessfulRecommendations * 100.0 / mTotalRecommendations) : 0) + "%");
        pw.println("  Last Model Update: " + mLastModelUpdate);
        pw.println("  Active Models: " + mActiveModels.size());
        pw.println("  Pending Interactions: " + mPendingInteractions.size());
        
        if (mProfileManager != null) {
            mProfileManager.dump(pw);
        }
        
        if (mModelManager != null) {
            mModelManager.dump(pw);
        }
    }
}
