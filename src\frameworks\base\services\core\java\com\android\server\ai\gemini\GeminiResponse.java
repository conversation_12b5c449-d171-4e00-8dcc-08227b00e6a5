/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.gemini;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Represents a response from the Gemini API.
 * 
 * Provides methods to parse and extract content from API responses.
 */
public class GeminiResponse {
    private final int mStatusCode;
    private final String mBody;
    private final boolean mSuccess;
    private final long mTimestamp;
    
    public GeminiResponse(int statusCode, String body, boolean success) {
        mStatusCode = statusCode;
        mBody = body;
        mSuccess = success;
        mTimestamp = System.currentTimeMillis();
    }
    
    public int getStatusCode() {
        return mStatusCode;
    }
    
    public String getBody() {
        return mBody;
    }
    
    public boolean isSuccess() {
        return mSuccess;
    }
    
    public long getTimestamp() {
        return mTimestamp;
    }
    
    /**
     * Extract the generated text content from the response
     */
    public String getGeneratedText() {
        if (!mSuccess || mBody == null) {
            return null;
        }
        
        try {
            JSONObject response = new JSONObject(mBody);
            
            if (response.has("candidates")) {
                JSONArray candidates = response.getJSONArray("candidates");
                if (candidates.length() > 0) {
                    JSONObject candidate = candidates.getJSONObject(0);
                    if (candidate.has("content")) {
                        JSONObject content = candidate.getJSONObject("content");
                        if (content.has("parts")) {
                            JSONArray parts = content.getJSONArray("parts");
                            if (parts.length() > 0) {
                                JSONObject part = parts.getJSONObject(0);
                                if (part.has("text")) {
                                    return part.getString("text");
                                }
                            }
                        }
                    }
                }
            }
            
            return null;
            
        } catch (JSONException e) {
            return null;
        }
    }
    
    /**
     * Extract error message from the response
     */
    public String getErrorMessage() {
        if (mSuccess) {
            return null;
        }
        
        try {
            JSONObject response = new JSONObject(mBody);
            
            if (response.has("error")) {
                JSONObject error = response.getJSONObject("error");
                if (error.has("message")) {
                    return error.getString("message");
                }
            }
            
            return "Unknown error";
            
        } catch (JSONException e) {
            return "Failed to parse error response";
        }
    }
    
    /**
     * Check if the response contains safety issues
     */
    public boolean hasSafetyIssues() {
        if (!mSuccess || mBody == null) {
            return false;
        }
        
        try {
            JSONObject response = new JSONObject(mBody);
            
            if (response.has("candidates")) {
                JSONArray candidates = response.getJSONArray("candidates");
                if (candidates.length() > 0) {
                    JSONObject candidate = candidates.getJSONObject(0);
                    if (candidate.has("finishReason")) {
                        String finishReason = candidate.getString("finishReason");
                        return "SAFETY".equals(finishReason);
                    }
                }
            }
            
            return false;
            
        } catch (JSONException e) {
            return false;
        }
    }
    
    /**
     * Get safety ratings from the response
     */
    public JSONArray getSafetyRatings() {
        if (!mSuccess || mBody == null) {
            return null;
        }
        
        try {
            JSONObject response = new JSONObject(mBody);
            
            if (response.has("candidates")) {
                JSONArray candidates = response.getJSONArray("candidates");
                if (candidates.length() > 0) {
                    JSONObject candidate = candidates.getJSONObject(0);
                    if (candidate.has("safetyRatings")) {
                        return candidate.getJSONArray("safetyRatings");
                    }
                }
            }
            
            return null;
            
        } catch (JSONException e) {
            return null;
        }
    }
    
    /**
     * Parse the response as a task plan JSON
     */
    public JSONObject getTaskPlanJson() {
        String text = getGeneratedText();
        if (text == null) {
            return null;
        }
        
        try {
            // Try to extract JSON from the response text
            // The response might contain additional text around the JSON
            int jsonStart = text.indexOf('{');
            int jsonEnd = text.lastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                String jsonText = text.substring(jsonStart, jsonEnd + 1);
                return new JSONObject(jsonText);
            }
            
            return null;
            
        } catch (JSONException e) {
            return null;
        }
    }
    
    /**
     * Parse the response as a validation result JSON
     */
    public JSONObject getValidationResultJson() {
        String text = getGeneratedText();
        if (text == null) {
            return null;
        }
        
        try {
            // Try to extract JSON from the response text
            int jsonStart = text.indexOf('{');
            int jsonEnd = text.lastIndexOf('}');
            
            if (jsonStart >= 0 && jsonEnd > jsonStart) {
                String jsonText = text.substring(jsonStart, jsonEnd + 1);
                return new JSONObject(jsonText);
            }
            
            return null;
            
        } catch (JSONException e) {
            return null;
        }
    }
    
    @Override
    public String toString() {
        return "GeminiResponse{" +
                "statusCode=" + mStatusCode +
                ", success=" + mSuccess +
                ", timestamp=" + mTimestamp +
                ", bodyLength=" + (mBody != null ? mBody.length() : 0) +
                '}';
    }
}
