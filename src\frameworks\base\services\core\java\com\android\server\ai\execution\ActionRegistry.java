/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution;

import android.ai.ActionCapability;
import android.ai.ContextSnapshot;
import android.ai.IActionProvider;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Registry for AI action providers in Jarvis OS.
 * 
 * Manages system and third-party action providers, validates capabilities,
 * and provides action discovery for the AI planning system.
 */
public class ActionRegistry {
    private static final String TAG = "ActionRegistry";
    private static final boolean DEBUG = true;

    // Built-in action types
    public static final String ACTION_OPEN_APP = "openApp";
    public static final String ACTION_SET_SYSTEM_SETTING = "setSystemSetting";
    public static final String ACTION_SEND_NOTIFICATION = "sendNotification";
    public static final String ACTION_MAKE_CALL = "makeCall";
    public static final String ACTION_SEND_MESSAGE = "sendMessage";
    public static final String ACTION_SET_ALARM = "setAlarm";
    public static final String ACTION_CREATE_CALENDAR_EVENT = "createCalendarEvent";
    public static final String ACTION_SEARCH_WEB = "searchWeb";
    public static final String ACTION_READ_FILE = "readFile";
    public static final String ACTION_WRITE_FILE = "writeFile";
    public static final String ACTION_ANALYZE_CONTENT = "analyzeContent";
    public static final String ACTION_WAIT_FOR_CONDITION = "waitForCondition";

    private final Context mContext;
    
    // Action provider registries
    private final ConcurrentHashMap<String, IActionProvider> mSystemActionProviders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, IActionProvider> mThirdPartyActionProviders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, String> mActionProviderPackages = new ConcurrentHashMap<>();
    
    // Action capabilities cache
    private final ConcurrentHashMap<String, ActionCapability> mActionCapabilities = new ConcurrentHashMap<>();
    
    // Statistics
    private long mTotalActionsRegistered = 0;
    private long mSystemActionsRegistered = 0;
    private long mThirdPartyActionsRegistered = 0;

    public ActionRegistry(Context context) {
        mContext = context;
        
        if (DEBUG) Slog.d(TAG, "ActionRegistry initialized");
    }

    /**
     * Initialize system service connections
     */
    public void initializeSystemServiceConnections() {
        if (DEBUG) Slog.d(TAG, "System service connections initialized");
    }

    /**
     * Register built-in action providers
     */
    public void registerBuiltinActionProviders() {
        // This will be called during service initialization
        registerSystemActionProviders();
        
        if (DEBUG) Slog.d(TAG, "Built-in action providers registered");
    }

    /**
     * Register a system action provider
     */
    public void registerSystemActionProvider(String actionType, IActionProvider provider) {
        mSystemActionProviders.put(actionType, provider);
        mActionProviderPackages.put(actionType, "system");
        
        // Cache action capability
        try {
            ActionCapability capability = provider.getCapability();
            mActionCapabilities.put(actionType, capability);
        } catch (Exception e) {
            Slog.e(TAG, "Error getting capability for system action: " + actionType, e);
        }
        
        mSystemActionsRegistered++;
        mTotalActionsRegistered++;
        
        if (DEBUG) Slog.d(TAG, "System action provider registered: " + actionType);
    }

    /**
     * Register a third-party action provider
     */
    public void registerActionProvider(String actionType, IActionProvider provider, String packageName) {
        // Validate package permissions
        if (!validateActionProviderPermissions(actionType, packageName)) {
            Slog.w(TAG, "Package " + packageName + " does not have permission for action: " + actionType);
            return;
        }
        
        mThirdPartyActionProviders.put(actionType, provider);
        mActionProviderPackages.put(actionType, packageName);
        
        // Cache action capability
        try {
            ActionCapability capability = provider.getCapability();
            mActionCapabilities.put(actionType, capability);
        } catch (Exception e) {
            Slog.e(TAG, "Error getting capability for action: " + actionType, e);
        }
        
        mThirdPartyActionsRegistered++;
        mTotalActionsRegistered++;
        
        if (DEBUG) Slog.d(TAG, "Third-party action provider registered: " + actionType + " by " + packageName);
    }

    /**
     * Unregister an action provider
     */
    public void unregisterActionProvider(String actionType, String packageName) {
        String registeredPackage = mActionProviderPackages.get(actionType);
        
        if (registeredPackage != null && registeredPackage.equals(packageName)) {
            if ("system".equals(packageName)) {
                mSystemActionProviders.remove(actionType);
                mSystemActionsRegistered--;
            } else {
                mThirdPartyActionProviders.remove(actionType);
                mThirdPartyActionsRegistered--;
            }
            
            mActionProviderPackages.remove(actionType);
            mActionCapabilities.remove(actionType);
            mTotalActionsRegistered--;
            
            if (DEBUG) Slog.d(TAG, "Action provider unregistered: " + actionType);
        }
    }

    /**
     * Get action provider for a specific action type
     */
    public IActionProvider getActionProvider(String actionType) {
        // Check system providers first
        IActionProvider provider = mSystemActionProviders.get(actionType);
        if (provider != null) {
            return provider;
        }
        
        // Check third-party providers
        return mThirdPartyActionProviders.get(actionType);
    }

    /**
     * Get available actions for current context
     */
    public List<ActionCapability> getAvailableActions(ContextSnapshot context, String requestingPackage) {
        List<ActionCapability> availableActions = new ArrayList<>();
        
        // Add system actions
        for (String actionType : mSystemActionProviders.keySet()) {
            ActionCapability capability = mActionCapabilities.get(actionType);
            if (capability != null && isActionAvailable(actionType, context, requestingPackage)) {
                availableActions.add(capability);
            }
        }
        
        // Add third-party actions
        for (String actionType : mThirdPartyActionProviders.keySet()) {
            ActionCapability capability = mActionCapabilities.get(actionType);
            if (capability != null && isActionAvailable(actionType, context, requestingPackage)) {
                availableActions.add(capability);
            }
        }
        
        return availableActions;
    }

    /**
     * Get all registered action types
     */
    public List<String> getAllActionTypes() {
        List<String> actionTypes = new ArrayList<>();
        actionTypes.addAll(mSystemActionProviders.keySet());
        actionTypes.addAll(mThirdPartyActionProviders.keySet());
        return actionTypes;
    }

    /**
     * Check if action type is registered
     */
    public boolean isActionRegistered(String actionType) {
        return mSystemActionProviders.containsKey(actionType) ||
               mThirdPartyActionProviders.containsKey(actionType);
    }

    /**
     * Check if action type is supported (alias for isActionRegistered)
     */
    public boolean isActionSupported(String actionType) {
        return isActionRegistered(actionType);
    }

    /**
     * Get action capability
     */
    public ActionCapability getActionCapability(String actionType) {
        return mActionCapabilities.get(actionType);
    }

    /**
     * Get registry statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putLong("total_actions", mTotalActionsRegistered);
        stats.putLong("system_actions", mSystemActionsRegistered);
        stats.putLong("third_party_actions", mThirdPartyActionsRegistered);
        stats.putInt("active_system_providers", mSystemActionProviders.size());
        stats.putInt("active_third_party_providers", mThirdPartyActionProviders.size());
        
        return stats;
    }

    /**
     * Dump registry state
     */
    public void dump(PrintWriter pw) {
        pw.println("    ActionRegistry State:");
        pw.println("      Total Actions: " + mTotalActionsRegistered);
        pw.println("      System Actions: " + mSystemActionsRegistered);
        pw.println("      Third-party Actions: " + mThirdPartyActionsRegistered);
        pw.println("      Active System Providers: " + mSystemActionProviders.size());
        pw.println("      Active Third-party Providers: " + mThirdPartyActionProviders.size());
        
        pw.println("      System Actions:");
        for (String actionType : mSystemActionProviders.keySet()) {
            pw.println("        " + actionType);
        }
        
        pw.println("      Third-party Actions:");
        for (String actionType : mThirdPartyActionProviders.keySet()) {
            String packageName = mActionProviderPackages.get(actionType);
            pw.println("        " + actionType + " (" + packageName + ")");
        }
    }

    // Private methods

    private void registerSystemActionProviders() {
        // Register built-in system action providers
        registerSystemActionProvider(ACTION_OPEN_APP, new SystemAppActionProvider(mContext));
        registerSystemActionProvider(ACTION_SET_SYSTEM_SETTING, new SystemSettingsActionProvider(mContext));
        registerSystemActionProvider(ACTION_SEND_NOTIFICATION, new NotificationActionProvider(mContext));
        registerSystemActionProvider(ACTION_MAKE_CALL, new TelephonyActionProvider(mContext));
        registerSystemActionProvider(ACTION_SEND_MESSAGE, new MessagingActionProvider(mContext));
        registerSystemActionProvider(ACTION_SET_ALARM, new AlarmActionProvider(mContext));
        registerSystemActionProvider(ACTION_CREATE_CALENDAR_EVENT, new CalendarActionProvider(mContext));
        registerSystemActionProvider(ACTION_SEARCH_WEB, new WebSearchActionProvider(mContext));
        registerSystemActionProvider(ACTION_READ_FILE, new FileReadActionProvider(mContext));
        registerSystemActionProvider(ACTION_WRITE_FILE, new FileWriteActionProvider(mContext));
        registerSystemActionProvider(ACTION_ANALYZE_CONTENT, new ContentAnalysisActionProvider(mContext));
        registerSystemActionProvider(ACTION_WAIT_FOR_CONDITION, new ConditionWaitActionProvider(mContext));
    }

    private boolean validateActionProviderPermissions(String actionType, String packageName) {
        // Validate that the package has appropriate permissions for the action type
        switch (actionType) {
            case ACTION_MAKE_CALL:
                return hasPermission(packageName, "android.permission.CALL_PHONE");
            case ACTION_SEND_MESSAGE:
                return hasPermission(packageName, "android.permission.SEND_SMS");
            case ACTION_SET_ALARM:
                return hasPermission(packageName, "com.android.alarm.permission.SET_ALARM");
            case ACTION_CREATE_CALENDAR_EVENT:
                return hasPermission(packageName, "android.permission.WRITE_CALENDAR");
            case ACTION_READ_FILE:
                return hasPermission(packageName, "android.permission.READ_EXTERNAL_STORAGE");
            case ACTION_WRITE_FILE:
                return hasPermission(packageName, "android.permission.WRITE_EXTERNAL_STORAGE");
            default:
                // For custom actions, require AI action provider permission
                return hasPermission(packageName, "android.permission.AI_ACTION_PROVIDER");
        }
    }

    private boolean hasPermission(String packageName, String permission) {
        try {
            return mContext.getPackageManager().checkPermission(permission, packageName) 
                == android.content.pm.PackageManager.PERMISSION_GRANTED;
        } catch (Exception e) {
            Slog.e(TAG, "Error checking permission: " + permission + " for package: " + packageName, e);
            return false;
        }
    }

    private boolean isActionAvailable(String actionType, ContextSnapshot context, String requestingPackage) {
        // Check if action is available based on current context and requesting package
        
        // Basic availability checks
        ActionCapability capability = mActionCapabilities.get(actionType);
        if (capability == null) {
            return false;
        }
        
        // Check required permissions
        if (capability.requiredPermissions != null) {
            for (String permission : capability.requiredPermissions) {
                if (!hasPermission(requestingPackage, permission)) {
                    return false;
                }
            }
        }
        
        // Context-specific availability checks
        switch (actionType) {
            case ACTION_MAKE_CALL:
                // Only available if device has telephony capability
                return mContext.getPackageManager().hasSystemFeature("android.hardware.telephony");
                
            case ACTION_SEND_MESSAGE:
                // Only available if device has SMS capability
                return mContext.getPackageManager().hasSystemFeature("android.hardware.telephony");
                
            case ACTION_SET_ALARM:
                // Always available
                return true;
                
            case ACTION_SEARCH_WEB:
                // Only available if device has network connectivity
                return context != null && context.environmentalData != null &&
                       context.environmentalData.getBoolean("network_available", false);
                
            default:
                // Default to available for custom actions
                return true;
        }
    }

    // Placeholder action provider classes (these would be implemented separately)
    
    private static class SystemAppActionProvider implements IActionProvider {
        private final Context mContext;
        
        SystemAppActionProvider(Context context) {
            mContext = context;
        }
        
        @Override
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) {
            // Implementation for opening apps
            return null;
        }
        
        @Override
        public boolean canExecuteAction(android.ai.ActionRequest request) {
            return true;
        }
        
        @Override
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_OPEN_APP;
            capability.description = "Open applications on the device";
            capability.requiredPermissions = new String[0];
            capability.requiresUserConfirmation = false;
            return capability;
        }
    }
    
    // Additional placeholder classes for other action providers...
    private static class SystemSettingsActionProvider implements IActionProvider {
        private final Context mContext;
        SystemSettingsActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_SET_SYSTEM_SETTING;
            capability.description = "Modify system settings";
            capability.requiresUserConfirmation = true;
            return capability;
        }
    }
    
    private static class NotificationActionProvider implements IActionProvider {
        private final Context mContext;
        NotificationActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_SEND_NOTIFICATION;
            capability.description = "Send notifications to user";
            return capability;
        }
    }
    
    private static class TelephonyActionProvider implements IActionProvider {
        private final Context mContext;
        TelephonyActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_MAKE_CALL;
            capability.description = "Make phone calls";
            capability.requiredPermissions = new String[]{"android.permission.CALL_PHONE"};
            capability.requiresUserConfirmation = true;
            return capability;
        }
    }
    
    private static class MessagingActionProvider implements IActionProvider {
        private final Context mContext;
        MessagingActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_SEND_MESSAGE;
            capability.description = "Send text messages";
            capability.requiredPermissions = new String[]{"android.permission.SEND_SMS"};
            return capability;
        }
    }
    
    private static class AlarmActionProvider implements IActionProvider {
        private final Context mContext;
        AlarmActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_SET_ALARM;
            capability.description = "Set alarms and reminders";
            return capability;
        }
    }
    
    private static class CalendarActionProvider implements IActionProvider {
        private final Context mContext;
        CalendarActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_CREATE_CALENDAR_EVENT;
            capability.description = "Create calendar events";
            capability.requiredPermissions = new String[]{"android.permission.WRITE_CALENDAR"};
            return capability;
        }
    }
    
    private static class WebSearchActionProvider implements IActionProvider {
        private final Context mContext;
        WebSearchActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_SEARCH_WEB;
            capability.description = "Search the web";
            capability.requiredPermissions = new String[]{"android.permission.INTERNET"};
            return capability;
        }
    }
    
    private static class FileReadActionProvider implements IActionProvider {
        private final Context mContext;
        FileReadActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_READ_FILE;
            capability.description = "Read files from storage";
            capability.requiredPermissions = new String[]{"android.permission.READ_EXTERNAL_STORAGE"};
            return capability;
        }
    }
    
    private static class FileWriteActionProvider implements IActionProvider {
        private final Context mContext;
        FileWriteActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_WRITE_FILE;
            capability.description = "Write files to storage";
            capability.requiredPermissions = new String[]{"android.permission.WRITE_EXTERNAL_STORAGE"};
            capability.requiresUserConfirmation = true;
            return capability;
        }
    }
    
    private static class ContentAnalysisActionProvider implements IActionProvider {
        private final Context mContext;
        ContentAnalysisActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_ANALYZE_CONTENT;
            capability.description = "Analyze content using AI";
            return capability;
        }
    }
    
    private static class ConditionWaitActionProvider implements IActionProvider {
        private final Context mContext;
        ConditionWaitActionProvider(Context context) { mContext = context; }
        public android.ai.ActionResult executeAction(android.ai.ActionRequest request) { return null; }
        public boolean canExecuteAction(android.ai.ActionRequest request) { return true; }
        public ActionCapability getCapability() {
            ActionCapability capability = new ActionCapability();
            capability.actionType = ACTION_WAIT_FOR_CONDITION;
            capability.description = "Wait for specific conditions";
            return capability;
        }
    }
}
