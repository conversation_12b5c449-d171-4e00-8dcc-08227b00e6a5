/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "ai_performance_optimizer.h"
#include <android/log.h>
#include <cutils/log.h>
#include <utils/Mutex.h>
#include <sys/resource.h>
#include <unistd.h>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <thread>

#define LOG_TAG "ai_performance_optimizer"

namespace android {
namespace ai {

class PerformanceOptimizer {
private:
    mutable Mutex mLock;
    bool mInitialized;
    bool mOptimizationEnabled;
    
    // Performance monitoring
    struct PerformanceStats {
        uint64_t totalInferences;
        uint64_t totalProcessingTime;
        uint64_t peakMemoryUsage;
        float averageCpuUsage;
        uint32_t thermalThrottleEvents;
        uint64_t lastUpdateTime;
    } mStats;
    
    // Optimization parameters
    struct OptimizationConfig {
        bool enableCpuOptimization;
        bool enableMemoryOptimization;
        bool enableThermalManagement;
        bool enablePowerManagement;
        float cpuUsageThreshold;
        uint64_t memoryUsageThreshold;
        float thermalThreshold;
        uint32_t optimizationInterval;
    } mConfig;
    
    // System information
    struct SystemInfo {
        uint32_t numCpuCores;
        uint64_t totalMemory;
        uint64_t availableMemory;
        float currentTemperature;
        uint32_t currentCpuFreq;
        bool thermalThrottling;
    } mSystemInfo;

public:
    PerformanceOptimizer() : mInitialized(false), mOptimizationEnabled(true) {
        memset(&mStats, 0, sizeof(mStats));
        initializeDefaultConfig();
    }
    
    ~PerformanceOptimizer() {
        cleanup();
    }
    
    bool initialize() {
        Mutex::Autolock lock(mLock);
        
        if (mInitialized) {
            return true;
        }
        
        // Initialize system information
        if (!updateSystemInfo()) {
            ALOGE("Failed to update system information");
            return false;
        }
        
        // Set up performance monitoring
        mStats.lastUpdateTime = getCurrentTimeMs();
        
        mInitialized = true;
        ALOGD("Performance optimizer initialized");
        return true;
    }
    
    void cleanup() {
        Mutex::Autolock lock(mLock);
        
        if (!mInitialized) {
            return;
        }
        
        mInitialized = false;
        ALOGD("Performance optimizer cleaned up");
    }
    
    bool optimizeForInference() {
        Mutex::Autolock lock(mLock);
        
        if (!mInitialized || !mOptimizationEnabled) {
            return false;
        }
        
        updateSystemInfo();
        
        bool optimized = false;
        
        // CPU optimization
        if (mConfig.enableCpuOptimization) {
            optimized |= optimizeCpuPerformance();
        }
        
        // Memory optimization
        if (mConfig.enableMemoryOptimization) {
            optimized |= optimizeMemoryUsage();
        }
        
        // Thermal management
        if (mConfig.enableThermalManagement) {
            optimized |= manageThermalThrottling();
        }
        
        // Power management
        if (mConfig.enablePowerManagement) {
            optimized |= optimizePowerConsumption();
        }
        
        return optimized;
    }
    
    void recordInferenceMetrics(uint64_t processingTime, uint64_t memoryUsed, float cpuUsage) {
        Mutex::Autolock lock(mLock);
        
        mStats.totalInferences++;
        mStats.totalProcessingTime += processingTime;
        mStats.peakMemoryUsage = std::max(mStats.peakMemoryUsage, memoryUsed);
        
        // Update average CPU usage (exponential moving average)
        float alpha = 0.1f;
        mStats.averageCpuUsage = alpha * cpuUsage + (1.0f - alpha) * mStats.averageCpuUsage;
        
        mStats.lastUpdateTime = getCurrentTimeMs();
        
        // Trigger optimization if thresholds exceeded
        if (shouldTriggerOptimization()) {
            optimizeForInference();
        }
    }
    
    PerformanceMetrics getPerformanceMetrics() const {
        Mutex::Autolock lock(mLock);
        
        PerformanceMetrics metrics = {};
        metrics.totalInferences = mStats.totalInferences;
        metrics.averageProcessingTime = mStats.totalInferences > 0 ? 
            static_cast<float>(mStats.totalProcessingTime) / mStats.totalInferences : 0.0f;
        metrics.peakMemoryUsage = mStats.peakMemoryUsage;
        metrics.averageCpuUsage = mStats.averageCpuUsage;
        metrics.thermalThrottleEvents = mStats.thermalThrottleEvents;
        metrics.currentTemperature = mSystemInfo.currentTemperature;
        metrics.availableMemory = mSystemInfo.availableMemory;
        metrics.cpuFrequency = mSystemInfo.currentCpuFreq;
        metrics.thermalThrottling = mSystemInfo.thermalThrottling;
        
        return metrics;
    }
    
    bool setOptimizationConfig(const OptimizationConfig& config) {
        Mutex::Autolock lock(mLock);
        mConfig = config;
        return true;
    }
    
    OptimizationConfig getOptimizationConfig() const {
        Mutex::Autolock lock(mLock);
        return mConfig;
    }
    
    void enableOptimization(bool enable) {
        Mutex::Autolock lock(mLock);
        mOptimizationEnabled = enable;
        ALOGD("Performance optimization %s", enable ? "enabled" : "disabled");
    }

private:
    void initializeDefaultConfig() {
        mConfig.enableCpuOptimization = true;
        mConfig.enableMemoryOptimization = true;
        mConfig.enableThermalManagement = true;
        mConfig.enablePowerManagement = true;
        mConfig.cpuUsageThreshold = 80.0f;
        mConfig.memoryUsageThreshold = 1024 * 1024 * 1024; // 1GB
        mConfig.thermalThreshold = 70.0f; // 70°C
        mConfig.optimizationInterval = 5000; // 5 seconds
    }
    
    bool updateSystemInfo() {
        // Update CPU core count
        mSystemInfo.numCpuCores = sysconf(_SC_NPROCESSORS_ONLN);
        
        // Update memory information
        updateMemoryInfo();
        
        // Update thermal information
        updateThermalInfo();
        
        // Update CPU frequency
        updateCpuFrequency();
        
        return true;
    }
    
    void updateMemoryInfo() {
        std::ifstream meminfo("/proc/meminfo");
        std::string line;
        
        while (std::getline(meminfo, line)) {
            if (line.find("MemTotal:") == 0) {
                std::istringstream iss(line);
                std::string label;
                uint64_t value;
                iss >> label >> value;
                mSystemInfo.totalMemory = value * 1024; // Convert KB to bytes
            } else if (line.find("MemAvailable:") == 0) {
                std::istringstream iss(line);
                std::string label;
                uint64_t value;
                iss >> label >> value;
                mSystemInfo.availableMemory = value * 1024; // Convert KB to bytes
            }
        }
    }
    
    void updateThermalInfo() {
        // Read thermal zone temperature (simplified)
        std::ifstream thermal("/sys/class/thermal/thermal_zone0/temp");
        if (thermal.is_open()) {
            int temp_millicelsius;
            thermal >> temp_millicelsius;
            mSystemInfo.currentTemperature = temp_millicelsius / 1000.0f;
            
            // Check for thermal throttling
            mSystemInfo.thermalThrottling = mSystemInfo.currentTemperature > mConfig.thermalThreshold;
        } else {
            mSystemInfo.currentTemperature = 25.0f; // Default temperature
            mSystemInfo.thermalThrottling = false;
        }
    }
    
    void updateCpuFrequency() {
        // Read current CPU frequency (simplified)
        std::ifstream cpufreq("/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq");
        if (cpufreq.is_open()) {
            cpufreq >> mSystemInfo.currentCpuFreq;
        } else {
            mSystemInfo.currentCpuFreq = 1000000; // Default 1GHz
        }
    }
    
    bool optimizeCpuPerformance() {
        if (mStats.averageCpuUsage > mConfig.cpuUsageThreshold) {
            // High CPU usage detected, apply optimizations
            
            // Set CPU governor to performance mode (if available)
            setCpuGovernor("performance");
            
            // Adjust process priority
            setpriority(PRIO_PROCESS, 0, -10); // Higher priority
            
            ALOGD("Applied CPU performance optimizations");
            return true;
        } else if (mStats.averageCpuUsage < mConfig.cpuUsageThreshold * 0.5f) {
            // Low CPU usage, switch to power saving
            setCpuGovernor("powersave");
            setpriority(PRIO_PROCESS, 0, 0); // Normal priority
            
            ALOGD("Applied CPU power saving optimizations");
            return true;
        }
        
        return false;
    }
    
    bool optimizeMemoryUsage() {
        if (mStats.peakMemoryUsage > mConfig.memoryUsageThreshold) {
            // High memory usage detected
            
            // Trigger garbage collection (if applicable)
            // Force memory compaction
            std::ofstream compaction("/proc/sys/vm/compact_memory");
            if (compaction.is_open()) {
                compaction << "1";
                compaction.close();
            }
            
            ALOGD("Applied memory optimization");
            return true;
        }
        
        return false;
    }
    
    bool manageThermalThrottling() {
        if (mSystemInfo.thermalThrottling) {
            mStats.thermalThrottleEvents++;
            
            // Reduce CPU frequency to manage heat
            setCpuGovernor("powersave");
            
            // Add delay to reduce processing rate
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            ALOGW("Thermal throttling detected, applied cooling measures");
            return true;
        }
        
        return false;
    }
    
    bool optimizePowerConsumption() {
        // Implement power optimization strategies
        
        // If running on battery and low usage, reduce performance
        if (isOnBattery() && mStats.averageCpuUsage < 30.0f) {
            setCpuGovernor("powersave");
            ALOGD("Applied power consumption optimization");
            return true;
        }
        
        return false;
    }
    
    bool shouldTriggerOptimization() {
        uint64_t currentTime = getCurrentTimeMs();
        return (currentTime - mStats.lastUpdateTime) > mConfig.optimizationInterval;
    }
    
    void setCpuGovernor(const char* governor) {
        for (uint32_t cpu = 0; cpu < mSystemInfo.numCpuCores; cpu++) {
            std::string path = "/sys/devices/system/cpu/cpu" + std::to_string(cpu) + 
                              "/cpufreq/scaling_governor";
            std::ofstream gov_file(path);
            if (gov_file.is_open()) {
                gov_file << governor;
                gov_file.close();
            }
        }
    }
    
    bool isOnBattery() {
        // Check if device is running on battery
        std::ifstream power_supply("/sys/class/power_supply/battery/status");
        if (power_supply.is_open()) {
            std::string status;
            power_supply >> status;
            return status == "Discharging";
        }
        return false; // Assume plugged in if can't determine
    }
    
    uint64_t getCurrentTimeMs() {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        return std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
    }
};

// Global instance
static std::unique_ptr<PerformanceOptimizer> g_optimizer;
static Mutex g_optimizer_lock;

} // namespace ai
} // namespace android

// C API implementation
extern "C" {

using namespace android::ai;

bool ai_performance_optimizer_init() {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (g_optimizer) {
        return true;
    }
    
    g_optimizer = std::make_unique<PerformanceOptimizer>();
    return g_optimizer->initialize();
}

void ai_performance_optimizer_cleanup() {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (g_optimizer) {
        g_optimizer->cleanup();
        g_optimizer.reset();
    }
}

bool ai_performance_optimize_for_inference() {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (!g_optimizer) {
        return false;
    }
    
    return g_optimizer->optimizeForInference();
}

void ai_performance_record_metrics(uint64_t processing_time, uint64_t memory_used, float cpu_usage) {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (g_optimizer) {
        g_optimizer->recordInferenceMetrics(processing_time, memory_used, cpu_usage);
    }
}

PerformanceMetrics ai_performance_get_metrics() {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (g_optimizer) {
        return g_optimizer->getPerformanceMetrics();
    }
    
    return {};
}

void ai_performance_enable_optimization(bool enable) {
    Mutex::Autolock lock(g_optimizer_lock);
    
    if (g_optimizer) {
        g_optimizer->enableOptimization(enable);
    }
}

} // extern "C"
