/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Result of an AI action execution
 */
public class ActionResult implements Parcelable {
    
    public boolean success;
    public String message;
    public String errorCode;
    public Bundle resultData;
    public long executionTimeMs;
    public int statusCode;

    public ActionResult() {
        this.success = false;
        this.message = "";
        this.errorCode = "";
        this.resultData = new Bundle();
        this.executionTimeMs = 0;
        this.statusCode = 0;
    }

    public ActionResult(boolean success, String message) {
        this();
        this.success = success;
        this.message = message;
    }

    public ActionResult(boolean success, String message, Bundle resultData) {
        this(success, message);
        this.resultData = resultData != null ? resultData : new Bundle();
    }

    protected ActionResult(Parcel in) {
        success = in.readByte() != 0;
        message = in.readString();
        errorCode = in.readString();
        resultData = in.readParcelable(Bundle.class.getClassLoader());
        executionTimeMs = in.readLong();
        statusCode = in.readInt();
    }

    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
        @Override
        public ActionResult createFromParcel(Parcel in) {
            return new ActionResult(in);
        }

        @Override
        public ActionResult[] newArray(int size) {
            return new ActionResult[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (success ? 1 : 0));
        dest.writeString(message);
        dest.writeString(errorCode);
        dest.writeParcelable(resultData, flags);
        dest.writeLong(executionTimeMs);
        dest.writeInt(statusCode);
    }

    // Utility methods
    public boolean isSuccess() {
        return success;
    }

    public boolean hasError() {
        return !success;
    }

    public String getErrorMessage() {
        return success ? null : message;
    }

    public Bundle getResultData() {
        return resultData != null ? resultData : new Bundle();
    }

    public void setResultData(String key, Object value) {
        if (resultData == null) {
            resultData = new Bundle();
        }
        if (value instanceof String) {
            resultData.putString(key, (String) value);
        } else if (value instanceof Integer) {
            resultData.putInt(key, (Integer) value);
        } else if (value instanceof Boolean) {
            resultData.putBoolean(key, (Boolean) value);
        } else if (value instanceof Long) {
            resultData.putLong(key, (Long) value);
        } else if (value instanceof Bundle) {
            resultData.putBundle(key, (Bundle) value);
        }
    }

    public String getResultString(String key) {
        return resultData != null ? resultData.getString(key) : null;
    }

    public int getResultInt(String key, int defaultValue) {
        return resultData != null ? resultData.getInt(key, defaultValue) : defaultValue;
    }

    public boolean getResultBoolean(String key, boolean defaultValue) {
        return resultData != null ? resultData.getBoolean(key, defaultValue) : defaultValue;
    }

    @Override
    public String toString() {
        return "ActionResult{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", errorCode='" + errorCode + '\'' +
                ", executionTimeMs=" + executionTimeMs +
                ", statusCode=" + statusCode +
                '}';
    }
}
