#!/bin/bash

# Comprehensive Java Build Script for Jarvis OS
# Fixes classpath and dependency issues with proper compilation order

echo "🔧 Starting comprehensive Java compilation with dependency resolution..."

# Create output directory
mkdir -p build/classes
rm -rf build/classes/* 2>/dev/null

# Step 1: Compile Android framework stubs first (no dependencies)
echo "🏗️  Step 1: Compiling Android framework base classes..."

# Compile the most basic Android classes first (no external dependencies)
echo "  📦 Compiling android.os package..."
javac -d build/classes \
    src/frameworks/base/core/java/android/os/Parcelable.java \
    src/frameworks/base/core/java/android/os/Parcel.java \
    src/frameworks/base/core/java/android/os/Bundle.java \
    src/frameworks/base/core/java/android/os/IBinder.java \
    src/frameworks/base/core/java/android/os/IInterface.java \
    src/frameworks/base/core/java/android/os/RemoteException.java \
    src/frameworks/base/core/java/android/os/UserHandle.java \
    src/frameworks/base/core/java/android/os/SystemClock.java \
    2>&1 | tee build_step1a.log

if [ $? -eq 0 ]; then
    echo "  ✅ android.os package compiled successfully"
else
    echo "  ❌ android.os package compilation failed"
    echo "  Check build_step1a.log for errors"
    exit 1
fi

# Now compile Binder with the compiled classes in classpath
echo "  📦 Compiling Binder and related classes..."
javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/os/Binder.java \
    2>&1 | tee build_step1b.log

if [ $? -eq 0 ]; then
    echo "  ✅ Binder compiled successfully"
else
    echo "  ❌ Binder compilation failed"
    echo "  Check build_step1b.log for errors"
fi

# Compile other Android framework packages
echo "  📦 Compiling android.content package..."
javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/content/*.java \
    2>&1 | tee build_step1c.log

echo "  📦 Compiling android.app package..."
javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/app/*.java \
    2>&1 | tee build_step1d.log

echo "  📦 Compiling android.graphics package..."
javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/graphics/*.java \
    2>&1 | tee build_step1e.log

echo "  📦 Compiling android.content.pm package..."
javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/content/pm/*.java \
    2>&1 | tee build_step1f.log

echo "✅ Step 1: Android framework base classes completed"

# Step 2: Compile AI framework interfaces
echo "🤖 Step 2: Compiling AI framework interfaces..."

javac -cp build/classes -d build/classes \
    src/frameworks/base/core/java/android/ai/*.java \
    2>&1 | tee build_step2.log

if [ $? -eq 0 ]; then
    echo "✅ Step 2: AI framework interfaces compiled successfully"
else
    echo "❌ Step 2: AI framework compilation failed"
    echo "Errors in build_step2.log"
fi

# Step 3: Compile AI services (now with all dependencies available)
echo "⚙️  Step 3: Compiling AI services..."

# Compile AI services with all framework classes in classpath
find src/frameworks/base/services/core/java -name "*.java" -exec javac -cp build/classes -d build/classes {} \; 2>&1 | tee build_step3.log

if [ $? -eq 0 ]; then
    echo "✅ Step 3: AI services compiled successfully"
else
    echo "❌ Step 3: AI services compilation failed"
    echo "Errors in build_step3.log"
fi

# Step 4: Compile hardware interfaces
echo "🔌 Step 4: Compiling hardware interfaces..."

if [ -d "src/hardware/interfaces" ]; then
    find src/hardware/interfaces -name "*.java" -exec javac -cp "$CLASSPATH" -d build/classes {} \; 2>&1 | tee build_step4.log
    
    if [ $? -eq 0 ]; then
        echo "✅ Step 4: Hardware interfaces compiled successfully"
    else
        echo "❌ Step 4: Hardware interfaces compilation failed"
        echo "Errors in build_step4.log"
    fi
else
    echo "⚠️  Step 4: No hardware interfaces found"
fi

# Step 5: Compile system libraries
echo "📚 Step 5: Compiling system libraries..."

if [ -d "src/system/libai" ]; then
    find src/system/libai -name "*.java" -exec javac -cp "$CLASSPATH" -d build/classes {} \; 2>&1 | tee build_step5.log
    
    if [ $? -eq 0 ]; then
        echo "✅ Step 5: System libraries compiled successfully"
    else
        echo "❌ Step 5: System libraries compilation failed"
        echo "Errors in build_step5.log"
    fi
else
    echo "⚠️  Step 5: No system libraries found"
fi

# Final summary
echo ""
echo "📊 Build Summary:"
echo "=================="

total_java_files=$(find src -name "*.java" | wc -l)
compiled_classes=$(find build/classes -name "*.class" 2>/dev/null | wc -l)

echo "Total Java files: $total_java_files"
echo "Compiled classes: $compiled_classes"

if [ $compiled_classes -gt 0 ]; then
    success_rate=$((compiled_classes * 100 / total_java_files))
    echo "Success rate: $success_rate%"
    
    if [ $success_rate -eq 100 ]; then
        echo "🎉 BUILD SUCCESSFUL: All files compiled!"
    elif [ $success_rate -gt 80 ]; then
        echo "✅ BUILD MOSTLY SUCCESSFUL: $success_rate% compiled"
    else
        echo "⚠️  BUILD PARTIAL: Only $success_rate% compiled"
    fi
else
    echo "❌ BUILD FAILED: No classes compiled"
fi

echo ""
echo "📁 Build artifacts in: build/classes"
echo "📋 Build logs: build_step*.log"
