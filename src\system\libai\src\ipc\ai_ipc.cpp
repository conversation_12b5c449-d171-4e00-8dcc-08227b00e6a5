/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "ai_ipc.h"
#include <android/log.h>
#include <cutils/log.h>
#include <utils/Mutex.h>
#include <binder/IBinder.h>
#include <binder/ProcessState.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
#include <memory>
#include <unordered_map>
#include <queue>
#include <thread>
#include <condition_variable>
#include <atomic>

#define LOG_TAG "libai_ipc"
#define ALOGD_IF_DEBUG(cond, ...) if (cond) ALOGD(__VA_ARGS__)

namespace {

// Global state
static bool g_initialized = false;
static bool g_debug_logging = false;
static android::Mutex g_mutex;

// Connection and resource registries
static std::unordered_map<ai_ipc_connection_t*, std::unique_ptr<ai_ipc_connection_t>> g_connections;
static std::unordered_map<ai_ipc_shared_buffer_t*, std::unique_ptr<ai_ipc_shared_buffer_t>> g_shared_buffers;
static std::unordered_map<ai_ipc_event_t*, std::unique_ptr<ai_ipc_event_t>> g_events;

// Message ID counter
static std::atomic<uint32_t> g_next_message_id(1);

// Internal structures
struct ai_ipc_connection {
    ai_ipc_config_t config;
    ai_ipc_connection_type_t type;
    std::string endpoint_name;
    int socket_fd;
    bool is_connected;
    bool is_listening;
    
    // Message queue
    std::queue<std::unique_ptr<ai_ipc_message_t>> message_queue;
    android::Mutex queue_mutex;
    std::condition_variable queue_condition;
    
    // Callback handling
    ai_ipc_message_callback_t message_callback;
    void* callback_user_data;
    std::unique_ptr<std::thread> listener_thread;
    
    // Metrics
    ai_ipc_metrics_t metrics;
    
    ai_ipc_connection() : socket_fd(-1), is_connected(false), is_listening(false),
                         message_callback(nullptr), callback_user_data(nullptr) {
        memset(&metrics, 0, sizeof(metrics));
    }
    
    ~ai_ipc_connection() {
        if (socket_fd >= 0) {
            close(socket_fd);
        }
        if (listener_thread && listener_thread->joinable()) {
            listener_thread->join();
        }
    }
};

struct ai_ipc_message {
    uint32_t message_id;
    ai_ipc_message_type_t type;
    ai_ipc_priority_t priority;
    uint32_t sender_pid;
    uint32_t sender_uid;
    uint64_t timestamp;
    size_t payload_size;
    std::unique_ptr<uint8_t[]> payload;
    bool requires_response;
    uint32_t correlation_id;
    
    ai_ipc_message() : message_id(0), type(AI_IPC_MSG_CUSTOM), priority(AI_IPC_PRIORITY_NORMAL),
                      sender_pid(0), sender_uid(0), timestamp(0), payload_size(0),
                      requires_response(false), correlation_id(0) {}
};

struct ai_ipc_shared_buffer {
    std::string name;
    size_t buffer_size;
    bool read_only;
    bool enable_synchronization;
    void* mapped_data;
    int shm_fd;
    
    ai_ipc_shared_buffer() : buffer_size(0), read_only(false), enable_synchronization(false),
                            mapped_data(nullptr), shm_fd(-1) {}
    
    ~ai_ipc_shared_buffer() {
        if (mapped_data) {
            munmap(mapped_data, buffer_size);
        }
        if (shm_fd >= 0) {
            close(shm_fd);
        }
    }
};

struct ai_ipc_event {
    std::string event_name;
    bool auto_reset;
    bool manual_reset;
    uint32_t timeout_ms;
    std::atomic<bool> is_signaled;
    android::Mutex event_mutex;
    std::condition_variable event_condition;
    
    ai_ipc_event() : auto_reset(false), manual_reset(false), timeout_ms(0), is_signaled(false) {}
};

// Helper functions
ai_ipc_result_t create_unix_socket(const std::string& endpoint_name, int* socket_fd) {
    int fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (fd < 0) {
        return AI_IPC_ERROR_CONNECTION_FAILED;
    }
    
    struct sockaddr_un addr;
    memset(&addr, 0, sizeof(addr));
    addr.sun_family = AF_UNIX;
    strncpy(addr.sun_path, endpoint_name.c_str(), sizeof(addr.sun_path) - 1);
    
    *socket_fd = fd;
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t send_message_impl(ai_ipc_connection_t* connection, const ai_ipc_message_t* message) {
    if (!connection || !message || !connection->is_connected) {
        return AI_IPC_ERROR_NOT_CONNECTED;
    }
    
    // Serialize message
    size_t header_size = sizeof(uint32_t) * 6 + sizeof(uint64_t) + sizeof(size_t);
    size_t total_size = header_size + message->payload_size;
    
    std::unique_ptr<uint8_t[]> buffer(new uint8_t[total_size]);
    uint8_t* ptr = buffer.get();
    
    // Pack header
    *reinterpret_cast<uint32_t*>(ptr) = message->message_id; ptr += sizeof(uint32_t);
    *reinterpret_cast<uint32_t*>(ptr) = static_cast<uint32_t>(message->type); ptr += sizeof(uint32_t);
    *reinterpret_cast<uint32_t*>(ptr) = static_cast<uint32_t>(message->priority); ptr += sizeof(uint32_t);
    *reinterpret_cast<uint32_t*>(ptr) = message->sender_pid; ptr += sizeof(uint32_t);
    *reinterpret_cast<uint32_t*>(ptr) = message->sender_uid; ptr += sizeof(uint32_t);
    *reinterpret_cast<uint64_t*>(ptr) = message->timestamp; ptr += sizeof(uint64_t);
    *reinterpret_cast<size_t*>(ptr) = message->payload_size; ptr += sizeof(size_t);
    *reinterpret_cast<uint32_t*>(ptr) = message->correlation_id; ptr += sizeof(uint32_t);
    
    // Pack payload
    if (message->payload_size > 0 && message->payload) {
        memcpy(ptr, message->payload.get(), message->payload_size);
    }
    
    // Send over socket
    ssize_t sent = send(connection->socket_fd, buffer.get(), total_size, 0);
    if (sent != static_cast<ssize_t>(total_size)) {
        return AI_IPC_ERROR_CONNECTION_FAILED;
    }
    
    // Update metrics
    connection->metrics.messages_sent++;
    connection->metrics.bytes_sent += total_size;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Message sent: ID=%u, type=%d, size=%zu", 
                   message->message_id, message->type, total_size);
    
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t receive_message_impl(ai_ipc_connection_t* connection, ai_ipc_message_t** message) {
    if (!connection || !message || !connection->is_connected) {
        return AI_IPC_ERROR_NOT_CONNECTED;
    }
    
    // Receive header first
    size_t header_size = sizeof(uint32_t) * 6 + sizeof(uint64_t) + sizeof(size_t);
    std::unique_ptr<uint8_t[]> header_buffer(new uint8_t[header_size]);
    
    ssize_t received = recv(connection->socket_fd, header_buffer.get(), header_size, MSG_WAITALL);
    if (received != static_cast<ssize_t>(header_size)) {
        return AI_IPC_ERROR_CONNECTION_FAILED;
    }
    
    // Unpack header
    uint8_t* ptr = header_buffer.get();
    auto new_message = std::make_unique<ai_ipc_message_t>();
    
    new_message->message_id = *reinterpret_cast<uint32_t*>(ptr); ptr += sizeof(uint32_t);
    new_message->type = static_cast<ai_ipc_message_type_t>(*reinterpret_cast<uint32_t*>(ptr)); ptr += sizeof(uint32_t);
    new_message->priority = static_cast<ai_ipc_priority_t>(*reinterpret_cast<uint32_t*>(ptr)); ptr += sizeof(uint32_t);
    new_message->sender_pid = *reinterpret_cast<uint32_t*>(ptr); ptr += sizeof(uint32_t);
    new_message->sender_uid = *reinterpret_cast<uint32_t*>(ptr); ptr += sizeof(uint32_t);
    new_message->timestamp = *reinterpret_cast<uint64_t*>(ptr); ptr += sizeof(uint64_t);
    new_message->payload_size = *reinterpret_cast<size_t*>(ptr); ptr += sizeof(size_t);
    new_message->correlation_id = *reinterpret_cast<uint32_t*>(ptr); ptr += sizeof(uint32_t);
    
    // Receive payload if present
    if (new_message->payload_size > 0) {
        new_message->payload = std::make_unique<uint8_t[]>(new_message->payload_size);
        received = recv(connection->socket_fd, new_message->payload.get(), 
                       new_message->payload_size, MSG_WAITALL);
        if (received != static_cast<ssize_t>(new_message->payload_size)) {
            return AI_IPC_ERROR_CONNECTION_FAILED;
        }
    }
    
    // Update metrics
    connection->metrics.messages_received++;
    connection->metrics.bytes_received += header_size + new_message->payload_size;
    
    *message = new_message.release();
    
    ALOGD_IF_DEBUG(g_debug_logging, "Message received: ID=%u, type=%d, payload_size=%zu", 
                   (*message)->message_id, (*message)->type, (*message)->payload_size);
    
    return AI_IPC_SUCCESS;
}

void listener_thread_func(ai_ipc_connection_t* connection) {
    ALOGD_IF_DEBUG(g_debug_logging, "Listener thread started for connection: %s", 
                   connection->endpoint_name.c_str());
    
    while (connection->is_listening) {
        ai_ipc_message_t* message = nullptr;
        ai_ipc_result_t result = receive_message_impl(connection, &message);
        
        if (result == AI_IPC_SUCCESS && message) {
            if (connection->message_callback) {
                connection->message_callback(message, connection->callback_user_data);
            }
            ai_ipc_message_destroy(message);
        } else if (result != AI_IPC_ERROR_TIMEOUT) {
            // Connection error, stop listening
            break;
        }
    }
    
    ALOGD_IF_DEBUG(g_debug_logging, "Listener thread stopped for connection: %s", 
                   connection->endpoint_name.c_str());
}

} // anonymous namespace

// Public API implementation
extern "C" {

ai_ipc_result_t ai_ipc_init(void) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (g_initialized) {
        return AI_IPC_SUCCESS;
    }
    
    // Initialize Android Binder
    android::ProcessState::self()->startThreadPool();
    
    g_initialized = true;
    ALOGD("AI IPC library initialized");
    return AI_IPC_SUCCESS;
}

void ai_ipc_cleanup(void) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (!g_initialized) {
        return;
    }
    
    // Clean up all events
    g_events.clear();
    
    // Clean up all shared buffers
    g_shared_buffers.clear();
    
    // Clean up all connections
    g_connections.clear();
    
    g_initialized = false;
    ALOGD("AI IPC library cleaned up");
}

void ai_ipc_get_version(int* major, int* minor, int* patch) {
    if (major) *major = AI_IPC_VERSION_MAJOR;
    if (minor) *minor = AI_IPC_VERSION_MINOR;
    if (patch) *patch = AI_IPC_VERSION_PATCH;
}

ai_ipc_result_t ai_ipc_connection_create(const ai_ipc_config_t* config, ai_ipc_connection_t** connection) {
    if (!config || !connection || !g_initialized) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    auto new_connection = std::make_unique<ai_ipc_connection_t>();
    new_connection->config = *config;
    new_connection->type = config->type;
    new_connection->endpoint_name = config->endpoint_name ? config->endpoint_name : "";
    
    // Create transport based on type
    switch (config->type) {
        case AI_IPC_CONNECTION_SOCKET: {
            ai_ipc_result_t result = create_unix_socket(new_connection->endpoint_name, 
                                                       &new_connection->socket_fd);
            if (result != AI_IPC_SUCCESS) {
                return result;
            }
            break;
        }
        case AI_IPC_CONNECTION_BINDER:
            // Binder setup would go here
            break;
        case AI_IPC_CONNECTION_SHARED_MEM:
            // Shared memory setup would go here
            break;
        case AI_IPC_CONNECTION_PIPE:
            // Named pipe setup would go here
            break;
        default:
            return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    ai_ipc_connection_t* connection_ptr = new_connection.get();
    g_connections[connection_ptr] = std::move(new_connection);
    *connection = connection_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "IPC connection created: %s", config->endpoint_name);
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_connection_connect(ai_ipc_connection_t* connection, const char* endpoint_name) {
    if (!connection || !endpoint_name) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    if (connection->is_connected) {
        return AI_IPC_SUCCESS;
    }
    
    switch (connection->type) {
        case AI_IPC_CONNECTION_SOCKET: {
            struct sockaddr_un addr;
            memset(&addr, 0, sizeof(addr));
            addr.sun_family = AF_UNIX;
            strncpy(addr.sun_path, endpoint_name, sizeof(addr.sun_path) - 1);
            
            if (connect(connection->socket_fd, reinterpret_cast<struct sockaddr*>(&addr), 
                       sizeof(addr)) < 0) {
                return AI_IPC_ERROR_CONNECTION_FAILED;
            }
            break;
        }
        default:
            return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    connection->is_connected = true;
    ALOGD_IF_DEBUG(g_debug_logging, "Connected to endpoint: %s", endpoint_name);
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_connection_listen(ai_ipc_connection_t* connection,
                                        ai_ipc_message_callback_t callback, void* user_data) {
    if (!connection || !callback) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    switch (connection->type) {
        case AI_IPC_CONNECTION_SOCKET: {
            struct sockaddr_un addr;
            memset(&addr, 0, sizeof(addr));
            addr.sun_family = AF_UNIX;
            strncpy(addr.sun_path, connection->endpoint_name.c_str(), sizeof(addr.sun_path) - 1);
            
            // Remove existing socket file
            unlink(connection->endpoint_name.c_str());
            
            if (bind(connection->socket_fd, reinterpret_cast<struct sockaddr*>(&addr), 
                    sizeof(addr)) < 0) {
                return AI_IPC_ERROR_CONNECTION_FAILED;
            }
            
            if (listen(connection->socket_fd, 5) < 0) {
                return AI_IPC_ERROR_CONNECTION_FAILED;
            }
            break;
        }
        default:
            return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    connection->message_callback = callback;
    connection->callback_user_data = user_data;
    connection->is_listening = true;
    
    // Start listener thread
    connection->listener_thread = std::make_unique<std::thread>(listener_thread_func, connection);
    
    ALOGD_IF_DEBUG(g_debug_logging, "Listening on endpoint: %s", connection->endpoint_name.c_str());
    return AI_IPC_SUCCESS;
}

void ai_ipc_connection_destroy(ai_ipc_connection_t* connection) {
    if (!connection) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_connections.find(connection);
    if (it != g_connections.end()) {
        // Stop listening
        connection->is_listening = false;
        
        g_connections.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "IPC connection destroyed");
    }
}

ai_ipc_result_t ai_ipc_send_message(ai_ipc_connection_t* connection, const ai_ipc_message_t* message) {
    if (!connection || !message) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    return send_message_impl(connection, message);
}

ai_ipc_result_t ai_ipc_receive_message(ai_ipc_connection_t* connection, ai_ipc_message_t** message, 
                                      uint32_t timeout_ms) {
    if (!connection || !message) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    // Set socket timeout
    struct timeval tv;
    tv.tv_sec = timeout_ms / 1000;
    tv.tv_usec = (timeout_ms % 1000) * 1000;
    setsockopt(connection->socket_fd, SOL_SOCKET, SO_RCVTIMEO, &tv, sizeof(tv));
    
    return receive_message_impl(connection, message);
}

ai_ipc_result_t ai_ipc_message_create(ai_ipc_message_type_t type, ai_ipc_priority_t priority,
                                     const void* payload, size_t payload_size, ai_ipc_message_t** message) {
    if (!message) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    auto new_message = std::make_unique<ai_ipc_message_t>();
    new_message->message_id = g_next_message_id.fetch_add(1);
    new_message->type = type;
    new_message->priority = priority;
    new_message->sender_pid = getpid();
    new_message->sender_uid = getuid();
    new_message->timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    if (payload && payload_size > 0) {
        new_message->payload_size = payload_size;
        new_message->payload = std::make_unique<uint8_t[]>(payload_size);
        memcpy(new_message->payload.get(), payload, payload_size);
    }
    
    *message = new_message.release();
    return AI_IPC_SUCCESS;
}

void ai_ipc_message_destroy(ai_ipc_message_t* message) {
    if (message) {
        delete message;
    }
}

ai_ipc_result_t ai_ipc_shared_buffer_create(const ai_ipc_shared_buffer_config_t* config,
                                           ai_ipc_shared_buffer_t** buffer) {
    if (!config || !buffer || !g_initialized) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    auto new_buffer = std::make_unique<ai_ipc_shared_buffer_t>();
    new_buffer->name = config->name ? config->name : "";
    new_buffer->buffer_size = config->buffer_size;
    new_buffer->read_only = config->read_only;
    new_buffer->enable_synchronization = config->enable_synchronization;
    
    // Create shared memory
    new_buffer->shm_fd = shm_open(new_buffer->name.c_str(), O_CREAT | O_RDWR, 0666);
    if (new_buffer->shm_fd < 0) {
        return AI_IPC_ERROR_OUT_OF_MEMORY;
    }
    
    if (ftruncate(new_buffer->shm_fd, config->buffer_size) < 0) {
        return AI_IPC_ERROR_OUT_OF_MEMORY;
    }
    
    ai_ipc_shared_buffer_t* buffer_ptr = new_buffer.get();
    g_shared_buffers[buffer_ptr] = std::move(new_buffer);
    *buffer = buffer_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Shared buffer created: %s (%zu bytes)", 
                   config->name, config->buffer_size);
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_shared_buffer_map(ai_ipc_shared_buffer_t* buffer, void** data) {
    if (!buffer || !data) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    if (buffer->mapped_data) {
        *data = buffer->mapped_data;
        return AI_IPC_SUCCESS;
    }
    
    int prot = buffer->read_only ? PROT_READ : (PROT_READ | PROT_WRITE);
    buffer->mapped_data = mmap(nullptr, buffer->buffer_size, prot, MAP_SHARED, buffer->shm_fd, 0);
    
    if (buffer->mapped_data == MAP_FAILED) {
        buffer->mapped_data = nullptr;
        return AI_IPC_ERROR_OUT_OF_MEMORY;
    }
    
    *data = buffer->mapped_data;
    ALOGD_IF_DEBUG(g_debug_logging, "Shared buffer mapped: %s", buffer->name.c_str());
    return AI_IPC_SUCCESS;
}

void ai_ipc_shared_buffer_destroy(ai_ipc_shared_buffer_t* buffer) {
    if (!buffer) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_shared_buffers.find(buffer);
    if (it != g_shared_buffers.end()) {
        // Unlink shared memory
        if (!buffer->name.empty()) {
            shm_unlink(buffer->name.c_str());
        }
        
        g_shared_buffers.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Shared buffer destroyed: %s", buffer->name.c_str());
    }
}

ai_ipc_result_t ai_ipc_event_create(const ai_ipc_event_config_t* config, ai_ipc_event_t** event) {
    if (!config || !event || !g_initialized) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    auto new_event = std::make_unique<ai_ipc_event_t>();
    new_event->event_name = config->event_name ? config->event_name : "";
    new_event->auto_reset = config->auto_reset;
    new_event->manual_reset = config->manual_reset;
    new_event->timeout_ms = config->timeout_ms;
    
    ai_ipc_event_t* event_ptr = new_event.get();
    g_events[event_ptr] = std::move(new_event);
    *event = event_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "IPC event created: %s", config->event_name);
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_event_signal(ai_ipc_event_t* event) {
    if (!event) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    {
        android::Mutex::Autolock lock(event->event_mutex);
        event->is_signaled = true;
    }
    event->event_condition.notify_all();
    
    ALOGD_IF_DEBUG(g_debug_logging, "Event signaled: %s", event->event_name.c_str());
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_event_wait(ai_ipc_event_t* event, uint32_t timeout_ms) {
    if (!event) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    std::unique_lock<android::Mutex> lock(event->event_mutex);
    
    if (timeout_ms == 0) {
        event->event_condition.wait(lock, [event] { return event->is_signaled.load(); });
    } else {
        auto timeout = std::chrono::milliseconds(timeout_ms);
        if (!event->event_condition.wait_for(lock, timeout, [event] { return event->is_signaled.load(); })) {
            return AI_IPC_ERROR_TIMEOUT;
        }
    }
    
    if (event->auto_reset) {
        event->is_signaled = false;
    }
    
    return AI_IPC_SUCCESS;
}

ai_ipc_result_t ai_ipc_event_reset(ai_ipc_event_t* event) {
    if (!event) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    android::Mutex::Autolock lock(event->event_mutex);
    event->is_signaled = false;
    
    return AI_IPC_SUCCESS;
}

void ai_ipc_event_destroy(ai_ipc_event_t* event) {
    if (!event) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_events.find(event);
    if (it != g_events.end()) {
        g_events.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "IPC event destroyed: %s", event->event_name.c_str());
    }
}

ai_ipc_result_t ai_ipc_get_metrics(ai_ipc_connection_t* connection, ai_ipc_metrics_t* metrics) {
    if (!connection || !metrics) {
        return AI_IPC_ERROR_INVALID_PARAM;
    }
    
    *metrics = connection->metrics;
    return AI_IPC_SUCCESS;
}

const char* ai_ipc_get_error_message(ai_ipc_result_t result) {
    switch (result) {
        case AI_IPC_SUCCESS: return "Success";
        case AI_IPC_ERROR_INVALID_PARAM: return "Invalid parameter";
        case AI_IPC_ERROR_OUT_OF_MEMORY: return "Out of memory";
        case AI_IPC_ERROR_CONNECTION_FAILED: return "Connection failed";
        case AI_IPC_ERROR_TIMEOUT: return "Operation timed out";
        case AI_IPC_ERROR_PERMISSION_DENIED: return "Permission denied";
        case AI_IPC_ERROR_BUFFER_FULL: return "Buffer full";
        case AI_IPC_ERROR_NOT_CONNECTED: return "Not connected";
        case AI_IPC_ERROR_INVALID_MESSAGE: return "Invalid message";
        default: return "Unknown error";
    }
}

void ai_ipc_set_debug_logging(bool enable) {
    g_debug_logging = enable;
    ALOGD("IPC debug logging %s", enable ? "enabled" : "disabled");
}

} // extern "C"
