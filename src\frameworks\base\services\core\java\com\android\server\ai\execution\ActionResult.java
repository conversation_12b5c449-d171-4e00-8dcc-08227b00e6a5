/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Result of a single action execution
 */
public class ActionResult implements Parcelable {
    public String actionId;
    public String actionType;
    public boolean success;
    public String errorMessage;
    public Bundle resultData;
    public long executionTime;
    public Bundle metadata;

    public ActionResult() {
        this.actionId = null;
        this.actionType = null;
        this.success = false;
        this.errorMessage = null;
        this.resultData = new Bundle();
        this.executionTime = 0;
        this.metadata = new Bundle();
    }

    public ActionResult(String actionId, String actionType) {
        this();
        this.actionId = actionId;
        this.actionType = actionType;
    }

    protected ActionResult(Parcel in) {
        actionId = in.readString();
        actionType = in.readString();
        success = in.readByte() != 0;
        errorMessage = in.readString();
        resultData = in.readBundle(getClass().getClassLoader());
        executionTime = in.readLong();
        metadata = in.readBundle(getClass().getClassLoader());
    }

    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
        @Override
        public ActionResult createFromParcel(Parcel in) {
            return new ActionResult(in);
        }

        @Override
        public ActionResult[] newArray(int size) {
            return new ActionResult[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(actionId);
        dest.writeString(actionType);
        dest.writeByte((byte) (success ? 1 : 0));
        dest.writeString(errorMessage);
        dest.writeBundle(resultData);
        dest.writeLong(executionTime);
        dest.writeBundle(metadata);
    }

    public void setSuccess(Bundle data) {
        this.success = true;
        this.resultData = data != null ? data : new Bundle();
        this.errorMessage = null;
    }

    public void setError(String errorMessage) {
        this.success = false;
        this.errorMessage = errorMessage;
        this.resultData = new Bundle();
    }

    public boolean hasError() {
        return !success || errorMessage != null;
    }
}
