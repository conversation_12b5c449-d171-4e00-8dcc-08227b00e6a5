<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- Background Dimmer -->
    <View
        android:id="@+id/background_dimmer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/jarvis_overlay_background"
        android:clickable="true"
        android:focusable="true" />

    <!-- Conversation Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:layout_marginTop="48dp"
        android:layout_marginBottom="48dp"
        android:maxWidth="600dp"
        android:maxHeight="80%">

        <com.android.systemui.jarvis.JarvisConversationView
            android:id="@+id/conversation_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:minHeight="400dp"
            android:elevation="8dp" />

    </FrameLayout>

</FrameLayout>
