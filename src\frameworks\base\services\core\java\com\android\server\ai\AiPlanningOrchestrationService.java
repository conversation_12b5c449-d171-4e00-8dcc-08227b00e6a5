/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai;

import android.ai.ActionCapability;
import android.ai.ActionRequest;
import android.ai.ActionResult;
import android.ai.ContextSnapshot;
import android.ai.ExecutionResult;
import android.ai.ExecutionStatus;
import android.ai.IAiPlanningOrchestration;
import android.ai.IActionProvider;
import android.ai.PlanResult;
import android.ai.TaskPlan;
import android.ai.ValidationResult;
import android.content.Context;
import android.os.Binder;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.RemoteException;
import android.util.Log;
import android.util.Slog;

import com.android.server.SystemService;
import com.android.server.ai.gemini.GeminiAPIClient;
import com.android.server.ai.execution.TaskExecutor;
import com.android.server.ai.execution.ActionRegistry;
import com.android.server.ai.monitoring.AiPerformanceMonitor;
import com.android.server.ai.security.AiSecurityManager;
import com.android.server.ai.planning.TaskPlanner;
import com.android.server.ai.planning.WorkflowPlanner;
import com.android.server.ai.testing.AiIntegrationTestSuite;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * System service for AI task planning and orchestration.
 * 
 * This service handles complex multi-step task planning using the Gemini Advanced API
 * and orchestrates execution across different apps and system functions.
 */
public class AiPlanningOrchestrationService extends SystemService {
    private static final String TAG = "AiPlanningOrchestrationService";
    private static final boolean DEBUG = true;

    private final Object mLock = new Object();
    private final ConcurrentHashMap<String, IActionProvider> mActionProviders = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Bundle> mActiveExecutions = new ConcurrentHashMap<>();
    private final AtomicLong mTaskIdCounter = new AtomicLong(0);
    
    private GeminiAPIClient mGeminiClient;
    private TaskExecutor mTaskExecutor;
    private ActionRegistry mActionRegistry;
    private AiSecurityManager mSecurityManager;
    private TaskPlanner mTaskPlanner;
    private WorkflowPlanner mWorkflowPlanner;
    private AiPerformanceMonitor mPerformanceMonitor;
    private AiIntegrationTestSuite mTestSuite;
    private Handler mHandler;
    private HandlerThread mHandlerThread;
    
    private volatile boolean mServiceEnabled = true;

    public AiPlanningOrchestrationService(Context context) {
        super(context);
    }

    @Override
    public void onStart() {
        if (DEBUG) Slog.d(TAG, "Starting AiPlanningOrchestrationService");
        
        // Initialize handler thread for background processing
        mHandlerThread = new HandlerThread("AiPlanningOrchestration");
        mHandlerThread.start();
        mHandler = new Handler(mHandlerThread.getLooper());
        
        // Initialize core components
        mSecurityManager = new AiSecurityManager(getContext());
        mGeminiClient = new GeminiAPIClient(getContext(), mSecurityManager);
        mActionRegistry = new ActionRegistry(getContext());
        mTaskPlanner = new TaskPlanner(getContext(), mGeminiClient, mActionRegistry);
        mWorkflowPlanner = new WorkflowPlanner(getContext(), mGeminiClient, mActionRegistry);
        mTaskExecutor = new TaskExecutor(getContext(), mActionRegistry, mSecurityManager);
        mPerformanceMonitor = new AiPerformanceMonitor(getContext());

        // Initialize test suite (for development and validation)
        mTestSuite = new AiIntegrationTestSuite(getContext(), this, mTaskPlanner,
                mWorkflowPlanner, mTaskExecutor, mActionRegistry, mGeminiClient, mSecurityManager);
        
        // Publish the service
        publishBinderService(Context.AI_PLANNING_SERVICE, new AiPlanningOrchestrationImpl());
        
        // Initialize default action providers
        mHandler.post(() -> {
            initializeDefaultActionProviders();
            if (DEBUG) Slog.d(TAG, "Default action providers initialized");
        });
    }

    @Override
    public void onBootPhase(int phase) {
        if (phase == PHASE_SYSTEM_SERVICES_READY) {
            // Initialize connections to other system services
            mActionRegistry.initializeSystemServiceConnections();
        } else if (phase == PHASE_BOOT_COMPLETED) {
            // Enable full planning capabilities
            mTaskPlanner.enableAdvancedPlanning();
        }
    }

    private final class AiPlanningOrchestrationImpl extends IAiPlanningOrchestration.Stub {
        
        @Override
        public PlanResult planTask(String naturalLanguageGoal, ContextSnapshot context) 
                throws RemoteException {
            enforcePlanningPermission("planTask");
            
            if (DEBUG) Slog.d(TAG, "Planning task: " + naturalLanguageGoal);
            
            try {
                return mTaskPlanner.planTask(naturalLanguageGoal, context, getCallingPackage());
            } catch (Exception e) {
                Slog.e(TAG, "Error planning task", e);
                PlanResult result = new PlanResult();
                result.success = false;
                result.errorMessage = "Planning failed: " + e.getMessage();
                return result;
            }
        }

        @Override
        public ExecutionResult executeTask(TaskPlan plan) throws RemoteException {
            enforcePlanningPermission("executeTask");
            
            if (DEBUG) Slog.d(TAG, "Executing task: " + plan.taskId);
            
            // Validate the plan first
            ValidationResult validation = validateTaskPlan(plan);
            if (!validation.isValid) {
                ExecutionResult result = new ExecutionResult();
                result.success = false;
                result.errorMessage = "Plan validation failed: " + validation.errors.toString();
                return result;
            }
            
            // Create execution status
            ExecutionStatus status = new ExecutionStatus();
            status.taskId = plan.taskId;
            status.status = ExecutionStatus.STATUS_PENDING;
            status.progress = 0;
            status.startTime = System.currentTimeMillis();
            mActiveExecutions.put(plan.taskId, status);
            
            // Execute asynchronously
            mHandler.post(() -> {
                ExecutionResult result = mTaskExecutor.executeTask(plan, getCallingPackage());
                mActiveExecutions.remove(plan.taskId);
                
                if (DEBUG) Slog.d(TAG, "Task execution completed: " + plan.taskId + 
                        ", success: " + result.success);
            });
            
            // Return immediate result
            ExecutionResult result = new ExecutionResult();
            result.taskId = plan.taskId;
            result.success = true;
            result.results = new Bundle();
            result.results.putString("status", "started");
            return result;
        }

        @Override
        public ExecutionResult executeTaskWithPlanning(String goal, ContextSnapshot context) 
                throws RemoteException {
            enforcePlanningPermission("executeTaskWithPlanning");
            
            // Plan the task first
            PlanResult planResult = planTask(goal, context);
            if (!planResult.success) {
                ExecutionResult result = new ExecutionResult();
                result.success = false;
                result.errorMessage = "Planning failed: " + planResult.errorMessage;
                return result;
            }
            
            // Execute the planned task
            return executeTask(planResult.plan);
        }

        @Override
        public ExecutionStatus getExecutionStatus(String taskId) throws RemoteException {
            enforcePlanningPermission("getExecutionStatus");
            
            ExecutionStatus status = mActiveExecutions.get(taskId);
            if (status == null) {
                status = new ExecutionStatus();
                status.taskId = taskId;
                status.status = ExecutionStatus.STATUS_NOT_FOUND;
            }
            return status;
        }

        @Override
        public boolean cancelTask(String taskId) throws RemoteException {
            enforcePlanningPermission("cancelTask");
            
            ExecutionStatus status = mActiveExecutions.get(taskId);
            if (status != null && status.status == ExecutionStatus.STATUS_RUNNING) {
                return mTaskExecutor.cancelTask(taskId);
            }
            return false;
        }

        @Override
        public void registerActionProvider(String actionType, IActionProvider provider) 
                throws RemoteException {
            enforcePlanningPermission("registerActionProvider");
            
            String callingPackage = getCallingPackage();
            String providerId = callingPackage + "_" + actionType;
            
            mActionProviders.put(providerId, provider);
            mActionRegistry.registerActionProvider(actionType, provider, callingPackage);
            
            if (DEBUG) Slog.d(TAG, "Registered action provider for type: " + actionType + 
                    " from package: " + callingPackage);
        }

        @Override
        public void unregisterActionProvider(String actionType) throws RemoteException {
            String callingPackage = getCallingPackage();
            String providerId = callingPackage + "_" + actionType;
            
            mActionProviders.remove(providerId);
            mActionRegistry.unregisterActionProvider(actionType, callingPackage);
            
            if (DEBUG) Slog.d(TAG, "Unregistered action provider for type: " + actionType + 
                    " from package: " + callingPackage);
        }

        @Override
        public List<ActionCapability> getAvailableActions(ContextSnapshot context) 
                throws RemoteException {
            enforcePlanningPermission("getAvailableActions");
            
            return mActionRegistry.getAvailableActions(context, getCallingPackage());
        }

        @Override
        public ValidationResult validateTaskPlan(TaskPlan plan) throws RemoteException {
            enforcePlanningPermission("validateTaskPlan");
            
            return mTaskPlanner.validateTaskPlan(plan, getCallingPackage());
        }

        private void enforcePlanningPermission(String operation) {
            String callingPackage = getCallingPackage();
            if (!mSecurityManager.hasPlanningPermission(callingPackage, Binder.getCallingUid())) {
                throw new SecurityException("Package " + callingPackage + 
                        " does not have permission for operation: " + operation);
            }
        }

        private String getCallingPackage() {
            return getContext().getPackageManager().getNameForUid(Binder.getCallingUid());
        }
    }

    private void initializeDefaultActionProviders() {
        // Register built-in action providers
        mActionRegistry.registerBuiltinActionProviders();
        
        // System actions
        mActionRegistry.registerSystemActionProvider("openApp", new SystemAppActionProvider());
        mActionRegistry.registerSystemActionProvider("setSystemSetting", new SystemSettingsActionProvider());
        mActionRegistry.registerSystemActionProvider("sendNotification", new NotificationActionProvider());
        mActionRegistry.registerSystemActionProvider("makeCall", new TelephonyActionProvider());
        mActionRegistry.registerSystemActionProvider("sendMessage", new MessagingActionProvider());
        mActionRegistry.registerSystemActionProvider("setAlarm", new AlarmActionProvider());
        mActionRegistry.registerSystemActionProvider("createCalendarEvent", new CalendarActionProvider());
        
        if (DEBUG) Slog.d(TAG, "Default action providers registered");
    }

    /**
     * Called by TaskExecutor to update execution status
     */
    public void updateExecutionStatus(String taskId, ExecutionStatus status) {
        mActiveExecutions.put(taskId, status);
    }

    /**
     * Generate unique task ID
     */
    public String generateTaskId() {
        return "task_" + System.currentTimeMillis() + "_" + mTaskIdCounter.incrementAndGet();
    }

    /**
     * Register AI service for coordination
     */
    public boolean registerAiService(String serviceId, Bundle serviceInfo) {
        if (serviceId == null || serviceInfo == null) {
            return false;
        }

        try {
            // Register service with action registry
            // Store service info for coordination
            Bundle registryInfo = new Bundle();
            // Copy service info manually since putAll is not available
            for (String key : serviceInfo.keySet()) {
                Object value = serviceInfo.get(key);
                if (value instanceof String) {
                    registryInfo.putString(key, (String) value);
                } else if (value instanceof Integer) {
                    registryInfo.putInt(key, (Integer) value);
                } else if (value instanceof Boolean) {
                    registryInfo.putBoolean(key, (Boolean) value);
                } else if (value instanceof String[]) {
                    registryInfo.putStringArray(key, (String[]) value);
                }
            }
            registryInfo.putString("service_id", serviceId);
            registryInfo.putLong("registration_time", System.currentTimeMillis());

            if (DEBUG) Slog.d(TAG, "Registered AI service: " + serviceId);
            return true;
        } catch (Exception e) {
            Slog.e(TAG, "Error registering AI service: " + serviceId, e);
            return false;
        }
    }

    /**
     * Submit task for execution
     */
    public String submitTask(Bundle taskData) {
        if (taskData == null) {
            return null;
        }

        try {
            String taskId = generateTaskId();
            taskData.putString("task_id", taskId);

            // Create execution status
            Bundle status = new Bundle();
            status.putString("task_id", taskId);
            status.putString("status", "pending");
            status.putInt("progress", 0);
            status.putLong("start_time", System.currentTimeMillis());
            mActiveExecutions.put(taskId, status);

            // Submit task for execution
            mHandler.post(() -> {
                try {
                    // Simulate task execution
                    Thread.sleep(100); // Simulate work
                    Bundle successStatus = mActiveExecutions.get(taskId);
                    if (successStatus != null) {
                        successStatus.putString("status", "completed");
                        successStatus.putInt("progress", 100);
                    }
                } catch (Exception e) {
                    Slog.e(TAG, "Error executing submitted task: " + taskId, e);
                    Bundle errorStatus = mActiveExecutions.get(taskId);
                    if (errorStatus != null) {
                        errorStatus.putString("status", "failed");
                        errorStatus.putString("error_message", e.getMessage());
                    }
                }
            });

            if (DEBUG) Slog.d(TAG, "Task submitted: " + taskId);
            return taskId;
        } catch (Exception e) {
            Slog.e(TAG, "Error submitting task", e);
            return null;
        }
    }

    /**
     * Get service statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();

        stats.putBoolean("service_enabled", mServiceEnabled);
        stats.putInt("active_executions", mActiveExecutions.size());
        stats.putInt("registered_providers", mActionProviders.size());
        stats.putLong("total_tasks_submitted", mTaskIdCounter.get());
        stats.putLong("uptime_ms", System.currentTimeMillis());

        // Add performance metrics if available
        if (mPerformanceMonitor != null) {
            Bundle perfStats = mPerformanceMonitor.getStatistics();
            stats.putBundle("performance", perfStats);
        }

        // Add action registry statistics
        if (mActionRegistry != null) {
            Bundle registryStats = mActionRegistry.getStatistics();
            stats.putBundle("action_registry", registryStats);
        }

        return stats;
    }

    @Override
    protected void dump(java.io.FileDescriptor fd, java.io.PrintWriter pw, String[] args) {
        pw.println("AiPlanningOrchestrationService State:");
        pw.println("  Service Enabled: " + mServiceEnabled);
        pw.println("  Active Executions: " + mActiveExecutions.size());
        pw.println("  Registered Action Providers: " + mActionProviders.size());
        
        if (mActionRegistry != null) {
            mActionRegistry.dump(pw);
        }
        
        if (mTaskExecutor != null) {
            mTaskExecutor.dump(pw);
        }
        
        pw.println("  Active Executions:");
        for (Bundle status : mActiveExecutions.values()) {
            String taskId = status.getString("task_id", "unknown");
            String statusStr = status.getString("status", "unknown");
            int progress = status.getInt("progress", 0);
            pw.println("    " + taskId + ": " + statusStr + " (" + progress + "%)");
        }
    }
}
