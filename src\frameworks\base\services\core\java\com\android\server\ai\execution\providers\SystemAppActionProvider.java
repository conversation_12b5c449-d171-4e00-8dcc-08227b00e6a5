/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution.providers;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Slog;

import java.util.List;

/**
 * Enhanced system action provider for opening and managing applications.
 * 
 * Provides sophisticated app launching capabilities including:
 * - Smart app discovery and launching
 * - App state management
 * - Cross-app data passing
 * - App switching optimization
 */
public class SystemAppActionProvider extends BaseActionProvider {
    private static final String TAG = "SystemAppActionProvider";
    private static final boolean DEBUG = true;
    
    // Action parameters
    private static final String PARAM_PACKAGE_NAME = "packageName";
    private static final String PARAM_ACTIVITY_NAME = "activityName";
    private static final String PARAM_APP_NAME = "appName";
    private static final String PARAM_INTENT_ACTION = "intentAction";
    private static final String PARAM_INTENT_DATA = "intentData";
    private static final String PARAM_INTENT_EXTRAS = "intentExtras";
    private static final String PARAM_LAUNCH_MODE = "launchMode";
    private static final String PARAM_WAIT_FOR_LAUNCH = "waitForLaunch";
    
    // Launch modes
    private static final String LAUNCH_MODE_NORMAL = "normal";
    private static final String LAUNCH_MODE_CLEAR_TASK = "clearTask";
    private static final String LAUNCH_MODE_NEW_TASK = "newTask";
    private static final String LAUNCH_MODE_SINGLE_TOP = "singleTop";
    
    private final PackageManager mPackageManager;
    
    public SystemAppActionProvider(Context context) {
        super(context, "openApp");
        mPackageManager = context.getPackageManager();
        
        if (DEBUG) Slog.d(TAG, "SystemAppActionProvider initialized");
    }
    
    @Override
    public ActionResult executeAction(ActionRequest request) throws RemoteException {
        if (DEBUG) Slog.d(TAG, "Executing app launch action");
        
        try {
            // Extract parameters
            String packageName = getStringParameter(request, PARAM_PACKAGE_NAME);
            String activityName = getStringParameter(request, PARAM_ACTIVITY_NAME);
            String appName = getStringParameter(request, PARAM_APP_NAME);
            String intentAction = getStringParameter(request, PARAM_INTENT_ACTION);
            String intentData = getStringParameter(request, PARAM_INTENT_DATA);
            String launchMode = getStringParameter(request, PARAM_LAUNCH_MODE, LAUNCH_MODE_NORMAL);
            boolean waitForLaunch = getBooleanParameter(request, PARAM_WAIT_FOR_LAUNCH, false);
            
            // Resolve target app
            AppLaunchTarget target = resolveAppTarget(packageName, activityName, appName, intentAction);
            if (target == null) {
                return createErrorResult("Could not resolve target app");
            }
            
            // Create launch intent
            Intent launchIntent = createLaunchIntent(target, intentAction, intentData, request.parameters);
            if (launchIntent == null) {
                return createErrorResult("Could not create launch intent");
            }
            
            // Configure launch mode
            configureLaunchMode(launchIntent, launchMode);
            
            // Execute app launch
            boolean success = launchApp(launchIntent, target, waitForLaunch);
            
            if (success) {
                Bundle results = new Bundle();
                results.putString("launchedPackage", target.packageName);
                results.putString("launchedActivity", target.activityName);
                results.putLong("launchTime", System.currentTimeMillis());
                
                return createSuccessResult("App launched successfully", results);
            } else {
                return createErrorResult("Failed to launch app");
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error executing app launch action", e);
            return createErrorResult("App launch failed: " + e.getMessage());
        }
    }
    
    @Override
    public boolean canExecuteAction(ActionRequest request) {
        try {
            String packageName = getStringParameter(request, PARAM_PACKAGE_NAME);
            String appName = getStringParameter(request, PARAM_APP_NAME);
            
            // Check if we can resolve the target app
            AppLaunchTarget target = resolveAppTarget(packageName, null, appName, null);
            return target != null;
            
        } catch (Exception e) {
            Slog.w(TAG, "Error checking if action can be executed", e);
            return false;
        }
    }
    
    @Override
    public ActionCapability getCapability() {
        ActionCapability capability = new ActionCapability();
        capability.actionType = getActionType();
        capability.description = "Launch and manage applications on the device";
        capability.requiredPermissions = new String[0]; // No special permissions required
        capability.requiresUserConfirmation = false;
        
        // Supported parameters
        capability.supportedParameters = new String[]{
            PARAM_PACKAGE_NAME,
            PARAM_ACTIVITY_NAME,
            PARAM_APP_NAME,
            PARAM_INTENT_ACTION,
            PARAM_INTENT_DATA,
            PARAM_INTENT_EXTRAS,
            PARAM_LAUNCH_MODE,
            PARAM_WAIT_FOR_LAUNCH
        };
        
        return capability;
    }
    
    /**
     * Resolve the target app for launching
     */
    private AppLaunchTarget resolveAppTarget(String packageName, String activityName, 
                                           String appName, String intentAction) {
        try {
            // Direct package name resolution
            if (packageName != null && !packageName.isEmpty()) {
                return resolveByPackageName(packageName, activityName);
            }
            
            // App name resolution
            if (appName != null && !appName.isEmpty()) {
                return resolveByAppName(appName);
            }
            
            // Intent action resolution
            if (intentAction != null && !intentAction.isEmpty()) {
                return resolveByIntentAction(intentAction);
            }
            
            return null;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error resolving app target", e);
            return null;
        }
    }
    
    /**
     * Resolve app by package name
     */
    private AppLaunchTarget resolveByPackageName(String packageName, String activityName) {
        try {
            ApplicationInfo appInfo = mPackageManager.getApplicationInfo(packageName, 0);
            
            AppLaunchTarget target = new AppLaunchTarget();
            target.packageName = packageName;
            target.appName = appInfo.loadLabel(mPackageManager).toString();
            
            if (activityName != null && !activityName.isEmpty()) {
                target.activityName = activityName;
            } else {
                // Get launcher activity
                Intent launcherIntent = mPackageManager.getLaunchIntentForPackage(packageName);
                if (launcherIntent != null && launcherIntent.getComponent() != null) {
                    target.activityName = launcherIntent.getComponent().getClassName();
                }
            }
            
            return target;
            
        } catch (PackageManager.NameNotFoundException e) {
            Slog.w(TAG, "Package not found: " + packageName);
            return null;
        }
    }
    
    /**
     * Resolve app by display name
     */
    private AppLaunchTarget resolveByAppName(String appName) {
        try {
            List<ApplicationInfo> installedApps = mPackageManager.getInstalledApplications(0);
            
            for (ApplicationInfo appInfo : installedApps) {
                String displayName = appInfo.loadLabel(mPackageManager).toString();
                
                if (displayName.equalsIgnoreCase(appName) || 
                    displayName.toLowerCase().contains(appName.toLowerCase())) {
                    
                    AppLaunchTarget target = new AppLaunchTarget();
                    target.packageName = appInfo.packageName;
                    target.appName = displayName;
                    
                    // Get launcher activity
                    Intent launcherIntent = mPackageManager.getLaunchIntentForPackage(appInfo.packageName);
                    if (launcherIntent != null && launcherIntent.getComponent() != null) {
                        target.activityName = launcherIntent.getComponent().getClassName();
                    }
                    
                    return target;
                }
            }
            
            return null;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error resolving app by name", e);
            return null;
        }
    }
    
    /**
     * Resolve app by intent action
     */
    private AppLaunchTarget resolveByIntentAction(String intentAction) {
        try {
            Intent queryIntent = new Intent(intentAction);
            List<ResolveInfo> resolveInfos = mPackageManager.queryIntentActivities(queryIntent, 0);
            
            if (!resolveInfos.isEmpty()) {
                ResolveInfo resolveInfo = resolveInfos.get(0); // Take first match
                
                AppLaunchTarget target = new AppLaunchTarget();
                target.packageName = resolveInfo.activityInfo.packageName;
                target.activityName = resolveInfo.activityInfo.name;
                target.appName = resolveInfo.loadLabel(mPackageManager).toString();
                
                return target;
            }
            
            return null;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error resolving app by intent action", e);
            return null;
        }
    }
    
    /**
     * Create launch intent for the target app
     */
    private Intent createLaunchIntent(AppLaunchTarget target, String intentAction, 
                                    String intentData, Bundle parameters) {
        try {
            Intent intent;
            
            if (intentAction != null && !intentAction.isEmpty()) {
                intent = new Intent(intentAction);
                if (intentData != null && !intentData.isEmpty()) {
                    intent.setData(android.net.Uri.parse(intentData));
                }
            } else {
                intent = new Intent(Intent.ACTION_MAIN);
                intent.addCategory(Intent.CATEGORY_LAUNCHER);
            }
            
            // Set component if we have specific activity
            if (target.activityName != null && !target.activityName.isEmpty()) {
                intent.setComponent(new ComponentName(target.packageName, target.activityName));
            } else {
                intent.setPackage(target.packageName);
            }
            
            // Add extra parameters
            Bundle intentExtras = parameters.getBundle(PARAM_INTENT_EXTRAS);
            if (intentExtras != null) {
                intent.putExtras(intentExtras);
            }
            
            return intent;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error creating launch intent", e);
            return null;
        }
    }
    
    /**
     * Configure launch mode for the intent
     */
    private void configureLaunchMode(Intent intent, String launchMode) {
        switch (launchMode) {
            case LAUNCH_MODE_CLEAR_TASK:
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
                break;
            case LAUNCH_MODE_NEW_TASK:
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                break;
            case LAUNCH_MODE_SINGLE_TOP:
                intent.addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP);
                break;
            case LAUNCH_MODE_NORMAL:
            default:
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                break;
        }
    }
    
    /**
     * Launch the app with the configured intent
     */
    private boolean launchApp(Intent intent, AppLaunchTarget target, boolean waitForLaunch) {
        try {
            mContext.startActivity(intent);
            
            if (waitForLaunch) {
                // Wait a bit for the app to launch
                Thread.sleep(2000);
                
                // Verify app is running (simplified check)
                return isAppRunning(target.packageName);
            }
            
            return true;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error launching app", e);
            return false;
        }
    }
    
    /**
     * Check if an app is currently running
     */
    private boolean isAppRunning(String packageName) {
        try {
            // This is a simplified check
            // In a real implementation, you would check with ActivityManager
            return true;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error checking if app is running", e);
            return false;
        }
    }
    
    /**
     * App launch target information
     */
    private static class AppLaunchTarget {
        String packageName;
        String activityName;
        String appName;
    }
}
