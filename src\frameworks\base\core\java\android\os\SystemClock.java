/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

/**
 * Core timekeeping facilities.
 */
public final class SystemClock {
    private static final long BOOT_TIME = System.currentTimeMillis();

    /**
     * Returns milliseconds since boot, not counting time spent in deep sleep.
     */
    public static long uptimeMillis() {
        return System.currentTimeMillis() - BOOT_TIME;
    }

    /**
     * Returns milliseconds since boot, including time spent in sleep.
     */
    public static long elapsedRealtime() {
        return System.currentTimeMillis() - BOOT_TIME;
    }

    /**
     * Returns nanoseconds since boot, not counting time spent in deep sleep.
     */
    public static long uptimeNanos() {
        return System.nanoTime();
    }

    /**
     * Returns nanoseconds since boot, including time spent in sleep.
     */
    public static long elapsedRealtimeNanos() {
        return System.nanoTime();
    }

    /**
     * Waits a given number of milliseconds (of uptimeMillis) before returning.
     */
    public static void sleep(long ms) {
        try {
            Thread.sleep(ms);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Sets the current wall time, in milliseconds.
     */
    public static boolean setCurrentTimeMillis(long millis) {
        // Mock implementation - would require system permissions
        return false;
    }

    /**
     * Returns milliseconds running in the current thread.
     */
    public static long currentThreadTimeMillis() {
        return System.currentTimeMillis();
    }
}
