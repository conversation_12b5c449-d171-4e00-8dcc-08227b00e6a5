<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/jarvis_conversation_background"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="16dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_jarvis_logo"
            android:contentDescription="@string/jarvis_logo" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="@string/jarvis_title"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/jarvis_header_text" />

        <ImageButton
            android:id="@+id/close_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/close_conversation" />

    </LinearLayout>

    <!-- Quick Actions -->
    <HorizontalScrollView
        android:id="@+id/quick_actions_scroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:scrollbars="none"
        android:paddingBottom="16dp">

        <LinearLayout
            android:id="@+id/quick_actions_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal" />

    </HorizontalScrollView>

    <!-- Conversation Area -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/conversation_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:scrollbars="vertical" />

    <!-- Status Area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingTop="8dp"
        android:paddingBottom="8dp">

        <ProgressBar
            android:id="@+id/thinking_indicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="8dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/status_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="12sp"
            android:textColor="@color/jarvis_status_text"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Input Area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/jarvis_input_background"
        android:padding="8dp">

        <EditText
            android:id="@+id/text_input"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/jarvis_input_hint"
            android:textSize="16sp"
            android:textColor="@color/jarvis_input_text"
            android:textColorHint="@color/jarvis_input_hint"
            android:background="@null"
            android:padding="12dp"
            android:maxLines="3"
            android:inputType="textCapSentences|textMultiLine"
            android:imeOptions="actionSend" />

        <ImageButton
            android:id="@+id/voice_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_voice_input"
            android:background="@drawable/jarvis_voice_button_background"
            android:layout_marginStart="8dp"
            android:contentDescription="@string/voice_input" />

        <ImageButton
            android:id="@+id/send_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_send"
            android:background="@drawable/jarvis_send_button_background"
            android:layout_marginStart="8dp"
            android:contentDescription="@string/send_message" />

    </LinearLayout>

</LinearLayout>
