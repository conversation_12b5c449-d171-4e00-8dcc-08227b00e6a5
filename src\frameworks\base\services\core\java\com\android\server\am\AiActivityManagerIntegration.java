/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.am;

import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.AiContextEngineService;
import com.android.server.ai.AiPlanningOrchestrationService;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AI integration layer for ActivityManagerService in Jarvis OS.
 * 
 * Provides AI-aware activity management, context collection from app lifecycle events,
 * and intelligent app state monitoring for the AI context engine.
 */
public class AiActivityManagerIntegration {
    private static final String TAG = "AiActivityManagerIntegration";
    private static final boolean DEBUG = true;

    // Activity state tracking for AI context
    private static final int AI_STATE_CREATED = 1;
    private static final int AI_STATE_STARTED = 2;
    private static final int AI_STATE_RESUMED = 3;
    private static final int AI_STATE_PAUSED = 4;
    private static final int AI_STATE_STOPPED = 5;
    private static final int AI_STATE_DESTROYED = 6;

    private final Context mContext;
    private final ActivityManagerService mActivityManagerService;
    private final Handler mHandler;
    
    // AI service references
    private AiContextEngineService mContextEngine;
    private AiPlanningOrchestrationService mPlanningService;
    
    // Activity tracking
    private final ConcurrentHashMap<ComponentName, ActivityInfo> mTrackedActivities = new ConcurrentHashMap<>();
    private final List<ActivityTransition> mRecentTransitions = new ArrayList<>();
    
    // Configuration
    private boolean mAiIntegrationEnabled = true;
    private boolean mContextCollectionEnabled = true;
    private boolean mActivityPredictionEnabled = true;
    private int mMaxRecentTransitions = 100;
    
    // Statistics
    private long mTotalActivityTransitions = 0;
    private long mAiPredictedTransitions = 0;
    private long mCorrectPredictions = 0;

    public AiActivityManagerIntegration(Context context, ActivityManagerService ams, Handler handler) {
        mContext = context;
        mActivityManagerService = ams;
        mHandler = handler;
        
        if (DEBUG) Slog.d(TAG, "AiActivityManagerIntegration created");
    }

    /**
     * Initialize AI service connections
     */
    public void initializeAiServices() {
        // Get references to AI services
        mContextEngine = (AiContextEngineService) mContext.getSystemService(Context.AI_CONTEXT_ENGINE_SERVICE);
        mPlanningService = (AiPlanningOrchestrationService) mContext.getSystemService(Context.AI_PLANNING_ORCHESTRATION_SERVICE);
        
        if (DEBUG) Slog.d(TAG, "AI services initialized");
    }

    /**
     * Called when an activity is being started
     */
    public void onActivityStarting(ActivityRecord r, Intent intent) {
        if (!mAiIntegrationEnabled || r == null) {
            return;
        }
        
        try {
            ComponentName component = r.getComponentName();
            String packageName = component.getPackageName();
            
            // Create activity info for tracking
            ActivityInfo activityInfo = new ActivityInfo(component, AI_STATE_STARTED, 
                SystemClock.elapsedRealtime(), intent);
            mTrackedActivities.put(component, activityInfo);
            
            // Collect context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createActivityContextData(r, "activity_starting", intent);
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            // Record activity transition
            recordActivityTransition(component, "starting", intent);
            
            // Check for AI predictions
            if (mActivityPredictionEnabled && mPlanningService != null) {
                checkActivityPrediction(component, "starting");
            }
            
            if (DEBUG) Slog.d(TAG, "Activity starting: " + component.flattenToShortString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onActivityStarting", e);
        }
    }

    /**
     * Called when an activity has been resumed
     */
    public void onActivityResumed(ActivityRecord r) {
        if (!mAiIntegrationEnabled || r == null) {
            return;
        }
        
        try {
            ComponentName component = r.getComponentName();
            
            // Update activity state
            ActivityInfo activityInfo = mTrackedActivities.get(component);
            if (activityInfo != null) {
                activityInfo.updateState(AI_STATE_RESUMED, SystemClock.elapsedRealtime());
            }
            
            // Collect context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createActivityContextData(r, "activity_resumed", null);
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            // Record activity transition
            recordActivityTransition(component, "resumed", null);
            
            // Notify AI services of foreground app change
            if (mPlanningService != null) {
                mPlanningService.onForegroundAppChanged(component.getPackageName(), 
                    component.getClassName());
            }
            
            if (DEBUG) Slog.d(TAG, "Activity resumed: " + component.flattenToShortString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onActivityResumed", e);
        }
    }

    /**
     * Called when an activity has been paused
     */
    public void onActivityPaused(ActivityRecord r) {
        if (!mAiIntegrationEnabled || r == null) {
            return;
        }
        
        try {
            ComponentName component = r.getComponentName();
            
            // Update activity state
            ActivityInfo activityInfo = mTrackedActivities.get(component);
            if (activityInfo != null) {
                activityInfo.updateState(AI_STATE_PAUSED, SystemClock.elapsedRealtime());
            }
            
            // Collect context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createActivityContextData(r, "activity_paused", null);
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            // Record activity transition
            recordActivityTransition(component, "paused", null);
            
            if (DEBUG) Slog.d(TAG, "Activity paused: " + component.flattenToShortString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onActivityPaused", e);
        }
    }

    /**
     * Called when an activity is being stopped
     */
    public void onActivityStopped(ActivityRecord r) {
        if (!mAiIntegrationEnabled || r == null) {
            return;
        }
        
        try {
            ComponentName component = r.getComponentName();
            
            // Update activity state
            ActivityInfo activityInfo = mTrackedActivities.get(component);
            if (activityInfo != null) {
                activityInfo.updateState(AI_STATE_STOPPED, SystemClock.elapsedRealtime());
            }
            
            // Collect context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createActivityContextData(r, "activity_stopped", null);
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            // Record activity transition
            recordActivityTransition(component, "stopped", null);
            
            if (DEBUG) Slog.d(TAG, "Activity stopped: " + component.flattenToShortString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onActivityStopped", e);
        }
    }

    /**
     * Called when an activity is being destroyed
     */
    public void onActivityDestroyed(ActivityRecord r) {
        if (!mAiIntegrationEnabled || r == null) {
            return;
        }
        
        try {
            ComponentName component = r.getComponentName();
            
            // Update activity state
            ActivityInfo activityInfo = mTrackedActivities.get(component);
            if (activityInfo != null) {
                activityInfo.updateState(AI_STATE_DESTROYED, SystemClock.elapsedRealtime());
            }
            
            // Collect context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createActivityContextData(r, "activity_destroyed", null);
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            // Record activity transition
            recordActivityTransition(component, "destroyed", null);
            
            // Clean up tracking
            mTrackedActivities.remove(component);
            
            if (DEBUG) Slog.d(TAG, "Activity destroyed: " + component.flattenToShortString());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onActivityDestroyed", e);
        }
    }

    /**
     * Called when a process is being started
     */
    public void onProcessStarted(ProcessRecord proc) {
        if (!mAiIntegrationEnabled || proc == null) {
            return;
        }
        
        try {
            // Collect process context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createProcessContextData(proc, "process_started");
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            if (DEBUG) Slog.d(TAG, "Process started: " + proc.processName);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onProcessStarted", e);
        }
    }

    /**
     * Called when a process has died
     */
    public void onProcessDied(ProcessRecord proc) {
        if (!mAiIntegrationEnabled || proc == null) {
            return;
        }
        
        try {
            // Collect process context for AI
            if (mContextCollectionEnabled && mContextEngine != null) {
                Bundle contextData = createProcessContextData(proc, "process_died");
                mContextEngine.updateContext("activity_manager", contextData);
            }
            
            if (DEBUG) Slog.d(TAG, "Process died: " + proc.processName);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onProcessDied", e);
        }
    }

    /**
     * Get AI-suggested next activity based on current context
     */
    public ComponentName getAiSuggestedActivity(String currentPackage, String currentActivity) {
        if (!mActivityPredictionEnabled || mPlanningService == null) {
            return null;
        }
        
        try {
            // Request AI prediction for next likely activity
            Bundle predictionRequest = new Bundle();
            predictionRequest.putString("current_package", currentPackage);
            predictionRequest.putString("current_activity", currentActivity);
            predictionRequest.putLong("timestamp", System.currentTimeMillis());
            
            // Add recent transition history
            addRecentTransitionsToBundle(predictionRequest);
            
            // Get prediction from AI planning service
            Bundle prediction = mPlanningService.predictNextActivity(predictionRequest);
            if (prediction != null) {
                String suggestedPackage = prediction.getString("suggested_package");
                String suggestedActivity = prediction.getString("suggested_activity");
                float confidence = prediction.getFloat("confidence", 0.0f);
                
                if (suggestedPackage != null && suggestedActivity != null && confidence > 0.7f) {
                    ComponentName suggested = new ComponentName(suggestedPackage, suggestedActivity);
                    
                    if (DEBUG) Slog.d(TAG, "AI suggested activity: " + suggested.flattenToShortString() + 
                        " (confidence: " + confidence + ")");
                    
                    return suggested;
                }
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error getting AI activity suggestion", e);
        }
        
        return null;
    }

    /**
     * Enable or disable AI integration
     */
    public void setAiIntegrationEnabled(boolean enabled) {
        mAiIntegrationEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "AI integration " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Enable or disable context collection
     */
    public void setContextCollectionEnabled(boolean enabled) {
        mContextCollectionEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "Context collection " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Get integration statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("ai_integration_enabled", mAiIntegrationEnabled);
        stats.putBoolean("context_collection_enabled", mContextCollectionEnabled);
        stats.putBoolean("activity_prediction_enabled", mActivityPredictionEnabled);
        stats.putInt("tracked_activities", mTrackedActivities.size());
        stats.putInt("recent_transitions", mRecentTransitions.size());
        stats.putLong("total_transitions", mTotalActivityTransitions);
        stats.putLong("ai_predicted_transitions", mAiPredictedTransitions);
        stats.putLong("correct_predictions", mCorrectPredictions);
        
        if (mTotalActivityTransitions > 0) {
            float predictionRate = (float) mAiPredictedTransitions / mTotalActivityTransitions * 100.0f;
            stats.putFloat("prediction_rate", predictionRate);
        }
        
        if (mAiPredictedTransitions > 0) {
            float accuracy = (float) mCorrectPredictions / mAiPredictedTransitions * 100.0f;
            stats.putFloat("prediction_accuracy", accuracy);
        }
        
        return stats;
    }

    /**
     * Dump integration state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("  AiActivityManagerIntegration State:");
        pw.println("    AI Integration Enabled: " + mAiIntegrationEnabled);
        pw.println("    Context Collection Enabled: " + mContextCollectionEnabled);
        pw.println("    Activity Prediction Enabled: " + mActivityPredictionEnabled);
        pw.println("    Tracked Activities: " + mTrackedActivities.size());
        pw.println("    Recent Transitions: " + mRecentTransitions.size());
        pw.println("    Total Transitions: " + mTotalActivityTransitions);
        pw.println("    AI Predicted Transitions: " + mAiPredictedTransitions);
        pw.println("    Correct Predictions: " + mCorrectPredictions);
        
        if (mTotalActivityTransitions > 0) {
            float predictionRate = (float) mAiPredictedTransitions / mTotalActivityTransitions * 100.0f;
            pw.println("    Prediction Rate: " + predictionRate + "%");
        }
        
        if (mAiPredictedTransitions > 0) {
            float accuracy = (float) mCorrectPredictions / mAiPredictedTransitions * 100.0f;
            pw.println("    Prediction Accuracy: " + accuracy + "%");
        }
    }

    // Private helper methods

    private Bundle createActivityContextData(ActivityRecord r, String event, Intent intent) {
        Bundle contextData = new Bundle();
        contextData.putString("event_type", event);
        contextData.putString("package_name", r.getPackageName());
        contextData.putString("activity_name", r.getComponentName().getClassName());
        contextData.putLong("timestamp", System.currentTimeMillis());
        contextData.putInt("task_id", r.getTaskRecord().taskId);
        contextData.putInt("user_id", r.mUserId);
        
        if (intent != null) {
            contextData.putString("intent_action", intent.getAction());
            if (intent.getCategories() != null) {
                contextData.putStringArray("intent_categories", 
                    intent.getCategories().toArray(new String[0]));
            }
        }
        
        return contextData;
    }

    private Bundle createProcessContextData(ProcessRecord proc, String event) {
        Bundle contextData = new Bundle();
        contextData.putString("event_type", event);
        contextData.putString("process_name", proc.processName);
        contextData.putInt("pid", proc.pid);
        contextData.putInt("uid", proc.uid);
        contextData.putLong("timestamp", System.currentTimeMillis());
        
        return contextData;
    }

    private void recordActivityTransition(ComponentName component, String transition, Intent intent) {
        ActivityTransition activityTransition = new ActivityTransition(component, transition, 
            System.currentTimeMillis(), intent);
        
        synchronized (mRecentTransitions) {
            mRecentTransitions.add(activityTransition);
            
            // Keep only recent transitions
            if (mRecentTransitions.size() > mMaxRecentTransitions) {
                mRecentTransitions.remove(0);
            }
        }
        
        mTotalActivityTransitions++;
    }

    private void checkActivityPrediction(ComponentName component, String transition) {
        // Check if this transition was predicted by AI
        // This would involve comparing with recent AI predictions
        // For now, just increment the counter
        mAiPredictedTransitions++;
    }

    private void addRecentTransitionsToBundle(Bundle bundle) {
        synchronized (mRecentTransitions) {
            if (!mRecentTransitions.isEmpty()) {
                ArrayList<Bundle> transitions = new ArrayList<>();
                for (ActivityTransition transition : mRecentTransitions) {
                    Bundle transitionBundle = new Bundle();
                    transitionBundle.putString("package", transition.component.getPackageName());
                    transitionBundle.putString("activity", transition.component.getClassName());
                    transitionBundle.putString("transition", transition.transition);
                    transitionBundle.putLong("timestamp", transition.timestamp);
                    transitions.add(transitionBundle);
                }
                bundle.putParcelableArrayList("recent_transitions", transitions);
            }
        }
    }

    // Inner classes

    private static class ActivityInfo {
        final ComponentName component;
        int currentState;
        long lastStateChange;
        Intent lastIntent;
        
        ActivityInfo(ComponentName component, int initialState, long timestamp, Intent intent) {
            this.component = component;
            this.currentState = initialState;
            this.lastStateChange = timestamp;
            this.lastIntent = intent;
        }
        
        void updateState(int newState, long timestamp) {
            this.currentState = newState;
            this.lastStateChange = timestamp;
        }
    }

    private static class ActivityTransition {
        final ComponentName component;
        final String transition;
        final long timestamp;
        final Intent intent;
        
        ActivityTransition(ComponentName component, String transition, long timestamp, Intent intent) {
            this.component = component;
            this.transition = transition;
            this.timestamp = timestamp;
            this.intent = intent;
        }
    }
}
