/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.os.Parcel;
import android.os.Parcelable;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Represents a single message in the Jarvis conversation.
 * 
 * Contains the message content, sender information, timestamp,
 * and any additional metadata for display and processing.
 */
public class ConversationMessage implements Parcelable {
    
    public enum MessageType {
        TEXT,
        VOICE,
        ACTION_RESULT,
        SYSTEM_INFO,
        ERROR
    }
    
    public enum MessageStatus {
        SENT,
        DELIVERED,
        PROCESSING,
        COMPLETED,
        FAILED
    }
    
    private String mContent;
    private boolean mIsUser;
    private long mTimestamp;
    private MessageType mType;
    private MessageStatus mStatus;
    private String mMessageId;
    private String mActionId; // For action-related messages
    private boolean mIsImportant;
    
    public ConversationMessage(String content, boolean isUser, long timestamp) {
        this(content, isUser, timestamp, MessageType.TEXT);
    }
    
    public ConversationMessage(String content, boolean isUser, long timestamp, MessageType type) {
        mContent = content;
        mIsUser = isUser;
        mTimestamp = timestamp;
        mType = type;
        mStatus = isUser ? MessageStatus.SENT : MessageStatus.DELIVERED;
        mMessageId = generateMessageId();
        mIsImportant = false;
    }
    
    // Parcelable constructor
    protected ConversationMessage(Parcel in) {
        mContent = in.readString();
        mIsUser = in.readByte() != 0;
        mTimestamp = in.readLong();
        mType = MessageType.valueOf(in.readString());
        mStatus = MessageStatus.valueOf(in.readString());
        mMessageId = in.readString();
        mActionId = in.readString();
        mIsImportant = in.readByte() != 0;
    }
    
    public static final Creator<ConversationMessage> CREATOR = new Creator<ConversationMessage>() {
        @Override
        public ConversationMessage createFromParcel(Parcel in) {
            return new ConversationMessage(in);
        }
        
        @Override
        public ConversationMessage[] newArray(int size) {
            return new ConversationMessage[size];
        }
    };
    
    private String generateMessageId() {
        return "msg_" + System.currentTimeMillis() + "_" + hashCode();
    }
    
    // Getters
    public String getContent() {
        return mContent;
    }
    
    public boolean isUser() {
        return mIsUser;
    }
    
    public long getTimestamp() {
        return mTimestamp;
    }
    
    public MessageType getType() {
        return mType;
    }
    
    public MessageStatus getStatus() {
        return mStatus;
    }
    
    public String getMessageId() {
        return mMessageId;
    }
    
    public String getActionId() {
        return mActionId;
    }
    
    public boolean isImportant() {
        return mIsImportant;
    }
    
    // Setters
    public void setContent(String content) {
        mContent = content;
    }
    
    public void setStatus(MessageStatus status) {
        mStatus = status;
    }
    
    public void setActionId(String actionId) {
        mActionId = actionId;
    }
    
    public void setImportant(boolean important) {
        mIsImportant = important;
    }
    
    // Utility methods
    public String getFormattedTimestamp() {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return formatter.format(new Date(mTimestamp));
    }
    
    public String getFormattedDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
        return formatter.format(new Date(mTimestamp));
    }
    
    public boolean isToday() {
        long now = System.currentTimeMillis();
        long dayStart = now - (now % (24 * 60 * 60 * 1000));
        return mTimestamp >= dayStart;
    }
    
    public boolean isRecent() {
        return System.currentTimeMillis() - mTimestamp < 5 * 60 * 1000; // 5 minutes
    }
    
    public boolean isSystemMessage() {
        return mType == MessageType.SYSTEM_INFO || mType == MessageType.ERROR;
    }
    
    public boolean isActionMessage() {
        return mType == MessageType.ACTION_RESULT && mActionId != null;
    }
    
    public boolean isPending() {
        return mStatus == MessageStatus.PROCESSING;
    }
    
    public boolean isFailed() {
        return mStatus == MessageStatus.FAILED;
    }
    
    @Override
    public String toString() {
        return "ConversationMessage{" +
                "content='" + mContent + '\'' +
                ", isUser=" + mIsUser +
                ", timestamp=" + mTimestamp +
                ", type=" + mType +
                ", status=" + mStatus +
                ", messageId='" + mMessageId + '\'' +
                ", actionId='" + mActionId + '\'' +
                ", isImportant=" + mIsImportant +
                '}';
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ConversationMessage that = (ConversationMessage) obj;
        return mMessageId != null ? mMessageId.equals(that.mMessageId) : that.mMessageId == null;
    }
    
    @Override
    public int hashCode() {
        return mMessageId != null ? mMessageId.hashCode() : 0;
    }
    
    // Parcelable implementation
    @Override
    public int describeContents() {
        return 0;
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mContent);
        dest.writeByte((byte) (mIsUser ? 1 : 0));
        dest.writeLong(mTimestamp);
        dest.writeString(mType.name());
        dest.writeString(mStatus.name());
        dest.writeString(mMessageId);
        dest.writeString(mActionId);
        dest.writeByte((byte) (mIsImportant ? 1 : 0));
    }
    
    // Builder pattern for complex message creation
    public static class Builder {
        private String content;
        private boolean isUser;
        private long timestamp = System.currentTimeMillis();
        private MessageType type = MessageType.TEXT;
        private String actionId;
        private boolean isImportant = false;
        
        public Builder(String content, boolean isUser) {
            this.content = content;
            this.isUser = isUser;
        }
        
        public Builder setTimestamp(long timestamp) {
            this.timestamp = timestamp;
            return this;
        }
        
        public Builder setType(MessageType type) {
            this.type = type;
            return this;
        }
        
        public Builder setActionId(String actionId) {
            this.actionId = actionId;
            return this;
        }
        
        public Builder setImportant(boolean important) {
            this.isImportant = important;
            return this;
        }
        
        public ConversationMessage build() {
            ConversationMessage message = new ConversationMessage(content, isUser, timestamp, type);
            message.setActionId(actionId);
            message.setImportant(isImportant);
            return message;
        }
    }
    
    // Factory methods for common message types
    public static ConversationMessage createUserText(String content) {
        return new ConversationMessage(content, true, System.currentTimeMillis(), MessageType.TEXT);
    }
    
    public static ConversationMessage createUserVoice(String content) {
        return new ConversationMessage(content, true, System.currentTimeMillis(), MessageType.VOICE);
    }
    
    public static ConversationMessage createAiResponse(String content) {
        return new ConversationMessage(content, false, System.currentTimeMillis(), MessageType.TEXT);
    }
    
    public static ConversationMessage createActionResult(String content, String actionId) {
        ConversationMessage message = new ConversationMessage(content, false, System.currentTimeMillis(), MessageType.ACTION_RESULT);
        message.setActionId(actionId);
        return message;
    }
    
    public static ConversationMessage createSystemInfo(String content) {
        return new ConversationMessage(content, false, System.currentTimeMillis(), MessageType.SYSTEM_INFO);
    }
    
    public static ConversationMessage createError(String content) {
        ConversationMessage message = new ConversationMessage(content, false, System.currentTimeMillis(), MessageType.ERROR);
        message.setImportant(true);
        return message;
    }
}
