<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow -->
    <item android:top="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_card_shadow" />
            <corners 
                android:topLeftRadius="@dimen/jarvis_corner_radius_xl"
                android:topRightRadius="@dimen/jarvis_corner_radius_xl" />
        </shape>
    </item>
    
    <!-- Background -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_nav_background" />
            <corners 
                android:topLeftRadius="@dimen/jarvis_corner_radius_xl"
                android:topRightRadius="@dimen/jarvis_corner_radius_xl" />
        </shape>
    </item>
    
    <!-- Glassmorphism Effect -->
    <item android:bottom="2dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#20FFFFFF"
                android:endColor="#10FFFFFF" />
            <corners 
                android:topLeftRadius="@dimen/jarvis_corner_radius_xl"
                android:topRightRadius="@dimen/jarvis_corner_radius_xl" />
        </shape>
    </item>
    
</layer-list>
