/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.gemini;

/**
 * Represents a request to the Gemini API.
 * 
 * Encapsulates the endpoint, request body, and metadata for API calls.
 */
public class GeminiRequest {
    private final String mEndpoint;
    private final String mBody;
    private final String mPackageName;
    private final long mTimestamp;
    private final String mRequestId;
    
    public GeminiRequest(String endpoint, String body, String packageName) {
        mEndpoint = endpoint;
        mBody = body;
        mPackageName = packageName;
        mTimestamp = System.currentTimeMillis();
        mRequestId = generateRequestId();
    }
    
    public String getEndpoint() {
        return mEndpoint;
    }
    
    public String getBody() {
        return mBody;
    }
    
    public String getPackageName() {
        return mPackageName;
    }
    
    public long getTimestamp() {
        return mTimestamp;
    }
    
    public String getRequestId() {
        return mRequestId;
    }
    
    private String generateRequestId() {
        return "req_" + mTimestamp + "_" + hashCode();
    }
    
    @Override
    public String toString() {
        return "GeminiRequest{" +
                "endpoint='" + mEndpoint + '\'' +
                ", packageName='" + mPackageName + '\'' +
                ", timestamp=" + mTimestamp +
                ", requestId='" + mRequestId + '\'' +
                '}';
    }
}
