/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

/**
 * Base interface for a remotable object, the core part of a lightweight
 * remote procedure call mechanism designed for high performance when
 * performing in-process and cross-process calls.
 */
public interface IBinder {
    /**
     * The first transaction code available for user commands.
     */
    int FIRST_CALL_TRANSACTION = 0x00000001;

    /**
     * The last transaction code available for user commands.
     */
    int LAST_CALL_TRANSACTION = 0x00ffffff;

    /**
     * IBinder protocol transaction code: pingBinder().
     */
    int PING_TRANSACTION = ('_'<<24)|('P'<<16)|('N'<<8)|'G';

    /**
     * IBinder protocol transaction code: dump internal state.
     */
    int DUMP_TRANSACTION = ('_'<<24)|('D'<<16)|('M'<<8)|'P';

    /**
     * IBinder protocol transaction code: interrogate the recipient side
     * of the transaction for its canonical interface descriptor.
     */
    int INTERFACE_TRANSACTION = ('_'<<24)|('N'<<16)|('T'<<8)|'F';

    /**
     * Flag to transact(): this is a one-way call, meaning that the
     * caller returns immediately, without waiting for a result from the
     * callee.
     */
    int FLAG_ONEWAY = 0x00000001;

    /**
     * Get the canonical name of the interface supported by this binder.
     */
    public String getInterfaceDescriptor() throws RemoteException;

    /**
     * Check to see if the process that the binder is in is still alive.
     */
    public boolean pingBinder();

    /**
     * Check to see if the object still exists.
     */
    public boolean isBinderAlive();

    /**
     * Attempt to retrieve a local implementation of an interface
     * for this Binder object.
     */
    public IInterface queryLocalInterface(String descriptor);

    /**
     * Print the object's state into the given stream.
     */
    public void dump(java.io.FileDescriptor fd, String[] args) throws RemoteException;

    /**
     * Like dump(), but ensures the target executes asynchronously.
     */
    public void dumpAsync(java.io.FileDescriptor fd, String[] args) throws RemoteException;

    /**
     * Perform a generic operation with the object.
     */
    public boolean transact(int code, Parcel data, Parcel reply, int flags)
        throws RemoteException;

    /**
     * Register the recipient for a notification if this binder
     * goes away.
     */
    public void linkToDeath(DeathRecipient recipient, int flags)
        throws RemoteException;

    /**
     * Remove a previously registered death notification.
     */
    public boolean unlinkToDeath(DeathRecipient recipient, int flags);

    /**
     * Interface for receiving a callback when the process hosting an IBinder has gone away.
     */
    public interface DeathRecipient {
        public void binderDied();
    }
}
