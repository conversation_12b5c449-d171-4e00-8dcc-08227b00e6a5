/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Result of task planning operation
 */
public class PlanResult implements Parcelable {
    public boolean success;
    public TaskPlan plan;
    public String errorMessage;
    public double confidence;
    public List<String> warnings;
    public Bundle alternatives;
    public long estimatedDuration;

    public PlanResult() {
        this.success = false;
        this.plan = null;
        this.errorMessage = null;
        this.confidence = 0.0;
        this.warnings = new ArrayList<>();
        this.alternatives = new Bundle();
        this.estimatedDuration = 0;
    }

    protected PlanResult(Parcel in) {
        success = in.readByte() != 0;
        plan = in.readParcelable(TaskPlan.class.getClassLoader());
        errorMessage = in.readString();
        confidence = in.readDouble();
        warnings = in.createStringArrayList();
        alternatives = in.readBundle(getClass().getClassLoader());
        estimatedDuration = in.readLong();
    }

    public static final Creator<PlanResult> CREATOR = new Creator<PlanResult>() {
        @Override
        public PlanResult createFromParcel(Parcel in) {
            return new PlanResult(in);
        }

        @Override
        public PlanResult[] newArray(int size) {
            return new PlanResult[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeByte((byte) (success ? 1 : 0));
        dest.writeParcelable(plan, flags);
        dest.writeString(errorMessage);
        dest.writeDouble(confidence);
        dest.writeStringList(warnings);
        dest.writeBundle(alternatives);
        dest.writeLong(estimatedDuration);
    }
}
