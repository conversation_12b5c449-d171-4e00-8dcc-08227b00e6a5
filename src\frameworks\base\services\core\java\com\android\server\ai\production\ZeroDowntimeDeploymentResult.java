/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Result of zero-downtime deployment operations
 */
public class ZeroDowntimeDeploymentResult implements Parcelable {
    private boolean mSuccess;
    private String mMessage;
    private String mErrorMessage;
    private Bundle mMetrics;
    private long mTimestamp;
    
    public ZeroDowntimeDeploymentResult() {
        mMetrics = new Bundle();
        mTimestamp = System.currentTimeMillis();
    }
    
    public ZeroDowntimeDeploymentResult(Parcel in) {
        mSuccess = in.readBoolean();
        mMessage = in.readString();
        mErrorMessage = in.readString();
        mMetrics = in.readBundle(getClass().getClassLoader());
        mTimestamp = in.readLong();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeBoolean(mSuccess);
        dest.writeString(mMessage);
        dest.writeString(mErrorMessage);
        dest.writeBundle(mMetrics);
        dest.writeLong(mTimestamp);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<ZeroDowntimeDeploymentResult> CREATOR = new Creator<ZeroDowntimeDeploymentResult>() {
        @Override
        public ZeroDowntimeDeploymentResult createFromParcel(Parcel in) {
            return new ZeroDowntimeDeploymentResult(in);
        }
        
        @Override
        public ZeroDowntimeDeploymentResult[] newArray(int size) {
            return new ZeroDowntimeDeploymentResult[size];
        }
    };
    
    // Getters and setters
    public boolean isSuccess() { return mSuccess; }
    public void setSuccess(boolean success) { mSuccess = success; }
    
    public String getMessage() { return mMessage; }
    public void setMessage(String message) { mMessage = message; }
    
    public String getErrorMessage() { return mErrorMessage; }
    public void setErrorMessage(String errorMessage) { mErrorMessage = errorMessage; }
    
    public Bundle getMetrics() { return mMetrics; }
    public void setMetrics(Bundle metrics) { mMetrics = metrics; }
    
    public long getTimestamp() { return mTimestamp; }
    public void setTimestamp(long timestamp) { mTimestamp = timestamp; }
}
