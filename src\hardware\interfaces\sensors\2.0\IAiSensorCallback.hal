/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware.sensors@2.0;

/**
 * AI Sensor Callback Interface for Jarvis OS
 * 
 * Provides callbacks for AI-enhanced sensor data, events, and status updates.
 */
interface IAiSensorCallback {
    /**
     * Sensor event types
     */
    enum EventType : uint32_t {
        DATA_AVAILABLE = 0,
        MOTION_DETECTED = 1,
        GESTURE_RECOGNIZED = 2,
        CONTEXT_CHANGED = 3,
        CALIBRATION_COMPLETE = 4,
        LEARNING_UPDATE = 5,
        ERROR_OCCURRED = 6,
        POWER_STATE_CHANGED = 7,
    };

    /**
     * Sensor event data structure
     */
    struct SensorEvent {
        EventType eventType;
        uint32_t sensorHandle;
        uint64_t timestamp;
        vec<uint8_t> eventData;
        string description;
    };

    /**
     * Called when AI sensor data is available
     * 
     * @param sensorHandle Handle of the sensor
     * @param data AI-enhanced sensor data
     */
    oneway onAiSensorData(uint32_t sensorHandle, AiSensorData data);

    /**
     * Called when motion is detected and classified
     * 
     * @param classification Motion classification result
     */
    oneway onMotionDetected(MotionClassification classification);

    /**
     * Called when a gesture is recognized
     * 
     * @param gesture Gesture recognition result
     */
    oneway onGestureRecognized(GestureRecognition gesture);

    /**
     * Called when environmental context changes
     * 
     * @param context New environmental context
     */
    oneway onContextChanged(EnvironmentalContext context);

    /**
     * Called when sensor calibration completes
     * 
     * @param sensorHandle Handle of the calibrated sensor
     * @param successful True if calibration succeeded
     * @param calibrationData Calibration parameters
     */
    oneway onCalibrationComplete(uint32_t sensorHandle, bool successful, vec<float> calibrationData);

    /**
     * Called when AI learning model is updated
     * 
     * @param sensorHandle Handle of the sensor
     * @param learningProgress Progress percentage (0-100)
     * @param accuracy Current model accuracy (0.0-1.0)
     */
    oneway onLearningUpdate(uint32_t sensorHandle, uint32_t learningProgress, float accuracy);

    /**
     * Called when a sensor error occurs
     * 
     * @param event Error event information
     */
    oneway onSensorError(SensorEvent event);

    /**
     * Called when sensor power state changes
     * 
     * @param sensorHandle Handle of the sensor
     * @param powerMode New power mode
     * @param powerConsumptionMw Current power consumption
     */
    oneway onPowerStateChanged(uint32_t sensorHandle, uint32_t powerMode, float powerConsumptionMw);

    /**
     * Called with periodic sensor status updates
     * 
     * @param sensorHandle Handle of the sensor
     * @param isActive True if sensor is actively collecting data
     * @param dataRate Current data rate in Hz
     * @param accuracy Current accuracy level (0-3)
     * @param confidence Current confidence level (0.0-1.0)
     */
    oneway onStatusUpdate(uint32_t sensorHandle, bool isActive, float dataRate, 
                         uint32_t accuracy, float confidence);
};
