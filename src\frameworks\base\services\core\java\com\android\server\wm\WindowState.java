/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.wm;

import android.content.ComponentName;
import android.graphics.Rect;
import android.view.WindowManager;

/**
 * Mock implementation of WindowState for Jarvis OS compilation.
 * This is a stub implementation for development and testing.
 */
public class WindowState {
    private final WindowManager.LayoutParams mAttrs;
    private final ComponentName mComponentName;
    private final Rect mBounds;
    private final int mDisplayId;
    private boolean mHasFocus;
    private boolean mVisible;

    public WindowState(WindowManager.LayoutParams attrs, ComponentName componentName, int displayId) {
        mAttrs = attrs != null ? attrs : new WindowManager.LayoutParams();
        mComponentName = componentName;
        mDisplayId = displayId;
        mBounds = new Rect(0, 0, 100, 100); // Default bounds
        mHasFocus = false;
        mVisible = true;
    }

    /**
     * Get the window attributes
     */
    public WindowManager.LayoutParams getAttrs() {
        return mAttrs;
    }

    /**
     * Get the component name associated with this window
     */
    public ComponentName getComponentName() {
        return mComponentName;
    }

    /**
     * Get the display ID this window is on
     */
    public int getDisplayId() {
        return mDisplayId;
    }

    /**
     * Get the window bounds
     */
    public Rect getBounds() {
        return new Rect(mBounds);
    }

    /**
     * Set the window bounds
     */
    public void setBounds(Rect bounds) {
        if (bounds != null) {
            mBounds.set(bounds);
        }
    }

    /**
     * Check if this window has focus
     */
    public boolean hasFocus() {
        return mHasFocus;
    }

    /**
     * Set the focus state of this window
     */
    public void setFocus(boolean hasFocus) {
        mHasFocus = hasFocus;
    }

    /**
     * Check if this window is visible
     */
    public boolean isVisible() {
        return mVisible;
    }

    /**
     * Set the visibility of this window
     */
    public void setVisible(boolean visible) {
        mVisible = visible;
    }

    /**
     * Get the window type
     */
    public int getWindowType() {
        return mAttrs.type;
    }

    /**
     * Get the window flags
     */
    public int getFlags() {
        return mAttrs.flags;
    }

    /**
     * Check if this is an application window
     */
    public boolean isApplicationWindow() {
        return mAttrs.type >= WindowManager.LayoutParams.FIRST_APPLICATION_WINDOW &&
               mAttrs.type <= WindowManager.LayoutParams.LAST_APPLICATION_WINDOW;
    }

    /**
     * Check if this is a system window
     */
    public boolean isSystemWindow() {
        return mAttrs.type >= WindowManager.LayoutParams.FIRST_SYSTEM_WINDOW &&
               mAttrs.type <= WindowManager.LayoutParams.LAST_SYSTEM_WINDOW;
    }

    /**
     * Check if this is a sub window
     */
    public boolean isSubWindow() {
        return mAttrs.type >= WindowManager.LayoutParams.FIRST_SUB_WINDOW &&
               mAttrs.type <= WindowManager.LayoutParams.LAST_SUB_WINDOW;
    }

    /**
     * Get the package name for this window
     */
    public String getPackageName() {
        if (mComponentName != null) {
            return mComponentName.getPackageName();
        }
        return mAttrs.packageName;
    }

    /**
     * Get the activity name for this window
     */
    public String getActivityName() {
        if (mComponentName != null) {
            return mComponentName.getClassName();
        }
        return "unknown";
    }

    /**
     * Check if window can receive touch events
     */
    public boolean canReceiveTouchEvents() {
        return (mAttrs.flags & WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE) == 0;
    }

    /**
     * Check if window is focusable
     */
    public boolean isFocusable() {
        return (mAttrs.flags & WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE) == 0;
    }

    /**
     * Get the window title
     */
    public CharSequence getTitle() {
        return mAttrs.getTitle();
    }

    /**
     * Set the window title
     */
    public void setTitle(CharSequence title) {
        mAttrs.setTitle(title);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("WindowState{");
        sb.append(Integer.toHexString(System.identityHashCode(this)));
        sb.append(" u0 ");
        if (mComponentName != null) {
            sb.append(mComponentName.flattenToShortString());
        } else {
            sb.append("null");
        }
        sb.append(" type=").append(mAttrs.type);
        sb.append(" flags=0x").append(Integer.toHexString(mAttrs.flags));
        sb.append(" visible=").append(mVisible);
        sb.append(" focus=").append(mHasFocus);
        sb.append("}");
        return sb.toString();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        WindowState that = (WindowState) obj;
        return System.identityHashCode(this) == System.identityHashCode(that);
    }

    @Override
    public int hashCode() {
        return System.identityHashCode(this);
    }
}
