/*
 * Copyright (C) 2024 Jarvis OS
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import com.android.systemui.R;

import java.text.DecimalFormat;
import java.util.Random;

/**
 * AI Activity Dashboard for real-time monitoring
 * Displays AI service usage, performance metrics, and system impact
 */
public class AiActivityDashboard {
    private static final String TAG = "AiActivityDashboard";
    private static final boolean DEBUG = true;
    
    private static final int UPDATE_INTERVAL_MS = 2000; // 2 seconds
    
    private final Context mContext;
    private final Handler mHandler;
    private final DecimalFormat mDecimalFormat;
    private final Random mRandom; // For demo data
    
    private View mDashboardView;
    private boolean mIsUpdating = false;
    
    // Metric views
    private TextView mCpuUsageText;
    private TextView mMemoryUsageText;
    private TextView mBatteryImpactText;
    private TextView mResponseTimeText;
    private TextView mRequestCountText;
    private TextView mSuccessRateText;
    private ProgressBar mCpuProgressBar;
    private ProgressBar mMemoryProgressBar;
    private ProgressBar mBatteryProgressBar;
    
    // Performance metrics
    private float mCpuUsage = 0f;
    private float mMemoryUsage = 0f;
    private float mBatteryImpact = 0f;
    private int mResponseTime = 0;
    private int mRequestCount = 0;
    private float mSuccessRate = 0f;
    
    public AiActivityDashboard(Context context) {
        mContext = context;
        mHandler = new Handler(Looper.getMainLooper());
        mDecimalFormat = new DecimalFormat("#.#");
        mRandom = new Random();
        
        if (DEBUG) Log.d(TAG, "AiActivityDashboard created");
    }
    
    public View createDashboardView() {
        if (mDashboardView != null) {
            return mDashboardView;
        }
        
        LayoutInflater inflater = LayoutInflater.from(mContext);
        mDashboardView = inflater.inflate(R.layout.jarvis_ai_dashboard, null);
        
        initializeViews();
        updateMetrics();
        
        if (DEBUG) Log.d(TAG, "Dashboard view created");
        return mDashboardView;
    }
    
    private void initializeViews() {
        // CPU Usage
        mCpuUsageText = mDashboardView.findViewById(R.id.cpu_usage_text);
        mCpuProgressBar = mDashboardView.findViewById(R.id.cpu_progress_bar);
        
        // Memory Usage
        mMemoryUsageText = mDashboardView.findViewById(R.id.memory_usage_text);
        mMemoryProgressBar = mDashboardView.findViewById(R.id.memory_progress_bar);
        
        // Battery Impact
        mBatteryImpactText = mDashboardView.findViewById(R.id.battery_impact_text);
        mBatteryProgressBar = mDashboardView.findViewById(R.id.battery_progress_bar);
        
        // Response Time
        mResponseTimeText = mDashboardView.findViewById(R.id.response_time_text);
        
        // Request Count
        mRequestCountText = mDashboardView.findViewById(R.id.request_count_text);
        
        // Success Rate
        mSuccessRateText = mDashboardView.findViewById(R.id.success_rate_text);
    }
    
    public void startUpdates() {
        if (mIsUpdating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Starting dashboard updates");
        mIsUpdating = true;
        scheduleNextUpdate();
    }
    
    public void stopUpdates() {
        if (!mIsUpdating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Stopping dashboard updates");
        mIsUpdating = false;
        mHandler.removeCallbacks(mUpdateRunnable);
    }
    
    private void scheduleNextUpdate() {
        if (mIsUpdating) {
            mHandler.postDelayed(mUpdateRunnable, UPDATE_INTERVAL_MS);
        }
    }
    
    private final Runnable mUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            updateMetrics();
            scheduleNextUpdate();
        }
    };
    
    private void updateMetrics() {
        // TODO: Get real metrics from AI services
        // For now, generate demo data
        generateDemoMetrics();
        
        // Update UI
        updateCpuUsage();
        updateMemoryUsage();
        updateBatteryImpact();
        updateResponseTime();
        updateRequestCount();
        updateSuccessRate();
        
        if (DEBUG) Log.d(TAG, "Metrics updated - CPU: " + mCpuUsage + "%, Memory: " + mMemoryUsage + "MB");
    }
    
    private void generateDemoMetrics() {
        // Generate realistic demo data
        mCpuUsage = 5f + mRandom.nextFloat() * 15f; // 5-20%
        mMemoryUsage = 50f + mRandom.nextFloat() * 100f; // 50-150MB
        mBatteryImpact = 2f + mRandom.nextFloat() * 8f; // 2-10%
        mResponseTime = 100 + mRandom.nextInt(400); // 100-500ms
        mRequestCount += mRandom.nextInt(5); // Increment by 0-4
        mSuccessRate = 85f + mRandom.nextFloat() * 14f; // 85-99%
    }
    
    private void updateCpuUsage() {
        if (mCpuUsageText != null && mCpuProgressBar != null) {
            String text = mDecimalFormat.format(mCpuUsage) + "%";
            mCpuUsageText.setText(text);
            mCpuProgressBar.setProgress((int) mCpuUsage);
        }
    }
    
    private void updateMemoryUsage() {
        if (mMemoryUsageText != null && mMemoryProgressBar != null) {
            String text = mDecimalFormat.format(mMemoryUsage) + " MB";
            mMemoryUsageText.setText(text);
            
            // Convert to percentage (assuming 200MB max)
            int percentage = (int) Math.min(100, (mMemoryUsage / 200f) * 100);
            mMemoryProgressBar.setProgress(percentage);
        }
    }
    
    private void updateBatteryImpact() {
        if (mBatteryImpactText != null && mBatteryProgressBar != null) {
            String text = mDecimalFormat.format(mBatteryImpact) + "%";
            mBatteryImpactText.setText(text);
            mBatteryProgressBar.setProgress((int) mBatteryImpact);
        }
    }
    
    private void updateResponseTime() {
        if (mResponseTimeText != null) {
            String text = mResponseTime + " ms";
            mResponseTimeText.setText(text);
        }
    }
    
    private void updateRequestCount() {
        if (mRequestCountText != null) {
            String text = String.valueOf(mRequestCount);
            mRequestCountText.setText(text);
        }
    }
    
    private void updateSuccessRate() {
        if (mSuccessRateText != null) {
            String text = mDecimalFormat.format(mSuccessRate) + "%";
            mSuccessRateText.setText(text);
        }
    }
    
    // Methods for external metric updates
    public void updateCpuMetric(float cpuUsage) {
        mCpuUsage = cpuUsage;
        if (mIsUpdating) {
            mHandler.post(this::updateCpuUsage);
        }
    }
    
    public void updateMemoryMetric(float memoryUsageMB) {
        mMemoryUsage = memoryUsageMB;
        if (mIsUpdating) {
            mHandler.post(this::updateMemoryUsage);
        }
    }
    
    public void updateBatteryMetric(float batteryImpact) {
        mBatteryImpact = batteryImpact;
        if (mIsUpdating) {
            mHandler.post(this::updateBatteryImpact);
        }
    }
    
    public void updateResponseTimeMetric(int responseTimeMs) {
        mResponseTime = responseTimeMs;
        if (mIsUpdating) {
            mHandler.post(this::updateResponseTime);
        }
    }
    
    public void incrementRequestCount() {
        mRequestCount++;
        if (mIsUpdating) {
            mHandler.post(this::updateRequestCount);
        }
    }
    
    public void updateSuccessRateMetric(float successRate) {
        mSuccessRate = successRate;
        if (mIsUpdating) {
            mHandler.post(this::updateSuccessRate);
        }
    }
    
    // Getters for current metrics
    public float getCpuUsage() {
        return mCpuUsage;
    }
    
    public float getMemoryUsage() {
        return mMemoryUsage;
    }
    
    public float getBatteryImpact() {
        return mBatteryImpact;
    }
    
    public int getResponseTime() {
        return mResponseTime;
    }
    
    public int getRequestCount() {
        return mRequestCount;
    }
    
    public float getSuccessRate() {
        return mSuccessRate;
    }
    
    public boolean isUpdating() {
        return mIsUpdating;
    }
}
