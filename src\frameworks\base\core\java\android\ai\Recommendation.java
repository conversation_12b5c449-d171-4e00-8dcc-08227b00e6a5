/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents an AI-generated recommendation
 */
public class Recommendation implements Parcelable {
    private final String mRecommendationId;
    private final String mCategory;
    private final String mTitle;
    private final String mDescription;
    private final Bundle mRecommendationData;
    private final float mConfidence;
    private final long mTimestamp;

    public Recommendation(String category, String title, String description, Bundle data, float confidence) {
        mRecommendationId = generateRecommendationId();
        mCategory = category;
        mTitle = title;
        mDescription = description;
        mRecommendationData = data != null ? new Bundle(data) : new Bundle();
        mConfidence = confidence;
        mTimestamp = System.currentTimeMillis();
    }

    private Recommendation(Parcel in) {
        mRecommendationId = in.readString();
        mCategory = in.readString();
        mTitle = in.readString();
        mDescription = in.readString();
        mRecommendationData = in.readBundle(getClass().getClassLoader());
        mConfidence = in.readFloat();
        mTimestamp = in.readLong();
    }

    public String getRecommendationId() {
        return mRecommendationId;
    }

    public String getCategory() {
        return mCategory;
    }

    public String getTitle() {
        return mTitle;
    }

    public String getDescription() {
        return mDescription;
    }

    public Bundle getRecommendationData() {
        return new Bundle(mRecommendationData);
    }

    public float getConfidence() {
        return mConfidence;
    }

    public long getTimestamp() {
        return mTimestamp;
    }

    private String generateRecommendationId() {
        return "rec_" + System.currentTimeMillis() + "_" + hashCode();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mRecommendationId);
        dest.writeString(mCategory);
        dest.writeString(mTitle);
        dest.writeString(mDescription);
        dest.writeBundle(mRecommendationData);
        dest.writeFloat(mConfidence);
        dest.writeLong(mTimestamp);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Recommendation> CREATOR = new Creator<Recommendation>() {
        @Override
        public Recommendation createFromParcel(Parcel in) {
            return new Recommendation(in);
        }

        @Override
        public Recommendation[] newArray(int size) {
            return new Recommendation[size];
        }
    };
}
