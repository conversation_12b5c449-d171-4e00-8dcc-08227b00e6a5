/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.DecelerateInterpolator;
import android.widget.FrameLayout;

import com.android.systemui.R;
import com.android.systemui.statusbar.CommandQueue;

/**
 * Controller for managing Jarvis AI conversation overlays.
 * 
 * Handles:
 * - Overlay window management
 * - Animation and transitions
 * - Touch handling and dismissal
 * - Integration with SystemUI
 */
public class JarvisOverlayController implements JarvisConversationView.ConversationListener {
    private static final String TAG = "JarvisOverlayController";
    private static final boolean DEBUG = true;
    
    private static final int ANIMATION_DURATION = 300;
    private static final int AUTO_DISMISS_DELAY = 30000; // 30 seconds
    
    private final Context mContext;
    private final WindowManager mWindowManager;
    private final Handler mMainHandler;
    
    // Overlay components
    private ViewGroup mOverlayContainer;
    private JarvisConversationView mConversationView;
    private View mBackgroundDimmer;
    private boolean mIsShowing = false;
    private boolean mIsAnimating = false;
    
    // Auto-dismiss handling
    private Runnable mAutoDismissRunnable;
    
    // Callbacks
    private OverlayListener mOverlayListener;
    
    public interface OverlayListener {
        void onOverlayShown();
        void onOverlayHidden();
        void onMessageSent(String message, boolean isVoice);
        void onQuickActionTriggered(String actionId);
    }
    
    public JarvisOverlayController(Context context) {
        mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        mMainHandler = new Handler(Looper.getMainLooper());
        
        createOverlayViews();
        setupAutoDismiss();
        
        if (DEBUG) Log.d(TAG, "JarvisOverlayController initialized");
    }
    
    private void createOverlayViews() {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        
        // Create main overlay container
        mOverlayContainer = (FrameLayout) inflater.inflate(R.layout.jarvis_overlay_container, null);
        
        // Get references to child views
        mBackgroundDimmer = mOverlayContainer.findViewById(R.id.background_dimmer);
        mConversationView = mOverlayContainer.findViewById(R.id.conversation_view);
        
        // Set up conversation listener
        mConversationView.setConversationListener(this);
        
        // Set up touch handling for dismissal
        setupTouchHandling();
        
        if (DEBUG) Log.d(TAG, "Overlay views created");
    }
    
    private void setupTouchHandling() {
        // Dismiss overlay when touching the dimmed background
        mBackgroundDimmer.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                hideOverlay();
                return true;
            }
            return false;
        });
        
        // Prevent touch events from propagating through the conversation view
        mConversationView.setOnTouchListener((v, event) -> true);
    }
    
    private void setupAutoDismiss() {
        mAutoDismissRunnable = () -> {
            if (mIsShowing) {
                hideOverlay();
            }
        };
    }
    
    public void setOverlayListener(OverlayListener listener) {
        mOverlayListener = listener;
    }
    
    public void showOverlay() {
        if (mIsShowing || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Showing Jarvis overlay");
        
        mIsAnimating = true;
        
        // Add overlay to window manager
        WindowManager.LayoutParams params = createOverlayLayoutParams();
        mWindowManager.addView(mOverlayContainer, params);
        
        // Start show animation
        animateShow(() -> {
            mIsShowing = true;
            mIsAnimating = false;
            
            // Schedule auto-dismiss
            scheduleAutoDismiss();
            
            // Notify listener
            if (mOverlayListener != null) {
                mOverlayListener.onOverlayShown();
            }
            
            if (DEBUG) Log.d(TAG, "Jarvis overlay shown");
        });
    }
    
    public void hideOverlay() {
        if (!mIsShowing || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Hiding Jarvis overlay");
        
        mIsAnimating = true;
        
        // Cancel auto-dismiss
        cancelAutoDismiss();
        
        // Start hide animation
        animateHide(() -> {
            // Remove overlay from window manager
            try {
                mWindowManager.removeView(mOverlayContainer);
            } catch (Exception e) {
                Log.w(TAG, "Error removing overlay view", e);
            }
            
            mIsShowing = false;
            mIsAnimating = false;
            
            // Clear conversation
            mConversationView.clearConversation();
            
            // Notify listener
            if (mOverlayListener != null) {
                mOverlayListener.onOverlayHidden();
            }
            
            if (DEBUG) Log.d(TAG, "Jarvis overlay hidden");
        });
    }
    
    public boolean isShowing() {
        return mIsShowing;
    }
    
    public void addAiResponse(String response) {
        if (mIsShowing) {
            mConversationView.addAiResponse(response);
            resetAutoDismiss();
        }
    }
    
    public void setProcessingState(boolean processing) {
        if (mIsShowing) {
            mConversationView.setProcessingState(processing);
        }
    }
    
    private WindowManager.LayoutParams createOverlayLayoutParams() {
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.TYPE_SYSTEM_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
                WindowManager.LayoutParams.FLAG_LAYOUT_INSET_DECOR,
                PixelFormat.TRANSLUCENT);
        
        params.gravity = Gravity.CENTER;
        params.setTitle("JarvisOverlay");
        
        return params;
    }
    
    private void animateShow(Runnable onComplete) {
        // Set initial state
        mOverlayContainer.setAlpha(0f);
        mConversationView.setTranslationY(200f);
        mConversationView.setScaleX(0.8f);
        mConversationView.setScaleY(0.8f);
        
        // Animate background dimmer
        ObjectAnimator backgroundAnimator = ObjectAnimator.ofFloat(mOverlayContainer, "alpha", 0f, 1f);
        backgroundAnimator.setDuration(ANIMATION_DURATION);
        backgroundAnimator.setInterpolator(new DecelerateInterpolator());
        
        // Animate conversation view
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(mConversationView, "translationY", 200f, 0f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(mConversationView, "scaleX", 0.8f, 1f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(mConversationView, "scaleY", 0.8f, 1f);
        
        translateAnimator.setDuration(ANIMATION_DURATION);
        scaleXAnimator.setDuration(ANIMATION_DURATION);
        scaleYAnimator.setDuration(ANIMATION_DURATION);
        
        translateAnimator.setInterpolator(new DecelerateInterpolator());
        scaleXAnimator.setInterpolator(new DecelerateInterpolator());
        scaleYAnimator.setInterpolator(new DecelerateInterpolator());
        
        // Start animations
        backgroundAnimator.start();
        translateAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();
        
        // Complete callback
        backgroundAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (onComplete != null) {
                    onComplete.run();
                }
            }
        });
    }
    
    private void animateHide(Runnable onComplete) {
        // Animate background dimmer
        ObjectAnimator backgroundAnimator = ObjectAnimator.ofFloat(mOverlayContainer, "alpha", 1f, 0f);
        backgroundAnimator.setDuration(ANIMATION_DURATION);
        backgroundAnimator.setInterpolator(new DecelerateInterpolator());
        
        // Animate conversation view
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(mConversationView, "translationY", 0f, 200f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(mConversationView, "scaleX", 1f, 0.8f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(mConversationView, "scaleY", 1f, 0.8f);
        
        translateAnimator.setDuration(ANIMATION_DURATION);
        scaleXAnimator.setDuration(ANIMATION_DURATION);
        scaleYAnimator.setDuration(ANIMATION_DURATION);
        
        translateAnimator.setInterpolator(new DecelerateInterpolator());
        scaleXAnimator.setInterpolator(new DecelerateInterpolator());
        scaleYAnimator.setInterpolator(new DecelerateInterpolator());
        
        // Start animations
        backgroundAnimator.start();
        translateAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();
        
        // Complete callback
        backgroundAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (onComplete != null) {
                    onComplete.run();
                }
            }
        });
    }
    
    private void scheduleAutoDismiss() {
        cancelAutoDismiss();
        mMainHandler.postDelayed(mAutoDismissRunnable, AUTO_DISMISS_DELAY);
    }
    
    private void cancelAutoDismiss() {
        mMainHandler.removeCallbacks(mAutoDismissRunnable);
    }
    
    private void resetAutoDismiss() {
        if (mIsShowing) {
            scheduleAutoDismiss();
        }
    }
    
    // JarvisConversationView.ConversationListener implementation
    @Override
    public void onTextMessage(String message) {
        resetAutoDismiss();
        
        if (mOverlayListener != null) {
            mOverlayListener.onMessageSent(message, false);
        }
        
        if (DEBUG) Log.d(TAG, "Text message sent: " + message);
    }
    
    @Override
    public void onVoiceMessage(String message) {
        resetAutoDismiss();
        
        if (mOverlayListener != null) {
            mOverlayListener.onMessageSent(message, true);
        }
        
        if (DEBUG) Log.d(TAG, "Voice message sent: " + message);
    }
    
    @Override
    public void onQuickAction(String actionId) {
        resetAutoDismiss();
        
        if (mOverlayListener != null) {
            mOverlayListener.onQuickActionTriggered(actionId);
        }
        
        if (DEBUG) Log.d(TAG, "Quick action triggered: " + actionId);
    }
    
    @Override
    public void onConversationStateChanged(boolean isActive) {
        if (isActive) {
            resetAutoDismiss();
        }
    }
    
    public void destroy() {
        if (mIsShowing) {
            hideOverlay();
        }
        
        cancelAutoDismiss();
        
        if (DEBUG) Log.d(TAG, "JarvisOverlayController destroyed");
    }
}
