/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.view;

/**
 * Mock implementation of Display for Jarvis OS compilation.
 * This is a stub implementation for development and testing.
 */
public class Display {
    /**
     * The default Display id, which is the id of the built-in primary display
     * assuming there is one.
     */
    public static final int DEFAULT_DISPLAY = 0;

    /**
     * Display flag: Indicates that the display supports compositing content
     * that is stored in protected graphics buffers.
     */
    public static final int FLAG_SUPPORTS_PROTECTED_BUFFERS = 1 << 0;

    /**
     * Display flag: Indicates that the display has a secure video output and
     * supports compositing secure surfaces.
     */
    public static final int FLAG_SECURE = 1 << 1;

    /**
     * Display flag: Indicates that the display is private.  Only the application that
     * owns the display and apps that are already on the display can create windows on it.
     */
    public static final int FLAG_PRIVATE = 1 << 2;

    /**
     * Display flag: Indicates that the display is a presentation display.
     */
    public static final int FLAG_PRESENTATION = 1 << 3;

    /**
     * Display flag: Indicates that the display has a round shape.
     */
    public static final int FLAG_ROUND = 1 << 4;

    /**
     * Display flag: Indicates that the display can show its content when non-secure keyguard is shown.
     */
    public static final int FLAG_CAN_SHOW_WITH_INSECURE_KEYGUARD = 1 << 5;

    /**
     * Display flag: Indicates that the display should show system decorations.
     */
    public static final int FLAG_SHOULD_SHOW_SYSTEM_DECORATIONS = 1 << 6;

    /**
     * Display flag: Indicates that the display supports color spaces other than the
     * default SRGB color space.
     */
    public static final int FLAG_WIDE_COLOR_GAMUT = 1 << 7;

    /**
     * Display flag: Indicates that the display has HDR capabilities.
     */
    public static final int FLAG_HDR = 1 << 8;

    /**
     * Display state: The display is off.
     */
    public static final int STATE_OFF = 1;

    /**
     * Display state: The display is on.
     */
    public static final int STATE_ON = 2;

    /**
     * Display state: The display is dozing.
     */
    public static final int STATE_DOZE = 3;

    /**
     * Display state: The display is dozing in a suspended state.
     */
    public static final int STATE_DOZE_SUSPEND = 4;

    /**
     * Display state: The display is on and optimized for VR mode.
     */
    public static final int STATE_VR = 5;

    /**
     * Display state: The display is in a suspended full power state.
     */
    public static final int STATE_ON_SUSPEND = 6;

    /**
     * Display state: The display is unknown.
     */
    public static final int STATE_UNKNOWN = 0;

    private final int mDisplayId;
    private final int mFlags;

    public Display(int displayId, int flags) {
        mDisplayId = displayId;
        mFlags = flags;
    }

    /**
     * Gets the display id.
     */
    public int getDisplayId() {
        return mDisplayId;
    }

    /**
     * Returns true if this display is still valid, false if the display has been removed.
     */
    public boolean isValid() {
        return true; // Mock implementation always returns true
    }

    /**
     * Gets the display flags.
     */
    public int getFlags() {
        return mFlags;
    }

    /**
     * Gets the display name.
     */
    public String getName() {
        return "Mock Display " + mDisplayId;
    }

    /**
     * Gets the display state.
     */
    public int getState() {
        return STATE_ON; // Mock implementation always returns ON
    }

    /**
     * Returns true if the specified UID has access to this display.
     */
    public boolean hasAccess(int uid) {
        return true; // Mock implementation always returns true
    }

    /**
     * Returns true if this is a public display.
     */
    public boolean isPublicDisplay() {
        return (mFlags & FLAG_PRIVATE) == 0;
    }

    @Override
    public String toString() {
        return "Display id " + mDisplayId + ": name=" + getName() + ", flags=0x" + Integer.toHexString(mFlags);
    }
}
