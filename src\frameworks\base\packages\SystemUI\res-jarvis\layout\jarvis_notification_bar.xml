<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/jarvis_notification_background"
    android:elevation="@dimen/jarvis_elevation_md">

    <!-- Smart Notification Header -->
    <LinearLayout
        android:id="@+id/notification_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="@dimen/jarvis_notification_padding"
        android:background="@drawable/jarvis_notification_header_background">

        <!-- AI Smart Summary Icon -->
        <ImageView
            android:id="@+id/smart_summary_icon"
            android:layout_width="@dimen/jarvis_notification_icon_size"
            android:layout_height="@dimen/jarvis_notification_icon_size"
            android:src="@drawable/ic_ai_summary"
            android:layout_marginEnd="@dimen/jarvis_spacing_sm"
            android:contentDescription="@string/smart_summary" />

        <!-- Header Text -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/notification_title"
                style="@style/JarvisText.Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/smart_notifications"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/notification_summary"
                style="@style/JarvisText.Caption"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/notification_summary_placeholder"
                android:visibility="gone" />

        </LinearLayout>

        <!-- AI Processing Indicator -->
        <ProgressBar
            android:id="@+id/ai_processing_indicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_marginEnd="@dimen/jarvis_spacing_sm"
            android:indeterminateTint="@color/jarvis_ai_thinking"
            android:visibility="gone" />

        <!-- Clear All Button -->
        <ImageButton
            android:id="@+id/clear_all_button"
            android:layout_width="@dimen/jarvis_notification_icon_size"
            android:layout_height="@dimen/jarvis_notification_icon_size"
            android:src="@drawable/ic_clear_all"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/clear_all_notifications" />

    </LinearLayout>

    <!-- Smart Priority Sections -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- High Priority Section -->
            <LinearLayout
                android:id="@+id/high_priority_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="@dimen/jarvis_spacing_md"
                    android:background="@color/jarvis_notification_high_priority">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_sm"
                        android:layout_height="@dimen/jarvis_icon_size_sm"
                        android:src="@drawable/ic_priority_high"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/high_priority" />

                    <TextView
                        style="@style/JarvisText.Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/high_priority_notifications"
                        android:textStyle="bold"
                        android:textColor="@color/jarvis_text_inverse" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/high_priority_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- AI Grouped Notifications -->
            <LinearLayout
                android:id="@+id/ai_grouped_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="@dimen/jarvis_spacing_md"
                    android:background="@color/jarvis_notification_surface">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_sm"
                        android:layout_height="@dimen/jarvis_icon_size_sm"
                        android:src="@drawable/ic_ai_grouped"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/ai_grouped" />

                    <TextView
                        style="@style/JarvisText.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/ai_grouped_notifications"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/grouped_count"
                        style="@style/JarvisText.Caption"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="3 groups"
                        android:background="@drawable/jarvis_count_badge"
                        android:padding="@dimen/jarvis_spacing_xs" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/ai_grouped_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- Regular Notifications -->
            <LinearLayout
                android:id="@+id/regular_notifications_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="@dimen/jarvis_spacing_md"
                    android:background="@color/jarvis_notification_surface">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_sm"
                        android:layout_height="@dimen/jarvis_icon_size_sm"
                        android:src="@drawable/ic_notifications_regular"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/regular_notifications" />

                    <TextView
                        style="@style/JarvisText.Body2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/other_notifications"
                        android:textStyle="bold" />

                    <ImageButton
                        android:id="@+id/expand_regular_button"
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_expand_more"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/expand_regular" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/regular_notifications_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- AI Suggestions Section -->
            <LinearLayout
                android:id="@+id/ai_suggestions_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:padding="@dimen/jarvis_spacing_md"
                    android:background="@drawable/jarvis_gradient_background">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_sm"
                        android:layout_height="@dimen/jarvis_icon_size_sm"
                        android:src="@drawable/ic_ai_suggestions"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/ai_suggestions" />

                    <TextView
                        style="@style/JarvisText.Body2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/smart_suggestions"
                        android:textStyle="bold"
                        android:textColor="@color/jarvis_text_inverse" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/ai_suggestions_recycler"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/empty_state"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="@dimen/jarvis_spacing_xxl"
                android:visibility="gone">

                <ImageView
                    android:layout_width="@dimen/jarvis_avatar_size_lg"
                    android:layout_height="@dimen/jarvis_avatar_size_lg"
                    android:src="@drawable/ic_no_notifications"
                    android:alpha="0.6"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md"
                    android:contentDescription="@string/no_notifications" />

                <TextView
                    style="@style/JarvisText.Body1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_notifications_message"
                    android:gravity="center"
                    android:textColor="@color/jarvis_text_secondary" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Quick Actions Footer -->
    <LinearLayout
        android:id="@+id/quick_actions_footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="@dimen/jarvis_spacing_md"
        android:background="@color/jarvis_notification_surface"
        android:visibility="gone">

        <Button
            android:id="@+id/manage_notifications_button"
            style="@style/JarvisButton.Text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/manage_notifications"
            android:layout_marginEnd="@dimen/jarvis_spacing_md" />

        <Button
            android:id="@+id/ai_settings_button"
            style="@style/JarvisButton.Text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/ai_settings" />

    </LinearLayout>

</LinearLayout>
