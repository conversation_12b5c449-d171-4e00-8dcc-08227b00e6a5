/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#define LOG_TAG "AiInferenceManagerJNI"

#include <jni.h>
#include <nativehelper/JNIHelp.h>
#include <android_runtime/AndroidRuntime.h>
#include <utils/Log.h>
#include <utils/misc.h>

#include "ai_inference.h"
#include "ai_security.h"
#include "ai_context.h"

namespace android {

// Global references to Java classes and methods
static struct {
    jclass clazz;
    jmethodID constructor;
    jfieldID nativePtr;
} gAiModelInfo;

static struct {
    jclass clazz;
    jmethodID constructor;
    jfieldID inferenceTimeMs;
    jfieldID memoryUsedBytes;
    jfieldID cpuUsagePercent;
} gPerformanceMetrics;

// Helper functions
static jlong getNativePtr(JNIEnv* env, jobject obj) {
    return env->GetLongField(obj, gAiModelInfo.nativePtr);
}

static void setNativePtr(JNIEnv* env, jobject obj, jlong ptr) {
    env->SetLongField(obj, gAiModelInfo.nativePtr, ptr);
}

// JNI method implementations
static jboolean nativeInit(JNIEnv* env, jclass clazz) {
    ALOGD("Initializing AI Inference native library");
    
    ai_inference_config_t config = {};
    config.hardware = AI_HARDWARE_AUTO;
    config.use_quantization = true;
    config.max_batch_size = 1;
    config.num_threads = 4;
    config.confidence_threshold = 0.5f;
    config.enable_profiling = true;
    
    ai_inference_result_t result = ai_inference_init(&config);
    if (result != AI_INFERENCE_SUCCESS) {
        ALOGE("Failed to initialize AI inference library: %s", ai_get_error_message(result));
        return JNI_FALSE;
    }
    
    // Also initialize security and context libraries
    ai_security_result_t sec_result = ai_security_init(nullptr);
    if (sec_result != AI_SECURITY_SUCCESS) {
        ALOGE("Failed to initialize AI security library: %s", ai_security_get_error_message(sec_result));
        return JNI_FALSE;
    }
    
    ai_context_result_t ctx_result = ai_context_init();
    if (ctx_result != AI_CONTEXT_SUCCESS) {
        ALOGE("Failed to initialize AI context library: %s", ai_context_get_error_message(ctx_result));
        return JNI_FALSE;
    }
    
    return JNI_TRUE;
}

static void nativeCleanup(JNIEnv* env, jclass clazz) {
    ALOGD("Cleaning up AI Inference native library");
    ai_inference_cleanup();
    ai_security_cleanup();
    ai_context_cleanup();
}

static jlong nativeLoadModel(JNIEnv* env, jclass clazz, jstring modelPath, jint modelType) {
    if (modelPath == nullptr) {
        jniThrowNullPointerException(env, "Model path cannot be null");
        return 0;
    }
    
    const char* path = env->GetStringUTFChars(modelPath, nullptr);
    if (path == nullptr) {
        jniThrowException(env, "java/lang/OutOfMemoryError", "Failed to get model path");
        return 0;
    }
    
    ai_model_t* model = nullptr;
    ai_inference_result_t result = ai_model_load(path, static_cast<ai_model_type_t>(modelType), &model);
    
    env->ReleaseStringUTFChars(modelPath, path);
    
    if (result != AI_INFERENCE_SUCCESS) {
        jniThrowException(env, "java/lang/RuntimeException", ai_get_error_message(result));
        return 0;
    }
    
    ALOGD("Model loaded successfully: %p", model);
    return reinterpret_cast<jlong>(model);
}

static jlong nativeLoadModelFromBuffer(JNIEnv* env, jclass clazz, jbyteArray modelData, jint modelType) {
    if (modelData == nullptr) {
        jniThrowNullPointerException(env, "Model data cannot be null");
        return 0;
    }
    
    jsize dataSize = env->GetArrayLength(modelData);
    if (dataSize <= 0) {
        jniThrowException(env, "java/lang/IllegalArgumentException", "Model data is empty");
        return 0;
    }
    
    jbyte* data = env->GetByteArrayElements(modelData, nullptr);
    if (data == nullptr) {
        jniThrowException(env, "java/lang/OutOfMemoryError", "Failed to get model data");
        return 0;
    }
    
    ai_model_t* model = nullptr;
    ai_inference_result_t result = ai_model_load_from_buffer(data, dataSize, 
                                                           static_cast<ai_model_type_t>(modelType), &model);
    
    env->ReleaseByteArrayElements(modelData, data, JNI_ABORT);
    
    if (result != AI_INFERENCE_SUCCESS) {
        jniThrowException(env, "java/lang/RuntimeException", ai_get_error_message(result));
        return 0;
    }
    
    ALOGD("Model loaded from buffer successfully: %p", model);
    return reinterpret_cast<jlong>(model);
}

static void nativeUnloadModel(JNIEnv* env, jclass clazz, jlong modelPtr) {
    if (modelPtr == 0) {
        return;
    }
    
    ai_model_t* model = reinterpret_cast<ai_model_t*>(modelPtr);
    ai_model_unload(model);
    ALOGD("Model unloaded: %p", model);
}

static jobject nativeGetModelInfo(JNIEnv* env, jclass clazz, jlong modelPtr) {
    if (modelPtr == 0) {
        jniThrowNullPointerException(env, "Model pointer is null");
        return nullptr;
    }
    
    ai_model_t* model = reinterpret_cast<ai_model_t*>(modelPtr);
    ai_model_info_t info;
    
    ai_inference_result_t result = ai_model_get_info(model, &info);
    if (result != AI_INFERENCE_SUCCESS) {
        jniThrowException(env, "java/lang/RuntimeException", ai_get_error_message(result));
        return nullptr;
    }
    
    // Create Java ModelInfo object
    jobject modelInfo = env->NewObject(gAiModelInfo.clazz, gAiModelInfo.constructor);
    if (modelInfo == nullptr) {
        jniThrowException(env, "java/lang/OutOfMemoryError", "Failed to create ModelInfo object");
        return nullptr;
    }
    
    // Set fields (simplified - in production would set all fields)
    jstring name = env->NewStringUTF(info.name);
    jstring version = env->NewStringUTF(info.version);
    
    // Note: In a real implementation, we would set all the fields of the ModelInfo object
    // For now, just return the object
    
    return modelInfo;
}

static jlong nativeCreateSession(JNIEnv* env, jclass clazz, jlong modelPtr) {
    if (modelPtr == 0) {
        jniThrowNullPointerException(env, "Model pointer is null");
        return 0;
    }
    
    ai_model_t* model = reinterpret_cast<ai_model_t*>(modelPtr);
    ai_inference_session_t* session = nullptr;
    
    ai_inference_result_t result = ai_session_create(model, nullptr, &session);
    if (result != AI_INFERENCE_SUCCESS) {
        jniThrowException(env, "java/lang/RuntimeException", ai_get_error_message(result));
        return 0;
    }
    
    ALOGD("Inference session created: %p", session);
    return reinterpret_cast<jlong>(session);
}

static void nativeDestroySession(JNIEnv* env, jclass clazz, jlong sessionPtr) {
    if (sessionPtr == 0) {
        return;
    }
    
    ai_inference_session_t* session = reinterpret_cast<ai_inference_session_t*>(sessionPtr);
    ai_session_destroy(session);
    ALOGD("Inference session destroyed: %p", session);
}

static jboolean nativeRunInference(JNIEnv* env, jclass clazz, jlong sessionPtr) {
    if (sessionPtr == 0) {
        jniThrowNullPointerException(env, "Session pointer is null");
        return JNI_FALSE;
    }
    
    ai_inference_session_t* session = reinterpret_cast<ai_inference_session_t*>(sessionPtr);
    
    ai_inference_result_t result = ai_session_run(session);
    if (result != AI_INFERENCE_SUCCESS) {
        ALOGE("Inference failed: %s", ai_get_error_message(result));
        return JNI_FALSE;
    }
    
    return JNI_TRUE;
}

static jobject nativeGetPerformanceMetrics(JNIEnv* env, jclass clazz, jlong sessionPtr) {
    if (sessionPtr == 0) {
        jniThrowNullPointerException(env, "Session pointer is null");
        return nullptr;
    }
    
    ai_inference_session_t* session = reinterpret_cast<ai_inference_session_t*>(sessionPtr);
    ai_performance_metrics_t metrics;
    
    ai_inference_result_t result = ai_session_get_metrics(session, &metrics);
    if (result != AI_INFERENCE_SUCCESS) {
        jniThrowException(env, "java/lang/RuntimeException", ai_get_error_message(result));
        return nullptr;
    }
    
    // Create Java PerformanceMetrics object
    jobject perfMetrics = env->NewObject(gPerformanceMetrics.clazz, gPerformanceMetrics.constructor);
    if (perfMetrics == nullptr) {
        jniThrowException(env, "java/lang/OutOfMemoryError", "Failed to create PerformanceMetrics object");
        return nullptr;
    }
    
    // Set fields
    env->SetFloatField(perfMetrics, gPerformanceMetrics.inferenceTimeMs, metrics.inference_time_ms);
    env->SetLongField(perfMetrics, gPerformanceMetrics.memoryUsedBytes, metrics.memory_used_bytes);
    env->SetFloatField(perfMetrics, gPerformanceMetrics.cpuUsagePercent, metrics.cpu_usage_percent);
    
    return perfMetrics;
}

static jboolean nativeIsHardwareAvailable(JNIEnv* env, jclass clazz, jint hardwareType) {
    return ai_is_hardware_available(static_cast<ai_hardware_type_t>(hardwareType)) ? JNI_TRUE : JNI_FALSE;
}

static void nativeSetDebugLogging(JNIEnv* env, jclass clazz, jboolean enable) {
    ai_set_debug_logging(enable == JNI_TRUE);
    ai_security_set_debug_logging(enable == JNI_TRUE);
    ai_context_set_debug_logging(enable == JNI_TRUE);
}

// JNI method table
static const JNINativeMethod gMethods[] = {
    {"nativeInit", "()Z", (void*)nativeInit},
    {"nativeCleanup", "()V", (void*)nativeCleanup},
    {"nativeLoadModel", "(Ljava/lang/String;I)J", (void*)nativeLoadModel},
    {"nativeLoadModelFromBuffer", "([BI)J", (void*)nativeLoadModelFromBuffer},
    {"nativeUnloadModel", "(J)V", (void*)nativeUnloadModel},
    {"nativeGetModelInfo", "(J)Lcom/android/server/ai/ModelInfo;", (void*)nativeGetModelInfo},
    {"nativeCreateSession", "(J)J", (void*)nativeCreateSession},
    {"nativeDestroySession", "(J)V", (void*)nativeDestroySession},
    {"nativeRunInference", "(J)Z", (void*)nativeRunInference},
    {"nativeGetPerformanceMetrics", "(J)Lcom/android/server/ai/PerformanceMetrics;", (void*)nativeGetPerformanceMetrics},
    {"nativeIsHardwareAvailable", "(I)Z", (void*)nativeIsHardwareAvailable},
    {"nativeSetDebugLogging", "(Z)V", (void*)nativeSetDebugLogging},
};

// Registration function
int register_android_server_ai_AiInferenceManager(JNIEnv* env) {
    // Find and cache Java classes and methods
    jclass clazz = FindClassOrDie(env, "com/android/server/ai/ModelInfo");
    gAiModelInfo.clazz = MakeGlobalRefOrDie(env, clazz);
    gAiModelInfo.constructor = GetMethodIDOrDie(env, clazz, "<init>", "()V");
    gAiModelInfo.nativePtr = GetFieldIDOrDie(env, clazz, "mNativePtr", "J");
    
    clazz = FindClassOrDie(env, "com/android/server/ai/PerformanceMetrics");
    gPerformanceMetrics.clazz = MakeGlobalRefOrDie(env, clazz);
    gPerformanceMetrics.constructor = GetMethodIDOrDie(env, clazz, "<init>", "()V");
    gPerformanceMetrics.inferenceTimeMs = GetFieldIDOrDie(env, clazz, "inferenceTimeMs", "F");
    gPerformanceMetrics.memoryUsedBytes = GetFieldIDOrDie(env, clazz, "memoryUsedBytes", "J");
    gPerformanceMetrics.cpuUsagePercent = GetFieldIDOrDie(env, clazz, "cpuUsagePercent", "F");
    
    // Register native methods
    return RegisterMethodsOrDie(env, "com/android/server/ai/AiInferenceManager", gMethods, NELEM(gMethods));
}

} // namespace android
