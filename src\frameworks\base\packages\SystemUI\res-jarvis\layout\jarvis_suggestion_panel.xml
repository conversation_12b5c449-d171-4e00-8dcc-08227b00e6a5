<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/jarvis_suggestion_panel_background"
    android:padding="16dp"
    android:elevation="4dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingBottom="12dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_jarvis_suggestions"
            android:contentDescription="@string/jarvis_suggestions_icon" />

        <TextView
            android:id="@+id/panel_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:text="@string/jarvis_suggestions"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/jarvis_suggestion_title" />

        <ImageButton
            android:id="@+id/refresh_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_refresh"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:layout_marginEnd="8dp"
            android:contentDescription="@string/refresh_suggestions" />

        <ImageButton
            android:id="@+id/dismiss_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/dismiss_suggestions" />

    </LinearLayout>

    <!-- Suggestions List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/suggestions_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="400dp"
        android:clipToPadding="false"
        android:scrollbars="vertical" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/empty_state_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="32dp"
        android:visibility="gone">

        <ImageView
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_no_suggestions"
            android:alpha="0.6"
            android:contentDescription="@string/no_suggestions" />

        <TextView
            android:id="@+id/empty_state_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/jarvis_no_suggestions"
            android:textSize="14sp"
            android:textColor="@color/jarvis_empty_state_text"
            android:gravity="center" />

    </LinearLayout>

</LinearLayout>
