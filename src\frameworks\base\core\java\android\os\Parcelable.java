/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.os;

/**
 * Interface for classes whose instances can be written to and restored from a Parcel.
 * This is a stub implementation for the Jarvis OS project.
 */
public interface Parcelable {
    
    /**
     * Flag for use with writeToParcel: the object being written is a return value.
     */
    public static final int PARCELABLE_WRITE_RETURN_VALUE = 0x0001;
    
    /**
     * Bit masks for use with describeContents(): each bit represents a kind of object
     * considered to have potential special significance when marshalled.
     */
    public static final int CONTENTS_FILE_DESCRIPTOR = 0x0001;
    
    /**
     * Describe the kinds of special objects contained in this Parcelable instance's
     * marshaled representation.
     */
    public int describeContents();
    
    /**
     * Flatten this object in to a Parcel.
     */
    public void writeToParcel(Parcel dest, int flags);
    
    /**
     * Interface that must be implemented and provided as a public CREATOR field
     * that generates instances of your Parcelable class from a Parcel.
     */
    public interface Creator<T> {
        /**
         * Create a new instance of the Parcelable class, instantiating it
         * from the given Parcel whose data had previously been written by
         * Parcelable.writeToParcel().
         */
        public T createFromParcel(Parcel source);
        
        /**
         * Create a new array of the Parcelable class.
         */
        public T[] newArray(int size);
    }
    
    /**
     * Specialization of Creator that allows you to receive the ClassLoader
     * the object is being created in.
     */
    public interface ClassLoaderCreator<T> extends Creator<T> {
        /**
         * Create a new instance of the Parcelable class, instantiating it
         * from the given Parcel whose data had previously been written by
         * Parcelable.writeToParcel() and using the given ClassLoader.
         */
        public T createFromParcel(Parcel source, ClassLoader loader);
    }
}
