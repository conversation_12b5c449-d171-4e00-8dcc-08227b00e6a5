/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution;

import android.ai.ActionRequest;
import android.ai.ActionResult;
import android.ai.ExecutionResult;
import android.ai.ExecutionStatus;
import android.ai.IActionProvider;
import android.ai.TaskPlan;
import android.ai.TaskStep;
import android.content.Context;
import android.os.Bundle;
import android.os.RemoteException;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Task execution engine that orchestrates the execution of task plans
 * by coordinating action providers and managing execution flow.
 */
public class TaskExecutor {
    private static final String TAG = "TaskExecutor";
    private static final boolean DEBUG = true;
    
    // Execution configuration
    private static final int MAX_CONCURRENT_STEPS = 3;
    private static final int DEFAULT_STEP_TIMEOUT_MS = 30000;
    private static final int MAX_RETRY_ATTEMPTS = 2;
    private static final long RETRY_DELAY_MS = 1000;
    
    private final Context mContext;
    private final ActionRegistry mActionRegistry;
    private final AiSecurityManager mSecurityManager;
    private final ExecutorService mExecutorService;
    
    // Execution tracking
    private final Map<String, ExecutionContext> mActiveExecutions = new HashMap<>();
    
    public TaskExecutor(Context context, ActionRegistry actionRegistry, AiSecurityManager securityManager) {
        mContext = context;
        mActionRegistry = actionRegistry;
        mSecurityManager = securityManager;
        mExecutorService = Executors.newFixedThreadPool(MAX_CONCURRENT_STEPS);
        
        if (DEBUG) Slog.d(TAG, "TaskExecutor initialized");
    }
    
    /**
     * Execute a task plan
     */
    public ExecutionResult executeTask(TaskPlan plan, String packageName) {
        if (DEBUG) Slog.d(TAG, "Executing task: " + plan.taskId + " for package: " + packageName);
        
        try {
            // Validate execution permissions
            if (!mSecurityManager.hasExecutionPermission(packageName, plan)) {
                return createErrorResult(plan.taskId, "Execution permission denied");
            }
            
            // Create execution context
            ExecutionContext context = new ExecutionContext(plan, packageName);
            mActiveExecutions.put(plan.taskId, context);
            
            // Execute steps in order, respecting dependencies
            ExecutionResult result = executeStepsSequentially(context);
            
            // Clean up
            mActiveExecutions.remove(plan.taskId);
            
            if (DEBUG) Slog.d(TAG, "Task execution completed: " + plan.taskId + 
                    ", success: " + result.success);
            
            return result;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error executing task: " + plan.taskId, e);
            mActiveExecutions.remove(plan.taskId);
            return createErrorResult(plan.taskId, "Execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Execute steps sequentially, respecting dependencies
     */
    private ExecutionResult executeStepsSequentially(ExecutionContext context) {
        TaskPlan plan = context.plan;
        List<TaskStep> remainingSteps = new ArrayList<>(plan.steps);
        Map<String, ActionResult> stepResults = new HashMap<>();
        
        while (!remainingSteps.isEmpty()) {
            // Find steps that can be executed (dependencies satisfied)
            List<TaskStep> executableSteps = findExecutableSteps(remainingSteps, stepResults);
            
            if (executableSteps.isEmpty()) {
                // No executable steps found - circular dependency or missing dependency
                return createErrorResult(plan.taskId, "Cannot resolve step dependencies");
            }
            
            // Execute steps (up to MAX_CONCURRENT_STEPS at once)
            List<CompletableFuture<StepExecutionResult>> futures = new ArrayList<>();
            int stepsToExecute = Math.min(executableSteps.size(), MAX_CONCURRENT_STEPS);
            
            for (int i = 0; i < stepsToExecute; i++) {
                TaskStep step = executableSteps.get(i);
                CompletableFuture<StepExecutionResult> future = executeStepAsync(step, context, stepResults);
                futures.add(future);
                remainingSteps.remove(step);
            }
            
            // Wait for all steps to complete
            for (CompletableFuture<StepExecutionResult> future : futures) {
                try {
                    StepExecutionResult stepResult = future.get();
                    stepResults.put(stepResult.stepId, stepResult.actionResult);
                    
                    if (!stepResult.actionResult.success) {
                        // Step failed - check if we should continue or abort
                        if (stepResult.step.required) {
                            return createErrorResult(plan.taskId, 
                                    "Required step failed: " + stepResult.stepId + 
                                    " - " + stepResult.actionResult.errorMessage);
                        } else {
                            Slog.w(TAG, "Optional step failed: " + stepResult.stepId);
                        }
                    }
                    
                } catch (Exception e) {
                    Slog.e(TAG, "Error waiting for step execution", e);
                    return createErrorResult(plan.taskId, "Step execution error: " + e.getMessage());
                }
            }
        }
        
        // All steps completed successfully
        ExecutionResult result = new ExecutionResult();
        result.taskId = plan.taskId;
        result.success = true;
        result.results = createResultsBundle(stepResults);
        result.executionTime = System.currentTimeMillis() - context.startTime;
        
        return result;
    }
    
    /**
     * Find steps that can be executed (dependencies satisfied)
     */
    private List<TaskStep> findExecutableSteps(List<TaskStep> remainingSteps, Map<String, ActionResult> completedSteps) {
        List<TaskStep> executable = new ArrayList<>();
        
        for (TaskStep step : remainingSteps) {
            boolean canExecute = true;
            
            if (step.dependencies != null) {
                for (String depId : step.dependencies) {
                    if (!completedSteps.containsKey(depId)) {
                        canExecute = false;
                        break;
                    }
                    
                    // Check if dependency succeeded (if required)
                    ActionResult depResult = completedSteps.get(depId);
                    if (!depResult.success && isStepRequired(depId, remainingSteps)) {
                        canExecute = false;
                        break;
                    }
                }
            }
            
            if (canExecute) {
                executable.add(step);
            }
        }
        
        return executable;
    }
    
    /**
     * Execute a single step asynchronously
     */
    private CompletableFuture<StepExecutionResult> executeStepAsync(TaskStep step, ExecutionContext context, 
                                                                   Map<String, ActionResult> previousResults) {
        return CompletableFuture.supplyAsync(() -> {
            if (DEBUG) Slog.d(TAG, "Executing step: " + step.stepId + " - " + step.action);
            
            try {
                // Get action provider
                IActionProvider provider = mActionRegistry.getActionProvider(step.action);
                if (provider == null) {
                    ActionResult result = new ActionResult();
                    result.success = false;
                    result.errorMessage = "No provider found for action: " + step.action;
                    return new StepExecutionResult(step, result);
                }
                
                // Create action request
                ActionRequest request = new ActionRequest();
                request.action = step.action;
                request.parameters = step.parameters != null ? step.parameters : new Bundle();
                request.timeout = step.timeout > 0 ? step.timeout : DEFAULT_STEP_TIMEOUT_MS;
                request.packageName = context.packageName;
                request.taskId = context.plan.taskId;
                request.stepId = step.stepId;
                
                // Add context from previous steps
                if (!previousResults.isEmpty()) {
                    Bundle contextBundle = new Bundle();
                    for (Map.Entry<String, ActionResult> entry : previousResults.entrySet()) {
                        if (entry.getValue().results != null) {
                            contextBundle.putBundle(entry.getKey(), entry.getValue().results);
                        }
                    }
                    request.parameters.putBundle("previousResults", contextBundle);
                }
                
                // Execute with retry logic
                ActionResult result = executeActionWithRetry(provider, request);
                
                if (DEBUG) Slog.d(TAG, "Step completed: " + step.stepId + ", success: " + result.success);
                
                return new StepExecutionResult(step, result);
                
            } catch (Exception e) {
                Slog.e(TAG, "Error executing step: " + step.stepId, e);
                ActionResult result = new ActionResult();
                result.success = false;
                result.errorMessage = "Step execution error: " + e.getMessage();
                return new StepExecutionResult(step, result);
            }
        }, mExecutorService);
    }
    
    /**
     * Execute action with retry logic
     */
    private ActionResult executeActionWithRetry(IActionProvider provider, ActionRequest request) {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                ActionResult result = provider.executeAction(request);
                
                if (result.success || attempt == MAX_RETRY_ATTEMPTS) {
                    return result;
                }
                
                // Wait before retry
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    Thread.sleep(RETRY_DELAY_MS * attempt);
                }
                
            } catch (RemoteException e) {
                lastException = e;
                Slog.w(TAG, "Action execution attempt " + attempt + " failed", e);
                
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
        
        // All retries failed
        ActionResult result = new ActionResult();
        result.success = false;
        result.errorMessage = "All retry attempts failed";
        if (lastException != null) {
            result.errorMessage += ": " + lastException.getMessage();
        }
        
        return result;
    }
    
    /**
     * Check if a step is required
     */
    private boolean isStepRequired(String stepId, List<TaskStep> allSteps) {
        for (TaskStep step : allSteps) {
            if (step.stepId.equals(stepId)) {
                return step.required;
            }
        }
        return true; // Default to required if not found
    }
    
    /**
     * Create results bundle from step results
     */
    private Bundle createResultsBundle(Map<String, ActionResult> stepResults) {
        Bundle bundle = new Bundle();
        
        for (Map.Entry<String, ActionResult> entry : stepResults.entrySet()) {
            String stepId = entry.getKey();
            ActionResult result = entry.getValue();
            
            Bundle stepBundle = new Bundle();
            stepBundle.putBoolean("success", result.success);
            if (result.errorMessage != null) {
                stepBundle.putString("errorMessage", result.errorMessage);
            }
            if (result.results != null) {
                stepBundle.putBundle("results", result.results);
            }
            
            bundle.putBundle(stepId, stepBundle);
        }
        
        return bundle;
    }
    
    /**
     * Create error result
     */
    private ExecutionResult createErrorResult(String taskId, String errorMessage) {
        ExecutionResult result = new ExecutionResult();
        result.taskId = taskId;
        result.success = false;
        result.errorMessage = errorMessage;
        result.executionTime = 0;
        return result;
    }
    
    /**
     * Get execution status for a task
     */
    public ExecutionStatus getExecutionStatus(String taskId) {
        ExecutionContext context = mActiveExecutions.get(taskId);
        if (context == null) {
            return null;
        }
        
        ExecutionStatus status = new ExecutionStatus();
        status.taskId = taskId;
        status.status = ExecutionStatus.STATUS_RUNNING;
        status.progress = calculateProgress(context);
        status.startTime = context.startTime;
        
        return status;
    }
    
    /**
     * Calculate execution progress
     */
    private int calculateProgress(ExecutionContext context) {
        // Simple progress calculation based on completed steps
        // This could be enhanced with more sophisticated progress tracking
        return 50; // Placeholder
    }
    
    /**
     * Cancel task execution
     */
    public boolean cancelExecution(String taskId) {
        ExecutionContext context = mActiveExecutions.get(taskId);
        if (context != null) {
            context.cancelled = true;
            mActiveExecutions.remove(taskId);
            if (DEBUG) Slog.d(TAG, "Task execution cancelled: " + taskId);
            return true;
        }
        return false;
    }
    
    /**
     * Shutdown the executor
     */
    public void shutdown() {
        if (mExecutorService != null && !mExecutorService.isShutdown()) {
            mExecutorService.shutdown();
            try {
                if (!mExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    mExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                mExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (DEBUG) Slog.d(TAG, "TaskExecutor shutdown");
    }
    
    /**
     * Execution context for tracking task execution
     */
    private static class ExecutionContext {
        final TaskPlan plan;
        final String packageName;
        final long startTime;
        volatile boolean cancelled = false;
        
        ExecutionContext(TaskPlan plan, String packageName) {
            this.plan = plan;
            this.packageName = packageName;
            this.startTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Result of step execution
     */
    private static class StepExecutionResult {
        final TaskStep step;
        final ActionResult actionResult;
        final String stepId;
        
        StepExecutionResult(TaskStep step, ActionResult actionResult) {
            this.step = step;
            this.actionResult = actionResult;
            this.stepId = step.stepId;
        }
    }
}
