src\frameworks\base\core\java\android\os\Parcelable.java:45: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags);
                              ^
  symbol:   class Parcel
  location: interface Parcelable
src\frameworks\base\core\java\android\os\Parcelable.java:57: error: cannot find symbol
        public T createFromParcel(Parcel source);
                                  ^
  symbol:   class Parcel
  location: interface Creator<T>
  where T is a type-variable:
    T extends Object declared in interface Creator
src\frameworks\base\core\java\android\os\Parcelable.java:75: error: cannot find symbol
        public T createFromParcel(Parcel source, ClassLoader loader);
                                  ^
  symbol:   class Parcel
  location: interface ClassLoaderCreator<T>
  where T is a type-variable:
    T extends Object declared in interface ClassLoaderCreator
3 errors
src\frameworks\base\core\java\android\os\Parcel.java:69: error: cannot find symbol
    public void writeBundle(Bundle val) {
                            ^
  symbol:   class Bundle
  location: class Parcel
src\frameworks\base\core\java\android\os\Parcel.java:124: error: cannot find symbol
    public Bundle readBundle() {
           ^
  symbol:   class Bundle
  location: class Parcel
src\frameworks\base\core\java\android\os\Parcel.java:128: error: cannot find symbol
    public Bundle readBundle(ClassLoader loader) {
           ^
  symbol:   class Bundle
  location: class Parcel
src\frameworks\base\core\java\android\os\Parcel.java:193: error: cannot find symbol
    public <T extends Parcelable> T readParcelable(ClassLoader loader) {
                      ^
  symbol:   class Parcelable
  location: class Parcel
src\frameworks\base\core\java\android\os\Parcel.java:200: error: cannot find symbol
    public void writeParcelable(Parcelable p, int flags) {
                                ^
  symbol:   class Parcelable
  location: class Parcel
src\frameworks\base\core\java\android\os\Parcel.java:130: error: cannot find symbol
            return (Bundle) mData.get(mPosition++);
                    ^
  symbol:   class Bundle
  location: class Parcel
Note: src\frameworks\base\core\java\android\os\Parcel.java uses unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
6 errors
src\frameworks\base\core\java\android\os\Bundle.java:27: error: cannot find symbol
public final class Bundle implements Parcelable {
                                     ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\os\Bundle.java:188: error: cannot find symbol
    public void putParcelable(String key, Parcelable value) {
                                          ^
  symbol:   class Parcelable
  location: class Bundle
src\frameworks\base\core\java\android\os\Bundle.java:192: error: cannot find symbol
    public <T extends Parcelable> T getParcelable(String key) {
                      ^
  symbol:   class Parcelable
  location: class Bundle
src\frameworks\base\core\java\android\os\Bundle.java:219: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class Bundle
src\frameworks\base\core\java\android\os\Bundle.java:228: error: cannot find symbol
    public static final Creator<Bundle> CREATOR = new Creator<Bundle>() {
                        ^
  symbol:   class Creator
  location: class Bundle
src\frameworks\base\core\java\android\os\Bundle.java:213: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Bundle.java:218: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Bundle.java:228: error: cannot find symbol
    public static final Creator<Bundle> CREATOR = new Creator<Bundle>() {
                                                      ^
  symbol:   class Creator
  location: class Bundle
src\frameworks\base\core\java\android\os\Bundle.java:230: error: cannot find symbol
        public Bundle createFromParcel(Parcel in) {
                                       ^
  symbol: class Parcel
src\frameworks\base\core\java\android\os\Bundle.java:229: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\os\Bundle.java:241: error: method does not override or implement a method from a supertype
        @Override
        ^
Note: src\frameworks\base\core\java\android\os\Bundle.java uses unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
11 errors
src\frameworks\base\core\java\android\os\IBinder.java:61: error: cannot find symbol
    public String getInterfaceDescriptor() throws RemoteException;
                                                  ^
  symbol:   class RemoteException
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:77: error: cannot find symbol
    public IInterface queryLocalInterface(String descriptor);
           ^
  symbol:   class IInterface
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:82: error: cannot find symbol
    public void dump(java.io.FileDescriptor fd, String[] args) throws RemoteException;
                                                                      ^
  symbol:   class RemoteException
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:87: error: cannot find symbol
    public void dumpAsync(java.io.FileDescriptor fd, String[] args) throws RemoteException;
                                                                           ^
  symbol:   class RemoteException
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:92: error: cannot find symbol
    public boolean transact(int code, Parcel data, Parcel reply, int flags)
                                      ^
  symbol:   class Parcel
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:92: error: cannot find symbol
    public boolean transact(int code, Parcel data, Parcel reply, int flags)
                                                   ^
  symbol:   class Parcel
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:93: error: cannot find symbol
        throws RemoteException;
               ^
  symbol:   class RemoteException
  location: interface IBinder
src\frameworks\base\core\java\android\os\IBinder.java:100: error: cannot find symbol
        throws RemoteException;
               ^
  symbol:   class RemoteException
  location: interface IBinder
8 errors
src\frameworks\base\core\java\android\os\IInterface.java:29: error: cannot find symbol
    public IBinder asBinder();
           ^
  symbol:   class IBinder
  location: interface IInterface
1 error
src\frameworks\base\core\java\android\os\Binder.java:24: error: cannot find symbol
public class Binder implements IBinder {
                               ^
  symbol: class IBinder
src\frameworks\base\core\java\android\os\Binder.java:26: error: cannot find symbol
    protected IInterface mOwner;
              ^
  symbol:   class IInterface
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:99: error: cannot find symbol
    public IInterface queryLocalInterface(String descriptor) {
           ^
  symbol:   class IInterface
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:114: error: cannot find symbol
    public boolean transact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
                                      ^
  symbol:   class Parcel
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:114: error: cannot find symbol
    public boolean transact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
                                                   ^
  symbol:   class Parcel
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:129: error: cannot find symbol
    public void attachInterface(IInterface owner, String descriptor) {
                                ^
  symbol:   class IInterface
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:136: error: cannot find symbol
    protected boolean onTransact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
                                           ^
  symbol:   class Parcel
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:136: error: cannot find symbol
    protected boolean onTransact(int code, Parcel data, Parcel reply, int flags) throws RemoteException {
                                                        ^
  symbol:   class Parcel
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:83: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:88: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:93: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:98: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:103: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:108: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:113: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:118: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:123: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Binder.java:137: error: cannot find symbol
        if (code == INTERFACE_TRANSACTION) {
                    ^
  symbol:   variable INTERFACE_TRANSACTION
  location: class Binder
src\frameworks\base\core\java\android\os\Binder.java:140: error: cannot find symbol
        } else if (code == DUMP_TRANSACTION) {
                           ^
  symbol:   variable DUMP_TRANSACTION
  location: class Binder
19 errors
src\frameworks\base\core\java\android\net\Uri.java:19: error: cannot find symbol
import android.os.Parcel;
                 ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\net\Uri.java:20: error: cannot find symbol
import android.os.Parcelable;
                 ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\net\Uri.java:25: error: cannot find symbol
public abstract class Uri implements Parcelable {
                                     ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\net\Uri.java:212: error: cannot find symbol
    public static final Creator<Uri> CREATOR = new Creator<Uri>() {
                        ^
  symbol:   class Creator
  location: class Uri
src\frameworks\base\core\java\android\net\Uri.java:135: error: cannot find symbol
        public void writeToParcel(Parcel dest, int flags) {
                                  ^
  symbol:   class Parcel
  location: class StringUri
src\frameworks\base\core\java\android\net\Uri.java:207: error: cannot find symbol
        public void writeToParcel(Parcel dest, int flags) {
                                  ^
  symbol:   class Parcel
  location: class HierarchicalUri
src\frameworks\base\core\java\android\net\Uri.java:129: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:134: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:201: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:206: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:212: error: cannot find symbol
    public static final Creator<Uri> CREATOR = new Creator<Uri>() {
                                                   ^
  symbol:   class Creator
  location: class Uri
src\frameworks\base\core\java\android\net\Uri.java:214: error: cannot find symbol
        public Uri createFromParcel(Parcel in) {
                                    ^
  symbol: class Parcel
src\frameworks\base\core\java\android\net\Uri.java:213: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:219: error: method does not override or implement a method from a supertype
        @Override
        ^
14 errors
src\frameworks\base\core\java\android\ai\ActionResult.java:19: error: cannot find symbol
import android.os.Bundle;
                 ^
  symbol:   class Bundle
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionResult.java:20: error: cannot find symbol
import android.os.Parcel;
                 ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionResult.java:21: error: cannot find symbol
import android.os.Parcelable;
                 ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionResult.java:26: error: cannot find symbol
public class ActionResult implements Parcelable {
                                     ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ActionResult.java:31: error: cannot find symbol
    public Bundle resultData;
           ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:50: error: cannot find symbol
    public ActionResult(boolean success, String message, Bundle resultData) {
                                                         ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:55: error: cannot find symbol
    protected ActionResult(Parcel in) {
                           ^
  symbol:   class Parcel
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:64: error: cannot find symbol
    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
                        ^
  symbol:   class Creator
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:82: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:104: error: cannot find symbol
    public Bundle getResultData() {
           ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:39: error: cannot find symbol
        this.resultData = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:52: error: cannot find symbol
        this.resultData = resultData != null ? resultData : new Bundle();
                                                                ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:59: error: cannot find symbol
        resultData = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:64: error: cannot find symbol
    public static final Creator<ActionResult> CREATOR = new Creator<ActionResult>() {
                                                            ^
  symbol:   class Creator
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:66: error: cannot find symbol
        public ActionResult createFromParcel(Parcel in) {
                                             ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ActionResult.java:65: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionResult.java:70: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionResult.java:76: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionResult.java:81: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionResult.java:105: error: cannot find symbol
        return resultData != null ? resultData : new Bundle();
                                                     ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:110: error: cannot find symbol
            resultData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:120: error: cannot find symbol
        } else if (value instanceof Bundle) {
                                    ^
  symbol:   class Bundle
  location: class ActionResult
src\frameworks\base\core\java\android\ai\ActionResult.java:121: error: cannot find symbol
            resultData.putBundle(key, (Bundle) value);
                                       ^
  symbol:   class Bundle
  location: class ActionResult
23 errors
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:19: error: cannot find symbol
import android.os.Bundle;
                 ^
  symbol:   class Bundle
  location: package android.os
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:20: error: cannot find symbol
import android.os.Parcel;
                 ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:21: error: cannot find symbol
import android.os.Parcelable;
                 ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:28: error: cannot find symbol
public class ContextSnapshot implements Parcelable {
                                        ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:32: error: cannot find symbol
    public Bundle appStates;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:33: error: cannot find symbol
    public Bundle sensorData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:34: error: cannot find symbol
    public Bundle notificationData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:35: error: cannot find symbol
    public Bundle userInteractionData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:36: error: cannot find symbol
    public Bundle environmentalData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:37: error: cannot find symbol
    public Bundle calendarData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:38: error: cannot find symbol
    public Bundle communicationData;
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:53: error: cannot find symbol
    public ContextSnapshot(Parcel in) {
                           ^
  symbol:   class Parcel
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:68: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:87: error: cannot find symbol
    public static final Creator<ContextSnapshot> CREATOR = new Creator<ContextSnapshot>() {
                        ^
  symbol:   class Creator
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:135: error: cannot find symbol
    public Bundle toBundle() {
           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:154: error: cannot find symbol
    public static ContextSnapshot fromBundle(Bundle bundle) {
                                             ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:43: error: cannot find symbol
        appStates = new Bundle();
                        ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:44: error: cannot find symbol
        sensorData = new Bundle();
                         ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:45: error: cannot find symbol
        notificationData = new Bundle();
                               ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:46: error: cannot find symbol
        userInteractionData = new Bundle();
                                  ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:47: error: cannot find symbol
        environmentalData = new Bundle();
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:48: error: cannot find symbol
        calendarData = new Bundle();
                           ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:49: error: cannot find symbol
        communicationData = new Bundle();
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:67: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:82: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:87: error: cannot find symbol
    public static final Creator<ContextSnapshot> CREATOR = new Creator<ContextSnapshot>() {
                                                               ^
  symbol:   class Creator
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:89: error: cannot find symbol
        public ContextSnapshot createFromParcel(Parcel in) {
                                                ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:88: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:93: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:121: error: cannot find symbol
        copy.appStates = new Bundle(this.appStates);
                             ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:122: error: cannot find symbol
        copy.sensorData = new Bundle(this.sensorData);
                              ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:123: error: cannot find symbol
        copy.notificationData = new Bundle(this.notificationData);
                                    ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:124: error: cannot find symbol
        copy.userInteractionData = new Bundle(this.userInteractionData);
                                       ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:125: error: cannot find symbol
        copy.environmentalData = new Bundle(this.environmentalData);
                                     ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:126: error: cannot find symbol
        copy.calendarData = new Bundle(this.calendarData);
                                ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:127: error: cannot find symbol
        copy.communicationData = new Bundle(this.communicationData);
                                     ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:136: error: cannot find symbol
        Bundle bundle = new Bundle();
        ^
  symbol:   class Bundle
  location: class ContextSnapshot
src\frameworks\base\core\java\android\ai\ContextSnapshot.java:136: error: cannot find symbol
        Bundle bundle = new Bundle();
                            ^
  symbol:   class Bundle
  location: class ContextSnapshot
38 errors
src\frameworks\base\core\java\android\ai\ActionRequest.java:19: error: cannot find symbol
import android.os.Bundle;
                 ^
  symbol:   class Bundle
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionRequest.java:20: error: cannot find symbol
import android.os.Parcel;
                 ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionRequest.java:21: error: cannot find symbol
import android.os.Parcelable;
                 ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\ai\ActionRequest.java:26: error: cannot find symbol
public class ActionRequest implements Parcelable {
                                      ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ActionRequest.java:29: error: cannot find symbol
    public Bundle parameters;
           ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:30: error: cannot find symbol
    public ContextSnapshot context;
           ^
  symbol:   class ContextSnapshot
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:57: error: cannot find symbol
    protected ActionRequest(Parcel in) {
                            ^
  symbol:   class Parcel
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:70: error: cannot find symbol
    public static final Creator<ActionRequest> CREATOR = new Creator<ActionRequest>() {
                        ^
  symbol:   class Creator
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:88: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:41: error: cannot find symbol
        this.parameters = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:60: error: cannot find symbol
        parameters = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:61: error: cannot find symbol
        context = in.readParcelable(ContextSnapshot.class.getClassLoader());
                                    ^
  symbol:   class ContextSnapshot
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:70: error: cannot find symbol
    public static final Creator<ActionRequest> CREATOR = new Creator<ActionRequest>() {
                                                             ^
  symbol:   class Creator
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:72: error: cannot find symbol
        public ActionRequest createFromParcel(Parcel in) {
                                              ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ActionRequest.java:71: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:76: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:82: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:87: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ActionRequest.java:103: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:110: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
src\frameworks\base\core\java\android\ai\ActionRequest.java:117: error: cannot find symbol
            parameters = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ActionRequest
21 errors
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:19: error: cannot find symbol
import android.os.Bundle;
                 ^
  symbol:   class Bundle
  location: package android.os
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:20: error: cannot find symbol
import android.os.Parcel;
                 ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:21: error: cannot find symbol
import android.os.Parcelable;
                 ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:26: error: cannot find symbol
public class ExecutionStatus implements Parcelable {
                                        ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:38: error: cannot find symbol
    public Bundle statusData;
           ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:57: error: cannot find symbol
    protected ExecutionStatus(Parcel in) {
                              ^
  symbol:   class Parcel
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:67: error: cannot find symbol
    public static final Creator<ExecutionStatus> CREATOR = new Creator<ExecutionStatus>() {
                        ^
  symbol:   class Creator
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:85: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:47: error: cannot find symbol
        this.statusData = new Bundle();
                              ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:62: error: cannot find symbol
        statusData = in.readParcelable(Bundle.class.getClassLoader());
                                       ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:67: error: cannot find symbol
    public static final Creator<ExecutionStatus> CREATOR = new Creator<ExecutionStatus>() {
                                                               ^
  symbol:   class Creator
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:69: error: cannot find symbol
        public ExecutionStatus createFromParcel(Parcel in) {
                                                ^
  symbol: class Parcel
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:68: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:73: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:79: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:84: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:143: error: cannot find symbol
            statusData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ExecutionStatus
src\frameworks\base\core\java\android\ai\ExecutionStatus.java:150: error: cannot find symbol
            statusData = new Bundle();
                             ^
  symbol:   class Bundle
  location: class ExecutionStatus
18 errors
src\frameworks\base\core\java\android\ai\IActionProvider.java:19: error: cannot find symbol
import android.os.IInterface;
                 ^
  symbol:   class IInterface
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:25: error: cannot find symbol
public interface IActionProvider extends IInterface {
                                         ^
  symbol: class IInterface
src\frameworks\base\core\java\android\ai\IActionProvider.java:30: error: cannot find symbol
    ActionResult executeAction(ActionRequest request) throws RemoteException;
                               ^
  symbol:   class ActionRequest
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:30: error: cannot find symbol
    ActionResult executeAction(ActionRequest request) throws RemoteException;
    ^
  symbol:   class ActionResult
  location: interface IActionProvider
src\frameworks\base\core\java\android\ai\IActionProvider.java:50: error: cannot find symbol
    public static abstract class Stub extends android.os.Binder implements IActionProvider {
                                                        ^
  symbol:   class Binder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:57: error: cannot find symbol
        public static IActionProvider asInterface(android.os.IBinder obj) {
                                                            ^
  symbol:   class IBinder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:69: error: cannot find symbol
        public android.os.IBinder asBinder() {
                         ^
  symbol:   class IBinder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:74: error: cannot find symbol
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
                                                      ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:74: error: cannot find symbol
        public boolean onTransact(int code, android.os.Parcel data, android.os.Parcel reply, int flags) throws RemoteException {
                                                                              ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:110: error: cannot find symbol
            private android.os.IBinder mRemote;
                              ^
  symbol:   class IBinder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:112: error: cannot find symbol
            Proxy(android.os.IBinder remote) {
                            ^
  symbol:   class IBinder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:117: error: cannot find symbol
            public android.os.IBinder asBinder() {
                             ^
  symbol:   class IBinder
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:126: error: cannot find symbol
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                                              ^
  symbol:   class ActionRequest
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:126: error: cannot find symbol
            public ActionResult executeAction(ActionRequest request) throws RemoteException {
                   ^
  symbol:   class ActionResult
  location: class Proxy
src\frameworks\base\core\java\android\ai\IActionProvider.java:54: error: cannot find symbol
            this.attachInterface(this, DESCRIPTOR);
                ^
  symbol: method attachInterface(Stub,String)
src\frameworks\base\core\java\android\ai\IActionProvider.java:61: error: cannot find symbol
            android.os.IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
                      ^
  symbol:   class IInterface
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:68: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:73: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:76: error: cannot find symbol
                case INTERFACE_TRANSACTION:
                     ^
  symbol:   variable INTERFACE_TRANSACTION
  location: class Stub
src\frameworks\base\core\java\android\ai\IActionProvider.java:81: error: cannot find symbol
                    ActionRequest request = ActionRequest.CREATOR.createFromParcel(data);
                    ^
  symbol:   class ActionRequest
  location: class Stub
src\frameworks\base\core\java\android\ai\IActionProvider.java:81: error: package ActionRequest does not exist
                    ActionRequest request = ActionRequest.CREATOR.createFromParcel(data);
                                                         ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:82: error: cannot find symbol
                    ActionResult result = this.executeAction(request);
                    ^
  symbol:   class ActionResult
  location: class Stub
src\frameworks\base\core\java\android\ai\IActionProvider.java:84: error: cannot find symbol
                    result.writeToParcel(reply, android.os.Parcelable.PARCELABLE_WRITE_RETURN_VALUE);
                                                          ^
  symbol:   class Parcelable
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:106: error: cannot find symbol
            return super.onTransact(code, data, reply, flags);
                   ^
  symbol:   variable super
  location: class Stub
src\frameworks\base\core\java\android\ai\IActionProvider.java:116: error: method does not override or implement a method from a supertype
            @Override
            ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:127: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:127: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                                                   ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:128: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:128: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                                                    ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:134: error: package ActionResult does not exist
                    return ActionResult.CREATOR.createFromParcel(reply);
                                       ^
src\frameworks\base\core\java\android\ai\IActionProvider.java:143: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:143: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                                                   ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:144: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:144: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                                                    ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:159: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:159: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                                                   ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:160: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:160: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                                                    ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:174: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:174: error: cannot find symbol
                android.os.Parcel data = android.os.Parcel.obtain();
                                                   ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:175: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                          ^
  symbol:   class Parcel
  location: package android.os
src\frameworks\base\core\java\android\ai\IActionProvider.java:175: error: cannot find symbol
                android.os.Parcel reply = android.os.Parcel.obtain();
                                                    ^
  symbol:   class Parcel
  location: package android.os
42 errors
