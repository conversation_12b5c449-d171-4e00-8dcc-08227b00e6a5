src\frameworks\base\core\java\android\os\UserHandle.java:23: error: cannot find symbol
public final class UserHandle implements Parcelable {
                                         ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\os\UserHandle.java:337: error: cannot find symbol
    public void writeToParcel(Parcel out, int flags) {
                              ^
  symbol:   class Parcel
  location: class UserHandle
src\frameworks\base\core\java\android\os\UserHandle.java:350: error: cannot find symbol
    public static void writeToParcel(UserHandle h, Parcel out) {
                                                   ^
  symbol:   class Parcel
  location: class UserHandle
src\frameworks\base\core\java\android\os\UserHandle.java:369: error: cannot find symbol
    public static UserHandle readFromParcel(Parcel in) {
                                            ^
  symbol:   class Parcel
  location: class UserHandle
src\frameworks\base\core\java\android\os\UserHandle.java:374: error: cannot find symbol
    public static final Creator<UserHandle> CREATOR
                        ^
  symbol:   class Creator
  location: class UserHandle
src\frameworks\base\core\java\android\os\UserHandle.java:395: error: cannot find symbol
    public UserHandle(Parcel in) {
                      ^
  symbol:   class Parcel
  location: class UserHandle
src\frameworks\base\core\java\android\os\Message.java:24: error: cannot find symbol
public final class Message implements Parcelable {
                                      ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\os\Message.java:57: error: cannot find symbol
    private Bundle data;
            ^
  symbol:   class Bundle
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:200: error: cannot find symbol
    public Bundle getData() {
           ^
  symbol:   class Bundle
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:207: error: cannot find symbol
    public Bundle peekData() {
           ^
  symbol:   class Bundle
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:211: error: cannot find symbol
    public void setData(Bundle data) {
                        ^
  symbol:   class Bundle
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:258: error: cannot find symbol
    public void writeToParcel(Parcel dest, int flags) {
                              ^
  symbol:   class Parcel
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:269: error: cannot find symbol
    public static final Creator<Message> CREATOR = new Creator<Message>() {
                        ^
  symbol:   class Creator
  location: class Message
src\frameworks\base\core\java\android\os\UserHandle.java:37: error: reference to UserHandle is ambiguous
    public static final UserHandle ALL = new UserHandle(USER_ALL);
                                         ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:47: error: reference to UserHandle is ambiguous
    public static final UserHandle CURRENT = new UserHandle(USER_CURRENT);
                                             ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:61: error: reference to UserHandle is ambiguous
    public static final UserHandle CURRENT_OR_SELF = new UserHandle(USER_CURRENT_OR_SELF);
                                                     ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:73: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public static final int USER_OWNER = 0;
                            ^
src\frameworks\base\core\java\android\os\UserHandle.java:80: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public static final UserHandle OWNER = new UserHandle(USER_OWNER);
                                   ^
src\frameworks\base\core\java\android\os\UserHandle.java:80: error: reference to UserHandle is ambiguous
    public static final UserHandle OWNER = new UserHandle(USER_OWNER);
                                           ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:90: error: reference to UserHandle is ambiguous
    public static final UserHandle SYSTEM = new UserHandle(USER_SYSTEM);
                                            ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:269: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public boolean isOwner() {
                   ^
src\frameworks\base\core\java\android\os\UserHandle.java:288: error: reference to UserHandle is ambiguous
        return userId == 0 ? SYSTEM : new UserHandle(userId);
                                      ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:371: error: reference to UserHandle is ambiguous
        return h != USER_NULL ? new UserHandle(h) : null;
                                ^
  both constructor UserHandle(int) in UserHandle and constructor UserHandle(Parcel) in UserHandle match
src\frameworks\base\core\java\android\os\UserHandle.java:375: error: cannot find symbol
            = new Creator<UserHandle>() {
                  ^
  symbol:   class Creator
  location: class UserHandle
src\frameworks\base\core\java\android\os\UserHandle.java:376: error: cannot find symbol
        public UserHandle createFromParcel(Parcel in) {
                                           ^
  symbol: class Parcel
src\frameworks\base\core\java\android\os\Message.java:202: error: cannot find symbol
            data = new Bundle();
                       ^
  symbol:   class Bundle
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:252: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Message.java:257: error: method does not override or implement a method from a supertype
    @Override
    ^
src\frameworks\base\core\java\android\os\Message.java:269: error: cannot find symbol
    public static final Creator<Message> CREATOR = new Creator<Message>() {
                                                       ^
  symbol:   class Creator
  location: class Message
src\frameworks\base\core\java\android\os\Message.java:271: error: cannot find symbol
        public Message createFromParcel(Parcel source) {
                                        ^
  symbol: class Parcel
src\frameworks\base\core\java\android\os\Message.java:270: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\os\Message.java:284: error: method does not override or implement a method from a supertype
        @Override
        ^
29 errors
3 warnings
src\frameworks\base\core\java\android\content\Intent.java:19: error: package android.os does not exist
import android.os.Bundle;
                 ^
src\frameworks\base\core\java\android\content\Intent.java:58: error: cannot find symbol
    private Bundle mExtras;
            ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:75: error: package android.net does not exist
    public Intent(String action, android.net.Uri data) {
                                            ^
src\frameworks\base\core\java\android\content\Intent.java:154: error: cannot find symbol
    public Bundle getExtras() {
           ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:158: error: cannot find symbol
    public Intent putExtras(Bundle extras) {
                            ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:62: error: cannot find symbol
        mExtras = new Bundle();
                      ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:93: error: cannot find symbol
                mExtras = new Bundle(o.mExtras);
                              ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:161: error: cannot find symbol
                mExtras = new Bundle();
                              ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:169: error: cannot find symbol
        if (mExtras == null) mExtras = new Bundle();
                                           ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:175: error: cannot find symbol
        if (mExtras == null) mExtras = new Bundle();
                                           ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:181: error: cannot find symbol
        if (mExtras == null) mExtras = new Bundle();
                                           ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:187: error: cannot find symbol
        if (mExtras == null) mExtras = new Bundle();
                                           ^
  symbol:   class Bundle
  location: class Intent
src\frameworks\base\core\java\android\content\Intent.java:193: error: cannot find symbol
        if (mExtras == null) mExtras = new Bundle();
                                           ^
  symbol:   class Bundle
  location: class Intent
13 errors
src\frameworks\base\core\java\android\net\Uri.java:19: error: package android.os does not exist
import android.os.Parcel;
                 ^
src\frameworks\base\core\java\android\net\Uri.java:20: error: package android.os does not exist
import android.os.Parcelable;
                 ^
src\frameworks\base\core\java\android\net\Uri.java:25: error: cannot find symbol
public abstract class Uri implements Parcelable {
                                     ^
  symbol: class Parcelable
src\frameworks\base\core\java\android\net\Uri.java:212: error: cannot find symbol
    public static final Creator<Uri> CREATOR = new Creator<Uri>() {
                        ^
  symbol:   class Creator
  location: class Uri
src\frameworks\base\core\java\android\net\Uri.java:135: error: cannot find symbol
        public void writeToParcel(Parcel dest, int flags) {
                                  ^
  symbol:   class Parcel
  location: class StringUri
src\frameworks\base\core\java\android\net\Uri.java:207: error: cannot find symbol
        public void writeToParcel(Parcel dest, int flags) {
                                  ^
  symbol:   class Parcel
  location: class HierarchicalUri
src\frameworks\base\core\java\android\net\Uri.java:129: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:134: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:201: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:206: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:212: error: cannot find symbol
    public static final Creator<Uri> CREATOR = new Creator<Uri>() {
                                                   ^
  symbol:   class Creator
  location: class Uri
src\frameworks\base\core\java\android\net\Uri.java:214: error: cannot find symbol
        public Uri createFromParcel(Parcel in) {
                                    ^
  symbol: class Parcel
src\frameworks\base\core\java\android\net\Uri.java:213: error: method does not override or implement a method from a supertype
        @Override
        ^
src\frameworks\base\core\java\android\net\Uri.java:219: error: method does not override or implement a method from a supertype
        @Override
        ^
14 errors
error: file not found: src\frameworks\base\core\java\android\app\PendingIntent.java
Usage: javac <options> <source files>
use --help for a list of possible options
error: file not found: src\frameworks\base\core\java\android\graphics\Bitmap.java
Usage: javac <options> <source files>
use --help for a list of possible options
