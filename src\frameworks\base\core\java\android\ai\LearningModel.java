/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents a learning model for AI personalization
 */
public class LearningModel implements Parcelable {
    private final String mModelId;
    private final String mModelType;
    private final Bundle mModelData;
    private final float mAccuracy;
    private final long mLastTrained;

    public LearningModel(String modelId, String modelType, Bundle modelData, float accuracy) {
        mModelId = modelId;
        mModelType = modelType;
        mModelData = modelData != null ? new Bundle(modelData) : new Bundle();
        mAccuracy = accuracy;
        mLastTrained = System.currentTimeMillis();
    }

    private LearningModel(Parcel in) {
        mModelId = in.readString();
        mModelType = in.readString();
        mModelData = in.readBundle(getClass().getClassLoader());
        mAccuracy = in.readFloat();
        mLastTrained = in.readLong();
    }

    public String getModelId() {
        return mModelId;
    }

    public String getModelType() {
        return mModelType;
    }

    public Bundle getModelData() {
        return new Bundle(mModelData);
    }

    public float getAccuracy() {
        return mAccuracy;
    }

    public long getLastTrained() {
        return mLastTrained;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mModelId);
        dest.writeString(mModelType);
        dest.writeBundle(mModelData);
        dest.writeFloat(mAccuracy);
        dest.writeLong(mLastTrained);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<LearningModel> CREATOR = new Creator<LearningModel>() {
        @Override
        public LearningModel createFromParcel(Parcel in) {
            return new LearningModel(in);
        }

        @Override
        public LearningModel[] newArray(int size) {
            return new LearningModel[size];
        }
    };
}
