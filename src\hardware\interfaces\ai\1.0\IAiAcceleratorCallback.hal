/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.hardware.ai@1.0;

/**
 * AI Accelerator Callback Interface for Jarvis OS
 * 
 * Provides asynchronous callbacks for AI accelerator operations
 * including model loading, inference completion, and error notifications.
 */
interface IAiAcceleratorCallback {
    /**
     * Event types for accelerator callbacks
     */
    enum EventType : uint32_t {
        MODEL_LOADED = 0,
        MODEL_LOAD_FAILED = 1,
        INFERENCE_COMPLETED = 2,
        INFERENCE_FAILED = 3,
        EXECUTION_CANCELLED = 4,
        ACCELERATOR_ERROR = 5,
        POWER_STATE_CHANGED = 6,
        THERMAL_THROTTLING = 7,
    };

    /**
     * Error codes for callback events
     */
    enum ErrorCode : uint32_t {
        NONE = 0,
        INVALID_MODEL = 1,
        INSUFFICIENT_MEMORY = 2,
        HARDWARE_FAILURE = 3,
        TIMEOUT = 4,
        CANCELLED = 5,
        THERMAL_LIMIT = 6,
        POWER_LIMIT = 7,
        UNKNOWN_ERROR = 100,
    };

    /**
     * Callback event data structure
     */
    struct CallbackEvent {
        EventType eventType;
        uint32_t requestId;      // Model ID or execution ID
        ErrorCode errorCode;
        string errorMessage;
        uint64_t timestamp;
        vec<uint8_t> eventData; // Additional event-specific data
    };

    /**
     * Performance data for completed inference
     */
    struct InferenceResult {
        uint32_t executionId;
        uint64_t executionTimeUs;
        uint64_t queueTimeUs;
        uint64_t memoryUsedBytes;
        float powerConsumptionMw;
        bool successful;
        ErrorCode errorCode;
        string errorMessage;
    };

    /**
     * Called when a model loading operation completes
     * 
     * @param modelId Identifier of the loaded model
     * @param successful True if loading succeeded, false otherwise
     * @param errorCode Error code if loading failed
     * @param errorMessage Human-readable error message
     */
    oneway onModelLoaded(uint32_t modelId, bool successful, ErrorCode errorCode, string errorMessage);

    /**
     * Called when an inference execution completes
     * 
     * @param result Inference execution result with performance metrics
     */
    oneway onInferenceCompleted(InferenceResult result);

    /**
     * Called when an execution is cancelled
     * 
     * @param executionId Identifier of the cancelled execution
     * @param reason Reason for cancellation
     */
    oneway onExecutionCancelled(uint32_t executionId, string reason);

    /**
     * Called when an accelerator error occurs
     * 
     * @param event Error event information
     */
    oneway onAcceleratorError(CallbackEvent event);

    /**
     * Called when accelerator power state changes
     * 
     * @param powerMode New power mode (0=low power, 1=balanced, 2=performance)
     * @param powerConsumptionMw Current power consumption in milliwatts
     */
    oneway onPowerStateChanged(uint32_t powerMode, float powerConsumptionMw);

    /**
     * Called when thermal throttling occurs
     * 
     * @param throttleLevel Throttling level (0=none, 1=light, 2=moderate, 3=severe)
     * @param temperatureCelsius Current temperature in Celsius
     */
    oneway onThermalThrottling(uint32_t throttleLevel, float temperatureCelsius);

    /**
     * Called with periodic status updates
     * 
     * @param activeExecutions Number of currently active executions
     * @param queuedExecutions Number of queued executions
     * @param memoryUsagePercent Memory usage as percentage
     * @param powerConsumptionMw Current power consumption
     */
    oneway onStatusUpdate(uint32_t activeExecutions, uint32_t queuedExecutions, 
                         float memoryUsagePercent, float powerConsumptionMw);
};
