/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a planned task with action steps
 */
public class TaskPlan implements Parcelable {
    public String taskId;
    public String originalGoal;
    public List<TaskStep> steps;
    public Bundle dependencies;
    public Bundle conditionalLogic;
    public long estimatedDuration;
    public double confidenceScore;
    public Bundle metadata;

    public TaskPlan() {
        this.taskId = null;
        this.originalGoal = null;
        this.steps = new ArrayList<>();
        this.dependencies = new Bundle();
        this.conditionalLogic = new Bundle();
        this.estimatedDuration = 0;
        this.confidenceScore = 0.0;
        this.metadata = new Bundle();
    }

    public TaskPlan(String taskId, String originalGoal) {
        this();
        this.taskId = taskId;
        this.originalGoal = originalGoal;
    }

    protected TaskPlan(Parcel in) {
        taskId = in.readString();
        originalGoal = in.readString();
        steps = in.createTypedArrayList(TaskStep.CREATOR);
        dependencies = in.readBundle(getClass().getClassLoader());
        conditionalLogic = in.readBundle(getClass().getClassLoader());
        estimatedDuration = in.readLong();
        confidenceScore = in.readDouble();
        metadata = in.readBundle(getClass().getClassLoader());
    }

    public static final Creator<TaskPlan> CREATOR = new Creator<TaskPlan>() {
        @Override
        public TaskPlan createFromParcel(Parcel in) {
            return new TaskPlan(in);
        }

        @Override
        public TaskPlan[] newArray(int size) {
            return new TaskPlan[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(taskId);
        dest.writeString(originalGoal);
        dest.writeTypedList(steps);
        dest.writeBundle(dependencies);
        dest.writeBundle(conditionalLogic);
        dest.writeLong(estimatedDuration);
        dest.writeDouble(confidenceScore);
        dest.writeBundle(metadata);
    }

    public void addStep(TaskStep step) {
        if (steps == null) {
            steps = new ArrayList<>();
        }
        steps.add(step);
    }

    public boolean isEmpty() {
        return steps == null || steps.isEmpty();
    }

    public int getStepCount() {
        return steps != null ? steps.size() : 0;
    }
}
