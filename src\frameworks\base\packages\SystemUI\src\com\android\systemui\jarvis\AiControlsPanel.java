/*
 * Copyright (C) 2024 Jarvis OS
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.Switch;
import android.widget.TextView;

import com.android.systemui.R;

/**
 * AI Controls Panel for comprehensive AI settings and controls
 * Provides quick access to all AI features and preferences
 */
public class AiControlsPanel {
    private static final String TAG = "AiControlsPanel";
    private static final boolean DEBUG = true;
    
    private final Context mContext;
    private final WindowManager mWindowManager;
    private final Handler mHandler;
    private final AiActivityDashboard mDashboard;
    private final AiPrivacyControls mPrivacyControls;
    
    private View mPanelView;
    private boolean mIsShowing = false;
    private boolean mIsAnimating = false;
    
    // Control views
    private Switch mAiEnabledSwitch;
    private Switch mVoiceAssistantSwitch;
    private Switch mSuggestionsSwitch;
    private Switch mPrivacyModeSwitch;
    private SeekBar mSuggestionIntensitySeekBar;
    private SeekBar mPrivacyLevelSeekBar;
    private TextView mStatusText;
    private LinearLayout mDashboardContainer;
    private LinearLayout mPrivacyContainer;
    
    public AiControlsPanel(Context context) {
        mContext = context;
        mWindowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        mHandler = new Handler(Looper.getMainLooper());
        mDashboard = new AiActivityDashboard(context);
        mPrivacyControls = new AiPrivacyControls(context);
        
        initializePanelView();
        
        if (DEBUG) Log.d(TAG, "AiControlsPanel created");
    }
    
    private void initializePanelView() {
        LayoutInflater inflater = LayoutInflater.from(mContext);
        mPanelView = inflater.inflate(R.layout.jarvis_ai_controls_panel, null);
        
        // Initialize control views
        mAiEnabledSwitch = mPanelView.findViewById(R.id.ai_enabled_switch);
        mVoiceAssistantSwitch = mPanelView.findViewById(R.id.voice_assistant_switch);
        mSuggestionsSwitch = mPanelView.findViewById(R.id.suggestions_switch);
        mPrivacyModeSwitch = mPanelView.findViewById(R.id.privacy_mode_switch);
        mSuggestionIntensitySeekBar = mPanelView.findViewById(R.id.suggestion_intensity_seekbar);
        mPrivacyLevelSeekBar = mPanelView.findViewById(R.id.privacy_level_seekbar);
        mStatusText = mPanelView.findViewById(R.id.status_text);
        mDashboardContainer = mPanelView.findViewById(R.id.dashboard_container);
        mPrivacyContainer = mPanelView.findViewById(R.id.privacy_container);
        
        setupControlListeners();
        setupDashboard();
        setupPrivacyControls();
        
        // Close button
        Button closeButton = mPanelView.findViewById(R.id.close_button);
        closeButton.setOnClickListener(v -> hide());
        
        // Settings button
        Button settingsButton = mPanelView.findViewById(R.id.settings_button);
        settingsButton.setOnClickListener(v -> openSettings());
    }
    
    private void setupControlListeners() {
        // AI Enabled Switch
        mAiEnabledSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (DEBUG) Log.d(TAG, "AI enabled changed: " + isChecked);
            // TODO: Enable/disable AI services
            updateStatus(isChecked ? "AI services enabled" : "AI services disabled");
        });
        
        // Voice Assistant Switch
        mVoiceAssistantSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (DEBUG) Log.d(TAG, "Voice assistant changed: " + isChecked);
            // TODO: Enable/disable voice assistant
            updateStatus(isChecked ? "Voice assistant enabled" : "Voice assistant disabled");
        });
        
        // Suggestions Switch
        mSuggestionsSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (DEBUG) Log.d(TAG, "Suggestions changed: " + isChecked);
            // TODO: Enable/disable suggestions
            mSuggestionIntensitySeekBar.setEnabled(isChecked);
            updateStatus(isChecked ? "Suggestions enabled" : "Suggestions disabled");
        });
        
        // Privacy Mode Switch
        mPrivacyModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (DEBUG) Log.d(TAG, "Privacy mode changed: " + isChecked);
            // TODO: Enable/disable privacy mode
            updatePrivacyMode(isChecked);
            updateStatus(isChecked ? "Privacy mode enabled" : "Privacy mode disabled");
        });
        
        // Suggestion Intensity SeekBar
        mSuggestionIntensitySeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    String intensity = getIntensityLabel(progress);
                    updateStatus("Suggestion intensity: " + intensity);
                    // TODO: Update suggestion intensity
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
        
        // Privacy Level SeekBar
        mPrivacyLevelSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    String level = getPrivacyLabel(progress);
                    updateStatus("Privacy level: " + level);
                    // TODO: Update privacy level
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }
    
    private void setupDashboard() {
        // Add dashboard view to container
        View dashboardView = mDashboard.createDashboardView();
        if (dashboardView != null) {
            mDashboardContainer.addView(dashboardView);
        }
    }
    
    private void setupPrivacyControls() {
        // Add privacy controls to container
        View privacyView = mPrivacyControls.createPrivacyControlsView();
        if (privacyView != null) {
            mPrivacyContainer.addView(privacyView);
        }
    }
    
    public void show() {
        if (mIsShowing || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Showing AI controls panel");
        
        mIsAnimating = true;
        
        // Add to window manager
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.TYPE_SYSTEM_DIALOG,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
            PixelFormat.TRANSLUCENT
        );
        
        params.gravity = Gravity.TOP | Gravity.CENTER_HORIZONTAL;
        params.y = 100; // Offset from top
        
        mWindowManager.addView(mPanelView, params);
        
        // Animate in
        mPanelView.setAlpha(0f);
        mPanelView.setTranslationY(-200f);
        
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(mPanelView, "alpha", 0f, 1f);
        ObjectAnimator slideIn = ObjectAnimator.ofFloat(mPanelView, "translationY", -200f, 0f);
        
        fadeIn.setDuration(300);
        slideIn.setDuration(300);
        
        fadeIn.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                mIsShowing = true;
                mIsAnimating = false;
                updateControlStates();
                startDashboardUpdates();
            }
        });
        
        fadeIn.start();
        slideIn.start();
    }
    
    public void hide() {
        if (!mIsShowing || mIsAnimating) {
            return;
        }
        
        if (DEBUG) Log.d(TAG, "Hiding AI controls panel");
        
        mIsAnimating = true;
        stopDashboardUpdates();
        
        // Animate out
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(mPanelView, "alpha", 1f, 0f);
        ObjectAnimator slideOut = ObjectAnimator.ofFloat(mPanelView, "translationY", 0f, -200f);
        
        fadeOut.setDuration(200);
        slideOut.setDuration(200);
        
        fadeOut.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                mWindowManager.removeView(mPanelView);
                mIsShowing = false;
                mIsAnimating = false;
            }
        });
        
        fadeOut.start();
        slideOut.start();
    }
    
    private void updateControlStates() {
        // TODO: Get current AI service states and update controls
        mAiEnabledSwitch.setChecked(true);
        mVoiceAssistantSwitch.setChecked(true);
        mSuggestionsSwitch.setChecked(true);
        mPrivacyModeSwitch.setChecked(false);
        mSuggestionIntensitySeekBar.setProgress(50);
        mPrivacyLevelSeekBar.setProgress(30);
        
        updateStatus("AI services active");
    }
    
    private void updateStatus(String status) {
        if (mStatusText != null) {
            mStatusText.setText(status);
        }
    }
    
    private void updatePrivacyMode(boolean enabled) {
        mPrivacyLevelSeekBar.setEnabled(enabled);
        if (mPrivacyControls != null) {
            mPrivacyControls.setPrivacyModeEnabled(enabled);
        }
    }
    
    private String getIntensityLabel(int progress) {
        if (progress < 25) return "Low";
        if (progress < 50) return "Medium";
        if (progress < 75) return "High";
        return "Maximum";
    }
    
    private String getPrivacyLabel(int progress) {
        if (progress < 25) return "Basic";
        if (progress < 50) return "Standard";
        if (progress < 75) return "Enhanced";
        return "Maximum";
    }
    
    private void startDashboardUpdates() {
        if (mDashboard != null) {
            mDashboard.startUpdates();
        }
    }
    
    private void stopDashboardUpdates() {
        if (mDashboard != null) {
            mDashboard.stopUpdates();
        }
    }
    
    private void openSettings() {
        if (DEBUG) Log.d(TAG, "Opening AI settings");
        
        Intent intent = new Intent();
        intent.setAction("android.settings.JARVIS_AI_SETTINGS");
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        mContext.startActivity(intent);
        
        hide();
    }
    
    public boolean isShowing() {
        return mIsShowing;
    }
}
