/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.util.Slog;

import java.io.PrintWriter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Manager for AI inference operations in Jarvis OS.
 * 
 * Provides high-level interface to native AI inference capabilities
 * including model loading, session management, and performance monitoring.
 */
public class AiInferenceManager {
    private static final String TAG = "AiInferenceManager";
    private static final boolean DEBUG = true;

    // Model types (must match native enum)
    public static final int MODEL_TYPE_CONTEXT_ANALYSIS = 0;
    public static final int MODEL_TYPE_PERSONALIZATION = 1;
    public static final int MODEL_TYPE_TASK_PLANNING = 2;
    public static final int MODEL_TYPE_CONTENT_UNDERSTANDING = 3;
    public static final int MODEL_TYPE_PATTERN_RECOGNITION = 4;
    public static final int MODEL_TYPE_SENTIMENT_ANALYSIS = 5;

    // Hardware types (must match native enum)
    public static final int HARDWARE_CPU = 0;
    public static final int HARDWARE_GPU = 1;
    public static final int HARDWARE_NPU = 2;
    public static final int HARDWARE_DSP = 3;
    public static final int HARDWARE_AUTO = 4;

    private final Context mContext;
    private final Object mLock = new Object();
    
    // Model and session tracking
    private final ConcurrentHashMap<Long, ModelInfo> mLoadedModels = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<Long, InferenceSession> mActiveSessions = new ConcurrentHashMap<>();
    private final AtomicLong mNextModelId = new AtomicLong(1);
    private final AtomicLong mNextSessionId = new AtomicLong(1);
    
    // Statistics
    private long mTotalInferences = 0;
    private long mSuccessfulInferences = 0;
    private long mFailedInferences = 0;
    private float mAverageInferenceTime = 0.0f;
    
    private volatile boolean mInitialized = false;

    static {
        System.loadLibrary("jarvis_jni");
    }

    public AiInferenceManager(Context context) {
        mContext = context;
        
        if (DEBUG) Slog.d(TAG, "AiInferenceManager created");
    }

    /**
     * Initialize the inference manager
     */
    public boolean initialize() {
        synchronized (mLock) {
            if (mInitialized) {
                return true;
            }
            
            if (!nativeInit()) {
                Slog.e(TAG, "Failed to initialize native AI inference library");
                return false;
            }
            
            mInitialized = true;
            
            if (DEBUG) Slog.d(TAG, "AiInferenceManager initialized");
            return true;
        }
    }

    /**
     * Cleanup and shutdown the inference manager
     */
    public void cleanup() {
        synchronized (mLock) {
            if (!mInitialized) {
                return;
            }
            
            // Destroy all active sessions
            for (InferenceSession session : mActiveSessions.values()) {
                nativeDestroySession(session.mNativePtr);
            }
            mActiveSessions.clear();
            
            // Unload all models
            for (ModelInfo model : mLoadedModels.values()) {
                nativeUnloadModel(model.mNativePtr);
            }
            mLoadedModels.clear();
            
            nativeCleanup();
            mInitialized = false;
            
            if (DEBUG) Slog.d(TAG, "AiInferenceManager cleaned up");
        }
    }

    /**
     * Load an AI model from file
     */
    public long loadModel(String modelPath, int modelType) {
        if (!mInitialized) {
            throw new IllegalStateException("AiInferenceManager not initialized");
        }
        
        if (modelPath == null) {
            throw new IllegalArgumentException("Model path cannot be null");
        }
        
        long nativePtr = nativeLoadModel(modelPath, modelType);
        if (nativePtr == 0) {
            throw new RuntimeException("Failed to load model: " + modelPath);
        }
        
        long modelId = mNextModelId.getAndIncrement();
        ModelInfo modelInfo = new ModelInfo(modelId, nativePtr, modelPath, modelType);
        mLoadedModels.put(modelId, modelInfo);
        
        if (DEBUG) Slog.d(TAG, "Model loaded: " + modelPath + " (ID: " + modelId + ")");
        return modelId;
    }

    /**
     * Load an AI model from memory buffer
     */
    public long loadModelFromBuffer(byte[] modelData, int modelType) {
        if (!mInitialized) {
            throw new IllegalStateException("AiInferenceManager not initialized");
        }
        
        if (modelData == null || modelData.length == 0) {
            throw new IllegalArgumentException("Model data cannot be null or empty");
        }
        
        long nativePtr = nativeLoadModelFromBuffer(modelData, modelType);
        if (nativePtr == 0) {
            throw new RuntimeException("Failed to load model from buffer");
        }
        
        long modelId = mNextModelId.getAndIncrement();
        ModelInfo modelInfo = new ModelInfo(modelId, nativePtr, "buffer", modelType);
        mLoadedModels.put(modelId, modelInfo);
        
        if (DEBUG) Slog.d(TAG, "Model loaded from buffer (ID: " + modelId + ")");
        return modelId;
    }

    /**
     * Unload a model
     */
    public void unloadModel(long modelId) {
        ModelInfo modelInfo = mLoadedModels.remove(modelId);
        if (modelInfo != null) {
            nativeUnloadModel(modelInfo.mNativePtr);
            
            if (DEBUG) Slog.d(TAG, "Model unloaded (ID: " + modelId + ")");
        }
    }

    /**
     * Get model information
     */
    public Bundle getModelInfo(long modelId) {
        ModelInfo modelInfo = mLoadedModels.get(modelId);
        if (modelInfo == null) {
            throw new IllegalArgumentException("Model not found: " + modelId);
        }
        
        // Get native model info
        ModelInfo nativeInfo = nativeGetModelInfo(modelInfo.mNativePtr);
        
        Bundle info = new Bundle();
        info.putLong("model_id", modelId);
        info.putString("model_path", modelInfo.mModelPath);
        info.putInt("model_type", modelInfo.mModelType);
        info.putLong("load_time", modelInfo.mLoadTime);
        
        if (nativeInfo != null) {
            // Add native model information
            // Note: In production, we would extract all fields from nativeInfo
        }
        
        return info;
    }

    /**
     * Create an inference session
     */
    public long createSession(long modelId) {
        ModelInfo modelInfo = mLoadedModels.get(modelId);
        if (modelInfo == null) {
            throw new IllegalArgumentException("Model not found: " + modelId);
        }
        
        long nativePtr = nativeCreateSession(modelInfo.mNativePtr);
        if (nativePtr == 0) {
            throw new RuntimeException("Failed to create inference session");
        }
        
        long sessionId = mNextSessionId.getAndIncrement();
        InferenceSession session = new InferenceSession(sessionId, nativePtr, modelId);
        mActiveSessions.put(sessionId, session);
        
        if (DEBUG) Slog.d(TAG, "Inference session created (ID: " + sessionId + ")");
        return sessionId;
    }

    /**
     * Destroy an inference session
     */
    public void destroySession(long sessionId) {
        InferenceSession session = mActiveSessions.remove(sessionId);
        if (session != null) {
            nativeDestroySession(session.mNativePtr);
            
            if (DEBUG) Slog.d(TAG, "Inference session destroyed (ID: " + sessionId + ")");
        }
    }

    /**
     * Run inference on a session
     */
    public boolean runInference(long sessionId) {
        InferenceSession session = mActiveSessions.get(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("Session not found: " + sessionId);
        }
        
        long startTime = System.currentTimeMillis();
        boolean success = nativeRunInference(session.mNativePtr);
        long endTime = System.currentTimeMillis();
        
        // Update statistics
        mTotalInferences++;
        if (success) {
            mSuccessfulInferences++;
            session.mLastInferenceTime = endTime - startTime;
            session.mTotalInferences++;
            
            // Update average inference time
            float newTime = endTime - startTime;
            mAverageInferenceTime = (mAverageInferenceTime * (mSuccessfulInferences - 1) + newTime) / mSuccessfulInferences;
        } else {
            mFailedInferences++;
        }
        
        if (DEBUG) Slog.d(TAG, "Inference " + (success ? "succeeded" : "failed") + 
            " (session: " + sessionId + ", time: " + (endTime - startTime) + "ms)");
        
        return success;
    }

    /**
     * Get performance metrics for a session
     */
    public Bundle getPerformanceMetrics(long sessionId) {
        InferenceSession session = mActiveSessions.get(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("Session not found: " + sessionId);
        }
        
        PerformanceMetrics nativeMetrics = nativeGetPerformanceMetrics(session.mNativePtr);
        
        Bundle metrics = new Bundle();
        metrics.putLong("session_id", sessionId);
        metrics.putLong("total_inferences", session.mTotalInferences);
        metrics.putLong("last_inference_time", session.mLastInferenceTime);
        
        if (nativeMetrics != null) {
            metrics.putFloat("inference_time_ms", nativeMetrics.inferenceTimeMs);
            metrics.putLong("memory_used_bytes", nativeMetrics.memoryUsedBytes);
            metrics.putFloat("cpu_usage_percent", nativeMetrics.cpuUsagePercent);
        }
        
        return metrics;
    }

    /**
     * Check if hardware acceleration is available
     */
    public boolean isHardwareAvailable(int hardwareType) {
        return nativeIsHardwareAvailable(hardwareType);
    }

    /**
     * Enable or disable debug logging
     */
    public void setDebugLogging(boolean enable) {
        nativeSetDebugLogging(enable);
    }

    /**
     * Get overall statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("initialized", mInitialized);
        stats.putInt("loaded_models", mLoadedModels.size());
        stats.putInt("active_sessions", mActiveSessions.size());
        stats.putLong("total_inferences", mTotalInferences);
        stats.putLong("successful_inferences", mSuccessfulInferences);
        stats.putLong("failed_inferences", mFailedInferences);
        stats.putFloat("average_inference_time", mAverageInferenceTime);
        stats.putFloat("success_rate", mTotalInferences > 0 ? 
            (float) mSuccessfulInferences / mTotalInferences * 100.0f : 0.0f);
        
        return stats;
    }

    /**
     * Dump manager state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("  AiInferenceManager State:");
        pw.println("    Initialized: " + mInitialized);
        pw.println("    Loaded Models: " + mLoadedModels.size());
        pw.println("    Active Sessions: " + mActiveSessions.size());
        pw.println("    Total Inferences: " + mTotalInferences);
        pw.println("    Successful Inferences: " + mSuccessfulInferences);
        pw.println("    Failed Inferences: " + mFailedInferences);
        pw.println("    Average Inference Time: " + mAverageInferenceTime + "ms");
        
        if (mTotalInferences > 0) {
            float successRate = (float) mSuccessfulInferences / mTotalInferences * 100.0f;
            pw.println("    Success Rate: " + successRate + "%");
        }
        
        pw.println("    Hardware Availability:");
        pw.println("      CPU: " + isHardwareAvailable(HARDWARE_CPU));
        pw.println("      GPU: " + isHardwareAvailable(HARDWARE_GPU));
        pw.println("      NPU: " + isHardwareAvailable(HARDWARE_NPU));
        pw.println("      DSP: " + isHardwareAvailable(HARDWARE_DSP));
    }

    // Inner classes
    public static class ModelInfo {
        public final long mModelId;
        public final long mNativePtr;
        public final String mModelPath;
        public final int mModelType;
        public final long mLoadTime;

        public ModelInfo(long modelId, long nativePtr, String modelPath, int modelType) {
            mModelId = modelId;
            mNativePtr = nativePtr;
            mModelPath = modelPath;
            mModelType = modelType;
            mLoadTime = System.currentTimeMillis();
        }
    }

    public static class InferenceSession {
        public final long mSessionId;
        public final long mNativePtr;
        public final long mModelId;
        public final long mCreateTime;
        public long mTotalInferences;
        public long mLastInferenceTime;

        public InferenceSession(long sessionId, long nativePtr, long modelId) {
            mSessionId = sessionId;
            mNativePtr = nativePtr;
            mModelId = modelId;
            mCreateTime = System.currentTimeMillis();
            mTotalInferences = 0;
            mLastInferenceTime = 0;
        }
    }

    public static class PerformanceMetrics {
        public float inferenceTimeMs;
        public long memoryUsedBytes;
        public float cpuUsagePercent;
    }

    // Native method declarations
    private static native boolean nativeInit();
    private static native void nativeCleanup();
    private static native long nativeLoadModel(String modelPath, int modelType);
    private static native long nativeLoadModelFromBuffer(byte[] modelData, int modelType);
    private static native void nativeUnloadModel(long modelPtr);
    private static native ModelInfo nativeGetModelInfo(long modelPtr);
    private static native long nativeCreateSession(long modelPtr);
    private static native void nativeDestroySession(long sessionPtr);
    private static native boolean nativeRunInference(long sessionPtr);
    private static native PerformanceMetrics nativeGetPerformanceMetrics(long sessionPtr);
    private static native boolean nativeIsHardwareAvailable(int hardwareType);
    private static native void nativeSetDebugLogging(boolean enable);
}
