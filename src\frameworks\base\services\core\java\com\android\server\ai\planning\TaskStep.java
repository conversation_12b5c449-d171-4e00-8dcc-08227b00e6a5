/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Represents a single step in a task plan
 */
public class TaskStep implements Parcelable {
    public String stepId;
    public String actionType;
    public Bundle parameters;
    public List<String> dependencies;
    public Bundle conditionalExecution;
    public int priority;
    public long timeout;

    public TaskStep() {
        this.stepId = null;
        this.actionType = null;
        this.parameters = new Bundle();
        this.dependencies = new ArrayList<>();
        this.conditionalExecution = new Bundle();
        this.priority = 0;
        this.timeout = 0;
    }

    public TaskStep(String stepId, String actionType) {
        this();
        this.stepId = stepId;
        this.actionType = actionType;
    }

    protected TaskStep(Parcel in) {
        stepId = in.readString();
        actionType = in.readString();
        parameters = in.readBundle(getClass().getClassLoader());
        dependencies = in.createStringArrayList();
        conditionalExecution = in.readBundle(getClass().getClassLoader());
        priority = in.readInt();
        timeout = in.readLong();
    }

    public static final Creator<TaskStep> CREATOR = new Creator<TaskStep>() {
        @Override
        public TaskStep createFromParcel(Parcel in) {
            return new TaskStep(in);
        }

        @Override
        public TaskStep[] newArray(int size) {
            return new TaskStep[size];
        }
    };

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(stepId);
        dest.writeString(actionType);
        dest.writeBundle(parameters);
        dest.writeStringList(dependencies);
        dest.writeBundle(conditionalExecution);
        dest.writeInt(priority);
        dest.writeLong(timeout);
    }

    public void addParameter(String key, String value) {
        if (parameters == null) {
            parameters = new Bundle();
        }
        parameters.putString(key, value);
    }

    public void addDependency(String dependency) {
        if (dependencies == null) {
            dependencies = new ArrayList<>();
        }
        dependencies.add(dependency);
    }
}
