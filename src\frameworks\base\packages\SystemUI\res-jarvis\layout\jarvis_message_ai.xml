<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/message_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="start"
    android:padding="8dp">

    <!-- AI Avatar -->
    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_jarvis_avatar"
        android:layout_marginEnd="12dp"
        android:layout_gravity="top"
        android:contentDescription="@string/jarvis_avatar" />

    <!-- Message Content -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.7"
        android:orientation="vertical">

        <!-- Message Bubble -->
        <LinearLayout
            android:id="@+id/message_bubble"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/jarvis_ai_message_background"
            android:padding="12dp"
            android:layout_marginBottom="4dp">

            <ImageView
                android:id="@+id/type_icon"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginEnd="8dp"
                android:layout_gravity="center_vertical"
                android:visibility="gone" />

            <TextView
                android:id="@+id/message_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="@color/jarvis_ai_message_text"
                android:lineSpacingExtra="2dp" />

        </LinearLayout>

        <!-- Message Info -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/message_timestamp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="10sp"
                android:textColor="@color/jarvis_timestamp_text"
                android:layout_marginEnd="4dp" />

            <ImageView
                android:id="@+id/status_icon"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <!-- Spacer to push message to the left -->
    <View
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_weight="0.3" />

</LinearLayout>
