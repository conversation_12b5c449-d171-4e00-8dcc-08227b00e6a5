<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/message_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="8dp">

    <!-- Message Bubble -->
    <LinearLayout
        android:id="@+id/message_bubble"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@drawable/jarvis_system_message_background"
        android:padding="8dp"
        android:layout_marginBottom="4dp">

        <ImageView
            android:id="@+id/type_icon"
            android:layout_width="14dp"
            android:layout_height="14dp"
            android:layout_marginEnd="6dp"
            android:layout_gravity="center_vertical"
            android:visibility="gone" />

        <TextView
            android:id="@+id/message_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="@color/jarvis_system_message_text"
            android:textStyle="italic"
            android:gravity="center" />

    </LinearLayout>

    <!-- Timestamp -->
    <TextView
        android:id="@+id/message_timestamp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="9sp"
        android:textColor="@color/jarvis_timestamp_text" />

</LinearLayout>
