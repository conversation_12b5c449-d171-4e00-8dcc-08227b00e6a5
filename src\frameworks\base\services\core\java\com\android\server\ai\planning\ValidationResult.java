/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.planning;



import java.util.ArrayList;
import java.util.List;

/**
 * Result of task plan validation
 */
public class ValidationResult {
    public boolean isValid;
    public String errorMessage;
    public List<String> errors;
    public List<String> warnings;
    public List<String> suggestions;

    public long validationTime;

    public ValidationResult() {
        this.isValid = false;
        this.errorMessage = null;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.suggestions = new ArrayList<>();

        this.validationTime = System.currentTimeMillis();
    }

    public ValidationResult(boolean isValid) {
        this();
        this.isValid = isValid;
    }

    public void addError(String error) {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        errors.add(error);
        this.isValid = false;
    }

    public void addWarning(String warning) {
        if (warnings == null) {
            warnings = new ArrayList<>();
        }
        warnings.add(warning);
    }

    public void addSuggestion(String suggestion) {
        if (suggestions == null) {
            suggestions = new ArrayList<>();
        }
        suggestions.add(suggestion);
    }

    public void setError(String errorMessage) {
        this.isValid = false;
        this.errorMessage = errorMessage;
    }

    public void setValid() {
        this.isValid = true;
        this.errorMessage = null;
    }
}
