/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.content;

import android.os.Bundle;

/**
 * Mock implementation of Intent for Jarvis OS compilation.
 * This is a stub implementation for development and testing.
 */
public class Intent {
    // Intent actions
    public static final String ACTION_MAIN = "android.intent.action.MAIN";
    public static final String ACTION_VIEW = "android.intent.action.VIEW";
    public static final String ACTION_SEND = "android.intent.action.SEND";
    public static final String ACTION_SENDTO = "android.intent.action.SENDTO";
    public static final String ACTION_EDIT = "android.intent.action.EDIT";
    public static final String ACTION_PICK = "android.intent.action.PICK";
    public static final String ACTION_CHOOSER = "android.intent.action.CHOOSER";
    public static final String ACTION_GET_CONTENT = "android.intent.action.GET_CONTENT";
    public static final String ACTION_DIAL = "android.intent.action.DIAL";
    public static final String ACTION_CALL = "android.intent.action.CALL";
    public static final String ACTION_SEARCH = "android.intent.action.SEARCH";
    public static final String ACTION_WEB_SEARCH = "android.intent.action.WEB_SEARCH";

    // Intent categories
    public static final String CATEGORY_DEFAULT = "android.intent.category.DEFAULT";
    public static final String CATEGORY_BROWSABLE = "android.intent.category.BROWSABLE";
    public static final String CATEGORY_LAUNCHER = "android.intent.category.LAUNCHER";
    public static final String CATEGORY_HOME = "android.intent.category.HOME";
    public static final String CATEGORY_PREFERENCE = "android.intent.category.PREFERENCE";

    // Intent flags
    public static final int FLAG_ACTIVITY_NEW_TASK = 0x10000000;
    public static final int FLAG_ACTIVITY_CLEAR_TOP = 0x04000000;
    public static final int FLAG_ACTIVITY_SINGLE_TOP = 0x20000000;
    public static final int FLAG_ACTIVITY_CLEAR_TASK = 0x00008000;
    public static final int FLAG_ACTIVITY_NO_HISTORY = 0x40000000;
    public static final int FLAG_ACTIVITY_MULTIPLE_TASK = 0x08000000;

    private String mAction;
    private String mType;
    private ComponentName mComponent;
    private Bundle mExtras;
    private int mFlags;

    public Intent() {
        mExtras = new Bundle();
    }

    public Intent(String action) {
        this();
        mAction = action;
    }

    public Intent(Context packageContext, Class<?> cls) {
        this();
        mComponent = new ComponentName(packageContext, cls);
    }

    public Intent(String action, android.net.Uri data) {
        this(action);
        // Note: Uri is not implemented in this mock
    }

    public Intent(Context packageContext, Class<?> cls, String action) {
        this(packageContext, cls);
        mAction = action;
    }

    public Intent(Intent o) {
        this();
        if (o != null) {
            mAction = o.mAction;
            mType = o.mType;
            mComponent = o.mComponent;
            mFlags = o.mFlags;
            if (o.mExtras != null) {
                mExtras = new Bundle(o.mExtras);
            }
        }
    }

    public String getAction() {
        return mAction;
    }

    public Intent setAction(String action) {
        mAction = action;
        return this;
    }

    public String getType() {
        return mType;
    }

    public Intent setType(String type) {
        mType = type;
        return this;
    }

    public ComponentName getComponent() {
        return mComponent;
    }

    public Intent setComponent(ComponentName component) {
        mComponent = component;
        return this;
    }

    public Intent setClass(Context packageContext, Class<?> cls) {
        mComponent = new ComponentName(packageContext, cls);
        return this;
    }

    public Intent setClassName(Context packageContext, String className) {
        mComponent = new ComponentName(packageContext, className);
        return this;
    }

    public Intent setClassName(String packageName, String className) {
        mComponent = new ComponentName(packageName, className);
        return this;
    }

    public int getFlags() {
        return mFlags;
    }

    public Intent setFlags(int flags) {
        mFlags = flags;
        return this;
    }

    public Intent addFlags(int flags) {
        mFlags |= flags;
        return this;
    }

    public Bundle getExtras() {
        return mExtras;
    }

    public Intent putExtras(Bundle extras) {
        if (extras != null) {
            if (mExtras == null) {
                mExtras = new Bundle();
            }
            mExtras.putAll(extras);
        }
        return this;
    }

    public Intent putExtra(String name, boolean value) {
        if (mExtras == null) mExtras = new Bundle();
        mExtras.putBoolean(name, value);
        return this;
    }

    public Intent putExtra(String name, int value) {
        if (mExtras == null) mExtras = new Bundle();
        mExtras.putInt(name, value);
        return this;
    }

    public Intent putExtra(String name, long value) {
        if (mExtras == null) mExtras = new Bundle();
        mExtras.putLong(name, value);
        return this;
    }

    public Intent putExtra(String name, float value) {
        if (mExtras == null) mExtras = new Bundle();
        mExtras.putFloat(name, value);
        return this;
    }

    public Intent putExtra(String name, String value) {
        if (mExtras == null) mExtras = new Bundle();
        mExtras.putString(name, value);
        return this;
    }

    public boolean getBooleanExtra(String name, boolean defaultValue) {
        return mExtras == null ? defaultValue : mExtras.getBoolean(name, defaultValue);
    }

    public int getIntExtra(String name, int defaultValue) {
        return mExtras == null ? defaultValue : mExtras.getInt(name, defaultValue);
    }

    public long getLongExtra(String name, long defaultValue) {
        return mExtras == null ? defaultValue : mExtras.getLong(name, defaultValue);
    }

    public float getFloatExtra(String name, float defaultValue) {
        return mExtras == null ? defaultValue : mExtras.getFloat(name, defaultValue);
    }

    public String getStringExtra(String name) {
        return mExtras == null ? null : mExtras.getString(name);
    }

    public boolean hasExtra(String name) {
        return mExtras != null && mExtras.containsKey(name);
    }

    public void removeExtra(String name) {
        if (mExtras != null) {
            mExtras.remove(name);
        }
    }

    @Override
    public String toString() {
        StringBuilder b = new StringBuilder(128);
        b.append("Intent { ");
        if (mAction != null) {
            b.append("act=").append(mAction).append(" ");
        }
        if (mComponent != null) {
            b.append("cmp=").append(mComponent.flattenToShortString()).append(" ");
        }
        if (mType != null) {
            b.append("typ=").append(mType).append(" ");
        }
        if (mFlags != 0) {
            b.append("flg=0x").append(Integer.toHexString(mFlags)).append(" ");
        }
        if (mExtras != null) {
            b.append("extras={...} ");
        }
        b.append("}");
        return b.toString();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof Intent) {
            Intent other = (Intent) obj;
            return java.util.Objects.equals(mAction, other.mAction) &&
                   java.util.Objects.equals(mComponent, other.mComponent) &&
                   java.util.Objects.equals(mType, other.mType) &&
                   mFlags == other.mFlags;
        }
        return false;
    }

    @Override
    public int hashCode() {
        int result = mAction != null ? mAction.hashCode() : 0;
        result = 31 * result + (mComponent != null ? mComponent.hashCode() : 0);
        result = 31 * result + (mType != null ? mType.hashCode() : 0);
        result = 31 * result + mFlags;
        return result;
    }
}
