/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.execution.providers;

import android.os.Bundle;
import android.os.RemoteException;

/**
 * Data types and interfaces for action execution in Jarvis OS.
 * 
 * These will eventually be converted to AIDL interfaces for
 * cross-process communication.
 */
public class ActionDataTypes {
    
    /**
     * Request for action execution
     */
    public static class ActionRequest {
        public String action;
        public Bundle parameters;
        public long timeout;
        public String packageName;
        public String taskId;
        public String stepId;
        public long timestamp;
    }
    
    /**
     * Result of action execution
     */
    public static class ActionResult {
        public boolean success;
        public String message;
        public String errorMessage;
        public Bundle results;
        public long executionTime;
        public String actionType;
    }
    
    /**
     * Capability description for an action provider
     */
    public static class ActionCapability {
        public String actionType;
        public String description;
        public String[] requiredPermissions;
        public String[] supportedParameters;
        public boolean requiresUserConfirmation;
        public boolean isSystemAction;
        public String category;
        public int priority;
    }
    
    /**
     * Interface for action providers
     */
    public interface IActionProvider {
        /**
         * Execute the specified action
         */
        ActionResult executeAction(ActionRequest request) throws RemoteException;
        
        /**
         * Check if this provider can execute the action
         */
        boolean canExecuteAction(ActionRequest request) throws RemoteException;
        
        /**
         * Get capability information for this provider
         */
        ActionCapability getCapability() throws RemoteException;
    }
    
    /**
     * System action provider interface
     */
    public interface SystemActionProvider extends IActionProvider {
        /**
         * Initialize the provider with system services
         */
        void initialize() throws RemoteException;
        
        /**
         * Shutdown the provider
         */
        void shutdown() throws RemoteException;
        
        /**
         * Get provider health status
         */
        boolean isHealthy() throws RemoteException;
    }
    
    /**
     * Action execution context
     */
    public static class ActionContext {
        public String packageName;
        public String taskId;
        public String stepId;
        public Bundle contextData;
        public long timestamp;
        public int userId;
    }
    
    /**
     * Action execution metrics
     */
    public static class ActionMetrics {
        public String actionType;
        public long executionTime;
        public boolean success;
        public String errorCode;
        public long timestamp;
        public String packageName;
    }
    
    /**
     * Action provider registration info
     */
    public static class ProviderRegistration {
        public String actionType;
        public String packageName;
        public String className;
        public int priority;
        public boolean isSystemProvider;
        public long registrationTime;
    }
}
