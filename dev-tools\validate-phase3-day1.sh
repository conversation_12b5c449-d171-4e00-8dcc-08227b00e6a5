#!/bin/bash

# Phase 3 Day 1 Validation Script
# Validates the implementation of Gemini API integration and task planning components

echo "🚀 PHASE 3 DAY 1 VALIDATION"
echo "=================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to check if file exists and has content
check_file() {
    local file_path="$1"
    local description="$2"
    local min_lines="${3:-10}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file_path" ]; then
        local line_count=$(wc -l < "$file_path")
        if [ "$line_count" -ge "$min_lines" ]; then
            echo -e "${GRE<PERSON>}✅ $description${NC} ($line_count lines)"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
            return 0
        else
            echo -e "${YELLOW}⚠️  $description${NC} (only $line_count lines, expected $min_lines+)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
            return 1
        fi
    else
        echo -e "${RED}❌ $description${NC} (file not found)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Function to check for specific content in file
check_content() {
    local file_path="$1"
    local search_pattern="$2"
    local description="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file_path" ] && grep -q "$search_pattern" "$file_path"; then
        echo -e "${GREEN}✅ $description${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}❌ $description${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo -e "${BLUE}📋 CHECKING GEMINI API INTEGRATION${NC}"
echo "----------------------------------------"

# Check Gemini API Client
check_file "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiAPIClient.java" \
    "GeminiAPIClient implementation" 300

check_content "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiAPIClient.java" \
    "generateTaskPlan" "GeminiAPIClient has task planning method"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiAPIClient.java" \
    "validateTaskPlan" "GeminiAPIClient has plan validation method"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiAPIClient.java" \
    "makeRequestWithRetries" "GeminiAPIClient has retry logic"

# Check Gemini Request/Response classes
check_file "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiRequest.java" \
    "GeminiRequest class" 20

check_file "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiResponse.java" \
    "GeminiResponse class" 50

check_content "src/frameworks/base/services/core/java/com/android/server/ai/gemini/GeminiResponse.java" \
    "getTaskPlanJson" "GeminiResponse has task plan parsing"

echo ""
echo -e "${BLUE}📋 CHECKING TASK PLANNING ENGINE${NC}"
echo "----------------------------------------"

# Check Task Planner
check_file "src/frameworks/base/services/core/java/com/android/server/ai/planning/TaskPlanner.java" \
    "TaskPlanner implementation" 250

check_content "src/frameworks/base/services/core/java/com/android/server/ai/planning/TaskPlanner.java" \
    "planTask" "TaskPlanner has plan task method"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/planning/TaskPlanner.java" \
    "validateTaskPlan" "TaskPlanner has plan validation method"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/planning/TaskPlanner.java" \
    "parseTaskPlanFromJson" "TaskPlanner has JSON parsing"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/planning/TaskPlanner.java" \
    "buildContextString" "TaskPlanner has context processing"

echo ""
echo -e "${BLUE}📋 CHECKING TASK EXECUTION ENGINE${NC}"
echo "----------------------------------------"

# Check Task Executor
check_file "src/frameworks/base/services/core/java/com/android/server/ai/execution/TaskExecutor.java" \
    "TaskExecutor implementation" 250

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/TaskExecutor.java" \
    "executeTask" "TaskExecutor has execute task method"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/TaskExecutor.java" \
    "executeStepsSequentially" "TaskExecutor has sequential execution"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/TaskExecutor.java" \
    "findExecutableSteps" "TaskExecutor has dependency resolution"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/TaskExecutor.java" \
    "executeActionWithRetry" "TaskExecutor has retry logic"

echo ""
echo -e "${BLUE}📋 CHECKING ACTION REGISTRY ENHANCEMENTS${NC}"
echo "----------------------------------------"

# Check Action Registry enhancements
check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/ActionRegistry.java" \
    "isActionSupported" "ActionRegistry has action support check"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/ActionRegistry.java" \
    "SystemAppActionProvider" "ActionRegistry has system action providers"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/execution/ActionRegistry.java" \
    "registerSystemActionProvider" "ActionRegistry has system provider registration"

echo ""
echo -e "${BLUE}📋 CHECKING SECURITY ENHANCEMENTS${NC}"
echo "----------------------------------------"

# Check Security Manager enhancements
check_content "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java" \
    "hasExecutionPermission" "AiSecurityManager has execution permission check"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java" \
    "getGeminiApiKey" "AiSecurityManager has API key management"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/security/AiSecurityManager.java" \
    "logApiRequest" "AiSecurityManager has API request logging"

# Check Audit Logger enhancements
check_content "src/frameworks/base/services/core/java/com/android/server/ai/security/AiAuditLogger.java" \
    "logApiRequest" "AiAuditLogger has API request logging"

echo ""
echo -e "${BLUE}📋 CHECKING INTEGRATION POINTS${NC}"
echo "----------------------------------------"

# Check AiPlanningOrchestrationService integration
check_content "src/frameworks/base/services/core/java/com/android/server/ai/AiPlanningOrchestrationService.java" \
    "GeminiAPIClient" "AiPlanningOrchestrationService uses GeminiAPIClient"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/AiPlanningOrchestrationService.java" \
    "TaskPlanner" "AiPlanningOrchestrationService uses TaskPlanner"

check_content "src/frameworks/base/services/core/java/com/android/server/ai/AiPlanningOrchestrationService.java" \
    "TaskExecutor" "AiPlanningOrchestrationService uses TaskExecutor"

echo ""
echo -e "${BLUE}📋 CHECKING DOCUMENTATION${NC}"
echo "----------------------------------------"

# Check documentation
check_file "PHASE_3_DAY_1_PROGRESS.md" \
    "Phase 3 Day 1 progress documentation" 100

check_content "PHASE_3_DAY_1_PROGRESS.md" \
    "Gemini API Integration" "Documentation covers Gemini integration"

check_content "PHASE_3_DAY_1_PROGRESS.md" \
    "Task Planning Engine" "Documentation covers task planning"

check_content "PHASE_3_DAY_1_PROGRESS.md" \
    "Task Execution Engine" "Documentation covers task execution"

echo ""
echo "=================================="
echo -e "${BLUE}📊 VALIDATION SUMMARY${NC}"
echo "=================================="

# Calculate success rate
if [ $TOTAL_CHECKS -gt 0 ]; then
    SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
else
    SUCCESS_RATE=0
fi

echo "Total Checks: $TOTAL_CHECKS"
echo -e "Passed: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "Failed: ${RED}$FAILED_CHECKS${NC}"
echo -e "Success Rate: ${GREEN}$SUCCESS_RATE%${NC}"

echo ""

# Overall assessment
if [ $SUCCESS_RATE -ge 90 ]; then
    echo -e "${GREEN}🎉 PHASE 3 DAY 1: EXCEPTIONAL SUCCESS!${NC}"
    echo -e "${GREEN}✅ All core components implemented successfully${NC}"
    echo -e "${GREEN}✅ Ready for Phase 3 Week 2 development${NC}"
elif [ $SUCCESS_RATE -ge 75 ]; then
    echo -e "${YELLOW}⚠️  PHASE 3 DAY 1: GOOD PROGRESS${NC}"
    echo -e "${YELLOW}✅ Most components implemented, minor issues to resolve${NC}"
elif [ $SUCCESS_RATE -ge 50 ]; then
    echo -e "${YELLOW}⚠️  PHASE 3 DAY 1: PARTIAL SUCCESS${NC}"
    echo -e "${YELLOW}⚠️  Some components need attention${NC}"
else
    echo -e "${RED}❌ PHASE 3 DAY 1: NEEDS ATTENTION${NC}"
    echo -e "${RED}❌ Multiple components require implementation${NC}"
fi

echo ""
echo -e "${BLUE}🚀 NEXT STEPS:${NC}"
echo "1. Address any failed validation checks"
echo "2. Create AIDL interface definitions"
echo "3. Implement comprehensive integration tests"
echo "4. Begin Phase 3 Week 2: Advanced Planning Features"

echo ""
echo "Validation completed at $(date)"

# Exit with appropriate code
if [ $SUCCESS_RATE -ge 75 ]; then
    exit 0
else
    exit 1
fi
