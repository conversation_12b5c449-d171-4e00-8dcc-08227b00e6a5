src\frameworks\base\core\java\android\os\UserHandle.java:73: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public static final int USER_OWNER = 0;
                            ^
src\frameworks\base\core\java\android\os\UserHandle.java:80: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public static final UserHandle OWNER = new UserHandle(USER_OWNER);
                                   ^
src\frameworks\base\core\java\android\os\UserHandle.java:269: warning: [dep-ann] deprecated item is not annotated with @Deprecated
    public boolean isOwner() {
                   ^
Note: Some input files use unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.
3 warnings
