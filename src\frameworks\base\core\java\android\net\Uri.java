/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.net;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Immutable URI reference. A URI reference can be absolute or relative.
 */
public abstract class Uri implements Parcelable {
    
    /**
     * The empty URI, equivalent to "".
     */
    public static final Uri EMPTY = new HierarchicalUri(null, null, null, null, null);

    private Uri() {}

    /**
     * Creates a Uri which parses the given encoded URI string.
     */
    public static Uri parse(String uriString) {
        if (uriString == null) {
            throw new NullPointerException("uriString");
        }
        
        if (uriString.isEmpty()) {
            return EMPTY;
        }
        
        // Simple implementation - just store the string
        return new StringUri(uriString);
    }

    /**
     * Gets the scheme of this URI.
     */
    public abstract String getScheme();

    /**
     * Gets the authority part of this URI.
     */
    public abstract String getAuthority();

    /**
     * Gets the path part of this URI.
     */
    public abstract String getPath();

    /**
     * Gets the query part of this URI.
     */
    public abstract String getQuery();

    /**
     * Gets the fragment part of this URI.
     */
    public abstract String getFragment();

    /**
     * Returns the encoded string representation of this URI.
     */
    public abstract String toString();

    // Simple implementation for basic URI functionality
    private static class StringUri extends Uri {
        private final String uriString;

        StringUri(String uriString) {
            this.uriString = uriString;
        }

        @Override
        public String getScheme() {
            int colonIndex = uriString.indexOf(':');
            return colonIndex > 0 ? uriString.substring(0, colonIndex) : null;
        }

        @Override
        public String getAuthority() {
            // Simplified implementation
            return null;
        }

        @Override
        public String getPath() {
            // Simplified implementation
            return uriString;
        }

        @Override
        public String getQuery() {
            int queryIndex = uriString.indexOf('?');
            if (queryIndex >= 0 && queryIndex < uriString.length() - 1) {
                String queryPart = uriString.substring(queryIndex + 1);
                int fragmentIndex = queryPart.indexOf('#');
                return fragmentIndex >= 0 ? queryPart.substring(0, fragmentIndex) : queryPart;
            }
            return null;
        }

        @Override
        public String getFragment() {
            int fragmentIndex = uriString.indexOf('#');
            return fragmentIndex >= 0 && fragmentIndex < uriString.length() - 1 
                ? uriString.substring(fragmentIndex + 1) : null;
        }

        @Override
        public String toString() {
            return uriString;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(uriString);
        }
    }

    private static class HierarchicalUri extends Uri {
        private final String scheme;
        private final String authority;
        private final String path;
        private final String query;
        private final String fragment;

        HierarchicalUri(String scheme, String authority, String path, String query, String fragment) {
            this.scheme = scheme;
            this.authority = authority;
            this.path = path;
            this.query = query;
            this.fragment = fragment;
        }

        @Override
        public String getScheme() {
            return scheme;
        }

        @Override
        public String getAuthority() {
            return authority;
        }

        @Override
        public String getPath() {
            return path;
        }

        @Override
        public String getQuery() {
            return query;
        }

        @Override
        public String getFragment() {
            return fragment;
        }

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            if (scheme != null) {
                sb.append(scheme).append(':');
            }
            if (authority != null) {
                sb.append("//").append(authority);
            }
            if (path != null) {
                sb.append(path);
            }
            if (query != null) {
                sb.append('?').append(query);
            }
            if (fragment != null) {
                sb.append('#').append(fragment);
            }
            return sb.toString();
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(toString());
        }
    }

    public static final Creator<Uri> CREATOR = new Creator<Uri>() {
        @Override
        public Uri createFromParcel(Parcel in) {
            String uriString = in.readString();
            return uriString != null ? parse(uriString) : EMPTY;
        }

        @Override
        public Uri[] newArray(int size) {
            return new Uri[size];
        }
    };
}
