<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<androidx.coordinatorlayout.widget.CoordinatorLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/jarvis_background_primary">

    <!-- Animated Background -->
    <FrameLayout
        android:id="@+id/animated_background"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        
        <!-- Particle System Container -->
        <View
            android:id="@+id/particle_system"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0.3" />
            
    </FrameLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/jarvis_spacing_md">

            <!-- Header Section -->
            <LinearLayout
                android:id="@+id/header_section"
                android:layout_width="match_parent"
                android:layout_height="@dimen/jarvis_home_header_height"
                android:orientation="vertical"
                android:gravity="center"
                android:background="@drawable/jarvis_gradient_background"
                android:layout_marginBottom="@dimen/jarvis_spacing_lg"
                android:elevation="@dimen/jarvis_elevation_md"
                android:padding="@dimen/jarvis_spacing_lg">

                <!-- AI Status Indicator -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/jarvis_spacing_sm">

                    <ImageView
                        android:id="@+id/ai_status_indicator"
                        android:layout_width="@dimen/jarvis_icon_size_sm"
                        android:layout_height="@dimen/jarvis_icon_size_sm"
                        android:src="@drawable/ic_jarvis_active"
                        android:layout_marginEnd="@dimen/jarvis_spacing_xs"
                        android:contentDescription="@string/ai_status" />

                    <TextView
                        android:id="@+id/ai_status_text"
                        style="@style/JarvisText.Caption"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/jarvis_ready"
                        android:textColor="@color/jarvis_text_inverse" />

                </LinearLayout>

                <!-- Welcome Message -->
                <TextView
                    android:id="@+id/welcome_message"
                    style="@style/JarvisText.Headline2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/jarvis_welcome"
                    android:textColor="@color/jarvis_text_inverse"
                    android:gravity="center" />

            </LinearLayout>

            <!-- Quick Actions Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_lg">

                <TextView
                    style="@style/JarvisText.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/quick_actions"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md" />

                <GridLayout
                    android:id="@+id/quick_actions_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:columnCount="2"
                    android:rowCount="2"
                    android:alignmentMode="alignBounds"
                    android:useDefaultMargins="true">

                    <!-- Voice Assistant -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/jarvis_home_widget_height"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/voice_assistant_card">

                        <ImageView
                            android:layout_width="@dimen/jarvis_home_quick_action_size"
                            android:layout_height="@dimen/jarvis_home_quick_action_size"
                            android:src="@drawable/ic_voice_assistant"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/voice_assistant" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/voice_assistant"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Smart Controls -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/jarvis_home_widget_height"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/smart_controls_card">

                        <ImageView
                            android:layout_width="@dimen/jarvis_home_quick_action_size"
                            android:layout_height="@dimen/jarvis_home_quick_action_size"
                            android:src="@drawable/ic_smart_controls"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/smart_controls" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/smart_controls"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- AI Suggestions -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/jarvis_home_widget_height"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/ai_suggestions_card">

                        <ImageView
                            android:layout_width="@dimen/jarvis_home_quick_action_size"
                            android:layout_height="@dimen/jarvis_home_quick_action_size"
                            android:src="@drawable/ic_ai_suggestions"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/ai_suggestions" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/ai_suggestions"
                            android:gravity="center" />

                    </LinearLayout>

                    <!-- Settings -->
                    <LinearLayout
                        style="@style/JarvisCard.Interactive"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/jarvis_home_widget_height"
                        android:layout_columnWeight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:id="@+id/settings_card">

                        <ImageView
                            android:layout_width="@dimen/jarvis_home_quick_action_size"
                            android:layout_height="@dimen/jarvis_home_quick_action_size"
                            android:src="@drawable/ic_settings"
                            android:layout_marginBottom="@dimen/jarvis_spacing_sm"
                            android:contentDescription="@string/settings" />

                        <TextView
                            style="@style/JarvisText.Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/settings"
                            android:gravity="center" />

                    </LinearLayout>

                </GridLayout>

            </LinearLayout>

            <!-- AI Insights Widget -->
            <LinearLayout
                style="@style/JarvisCard.Elevated"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_lg"
                android:id="@+id/ai_insights_widget">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md">

                    <ImageView
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_ai_insights"
                        android:layout_marginEnd="@dimen/jarvis_spacing_sm"
                        android:contentDescription="@string/ai_insights" />

                    <TextView
                        style="@style/JarvisText.Headline3"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/ai_insights" />

                    <ImageButton
                        android:id="@+id/insights_expand_button"
                        android:layout_width="@dimen/jarvis_icon_size_md"
                        android:layout_height="@dimen/jarvis_icon_size_md"
                        android:src="@drawable/ic_expand_more"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:contentDescription="@string/expand_insights" />

                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/insights_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

            <!-- Recent Activity -->
            <LinearLayout
                style="@style/JarvisCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="@dimen/jarvis_spacing_xxl">

                <TextView
                    style="@style/JarvisText.Headline3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/recent_activity"
                    android:layout_marginBottom="@dimen/jarvis_spacing_md" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recent_activity_recycler_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/voice_fab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="@dimen/jarvis_spacing_md"
        android:src="@drawable/ic_mic"
        app:backgroundTint="@color/jarvis_primary"
        app:tint="@color/jarvis_text_inverse"
        app:elevation="@dimen/jarvis_elevation_lg"
        android:contentDescription="@string/voice_activation" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
