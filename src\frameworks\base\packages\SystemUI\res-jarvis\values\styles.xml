<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>
    <!-- Base Jarvis Theme -->
    <style name="JarvisTheme" parent="@android:style/Theme.DeviceDefault">
        <item name="android:colorPrimary">@color/jarvis_primary</item>
        <item name="android:colorPrimaryDark">@color/jarvis_primary_variant</item>
        <item name="android:colorAccent">@color/jarvis_secondary</item>
        <item name="android:windowBackground">@color/jarvis_background_primary</item>
        <item name="android:textColorPrimary">@color/jarvis_text_primary</item>
        <item name="android:textColorSecondary">@color/jarvis_text_secondary</item>
    </style>

    <!-- Typography Styles -->
    <style name="JarvisText">
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textColor">@color/jarvis_text_primary</item>
    </style>

    <style name="JarvisText.Headline1" parent="JarvisText">
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.02</item>
    </style>

    <style name="JarvisText.Headline2" parent="JarvisText">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">-0.01</item>
    </style>

    <style name="JarvisText.Headline3" parent="JarvisText">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="JarvisText.Body1" parent="JarvisText">
        <item name="android:textSize">16sp</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="JarvisText.Body2" parent="JarvisText">
        <item name="android:textSize">14sp</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="JarvisText.Caption" parent="JarvisText">
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/jarvis_text_secondary</item>
    </style>

    <!-- Card Styles -->
    <style name="JarvisCard">
        <item name="android:background">@drawable/jarvis_card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
    </style>

    <style name="JarvisCard.Elevated">
        <item name="android:elevation">8dp</item>
    </style>

    <style name="JarvisCard.Interactive">
        <item name="android:foreground">@drawable/jarvis_card_ripple</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <!-- Button Styles -->
    <style name="JarvisButton" parent="@android:style/Widget.Material.Button">
        <item name="android:background">@drawable/jarvis_button_background</item>
        <item name="android:textColor">@color/jarvis_text_inverse</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:elevation">2dp</item>
    </style>

    <style name="JarvisButton.Secondary">
        <item name="android:background">@drawable/jarvis_button_secondary_background</item>
        <item name="android:textColor">@color/jarvis_primary</item>
    </style>

    <style name="JarvisButton.Outlined">
        <item name="android:background">@drawable/jarvis_button_outlined_background</item>
        <item name="android:textColor">@color/jarvis_primary</item>
    </style>

    <style name="JarvisButton.Text">
        <item name="android:background">@drawable/jarvis_button_text_background</item>
        <item name="android:textColor">@color/jarvis_primary</item>
        <item name="android:elevation">0dp</item>
    </style>

    <!-- Navigation Styles -->
    <style name="JarvisNavigation">
        <item name="android:background">@drawable/jarvis_navigation_background</item>
        <item name="android:elevation">8dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>

    <style name="JarvisNavigation.Item">
        <item name="android:background">@drawable/jarvis_navigation_item_background</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">4dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <!-- Status Bar Styles -->
    <style name="JarvisStatusBar">
        <item name="android:background">@color/jarvis_status_background</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>

    <!-- Notification Styles -->
    <style name="JarvisNotification">
        <item name="android:background">@drawable/jarvis_notification_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
    </style>

    <style name="JarvisNotification.HighPriority">
        <item name="android:background">@drawable/jarvis_notification_high_priority_background</item>
        <item name="android:elevation">4dp</item>
    </style>

    <!-- Input Styles -->
    <style name="JarvisInput" parent="@android:style/Widget.Material.EditText">
        <item name="android:background">@drawable/jarvis_input_background</item>
        <item name="android:textColor">@color/jarvis_text_primary</item>
        <item name="android:textColorHint">@color/jarvis_text_tertiary</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!-- Overlay Styles -->
    <style name="JarvisOverlay">
        <item name="android:background">@color/jarvis_overlay_background</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <!-- Animation Styles -->
    <style name="JarvisAnimation">
        <item name="android:duration">300</item>
        <item name="android:interpolator">@android:anim/accelerate_decelerate_interpolator</item>
    </style>

    <style name="JarvisAnimation.Fast">
        <item name="android:duration">150</item>
    </style>

    <style name="JarvisAnimation.Slow">
        <item name="android:duration">500</item>
    </style>
</resources>
