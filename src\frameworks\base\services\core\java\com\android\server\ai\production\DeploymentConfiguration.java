/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

/**
 * Configuration for production deployment operations
 */
public class DeploymentConfiguration implements Parcelable {
    private String mDeploymentId;
    private String mVersion;
    private String mEnvironment;
    private Bundle mParameters;
    private List<String> mTargetServices;
    private boolean mZeroDowntime;
    private boolean mRollbackEnabled;
    private long mTimeoutMs;
    private int mMaxRetries;
    
    public DeploymentConfiguration() {
        mParameters = new Bundle();
        mTargetServices = new ArrayList<>();
        mZeroDowntime = true;
        mRollbackEnabled = true;
        mTimeoutMs = 10 * 60 * 1000; // 10 minutes
        mMaxRetries = 3;
    }
    
    public DeploymentConfiguration(Parcel in) {
        mDeploymentId = in.readString();
        mVersion = in.readString();
        mEnvironment = in.readString();
        mParameters = in.readBundle(getClass().getClassLoader());
        mTargetServices = new ArrayList<>();
        in.readStringList(mTargetServices);
        mZeroDowntime = in.readBoolean();
        mRollbackEnabled = in.readBoolean();
        mTimeoutMs = in.readLong();
        mMaxRetries = in.readInt();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mDeploymentId);
        dest.writeString(mVersion);
        dest.writeString(mEnvironment);
        dest.writeBundle(mParameters);
        dest.writeStringList(mTargetServices);
        dest.writeBoolean(mZeroDowntime);
        dest.writeBoolean(mRollbackEnabled);
        dest.writeLong(mTimeoutMs);
        dest.writeInt(mMaxRetries);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<DeploymentConfiguration> CREATOR = new Creator<DeploymentConfiguration>() {
        @Override
        public DeploymentConfiguration createFromParcel(Parcel in) {
            return new DeploymentConfiguration(in);
        }
        
        @Override
        public DeploymentConfiguration[] newArray(int size) {
            return new DeploymentConfiguration[size];
        }
    };
    
    // Getters and setters
    public String getDeploymentId() { return mDeploymentId; }
    public void setDeploymentId(String deploymentId) { mDeploymentId = deploymentId; }
    
    public String getVersion() { return mVersion; }
    public void setVersion(String version) { mVersion = version; }
    
    public String getEnvironment() { return mEnvironment; }
    public void setEnvironment(String environment) { mEnvironment = environment; }
    
    public Bundle getParameters() { return mParameters; }
    public void setParameters(Bundle parameters) { mParameters = parameters; }
    
    public List<String> getTargetServices() { return mTargetServices; }
    public void setTargetServices(List<String> targetServices) { mTargetServices = targetServices; }
    
    public boolean isZeroDowntime() { return mZeroDowntime; }
    public void setZeroDowntime(boolean zeroDowntime) { mZeroDowntime = zeroDowntime; }
    
    public boolean isRollbackEnabled() { return mRollbackEnabled; }
    public void setRollbackEnabled(boolean rollbackEnabled) { mRollbackEnabled = rollbackEnabled; }
    
    public long getTimeoutMs() { return mTimeoutMs; }
    public void setTimeoutMs(long timeoutMs) { mTimeoutMs = timeoutMs; }
    
    public int getMaxRetries() { return mMaxRetries; }
    public void setMaxRetries(int maxRetries) { mMaxRetries = maxRetries; }
}
