/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.Context;
import android.graphics.Typeface;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.systemui.R;

import java.util.List;

/**
 * RecyclerView adapter for displaying conversation messages in the Jarvis interface.
 * 
 * Handles different message types (user, AI, system) with appropriate styling
 * and layout positioning.
 */
public class ConversationAdapter extends RecyclerView.Adapter<ConversationAdapter.MessageViewHolder> {
    
    private static final int VIEW_TYPE_USER_MESSAGE = 1;
    private static final int VIEW_TYPE_AI_MESSAGE = 2;
    private static final int VIEW_TYPE_SYSTEM_MESSAGE = 3;
    private static final int VIEW_TYPE_ACTION_MESSAGE = 4;
    
    private List<ConversationMessage> mMessages;
    private LayoutInflater mInflater;
    
    public ConversationAdapter(List<ConversationMessage> messages) {
        mMessages = messages;
    }
    
    @Override
    public void onAttachedToRecyclerView(@NonNull RecyclerView recyclerView) {
        super.onAttachedToRecyclerView(recyclerView);
        mInflater = LayoutInflater.from(recyclerView.getContext());
    }
    
    @Override
    public int getItemViewType(int position) {
        ConversationMessage message = mMessages.get(position);
        
        if (message.isSystemMessage()) {
            return VIEW_TYPE_SYSTEM_MESSAGE;
        } else if (message.isActionMessage()) {
            return VIEW_TYPE_ACTION_MESSAGE;
        } else if (message.isUser()) {
            return VIEW_TYPE_USER_MESSAGE;
        } else {
            return VIEW_TYPE_AI_MESSAGE;
        }
    }
    
    @NonNull
    @Override
    public MessageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view;
        
        switch (viewType) {
            case VIEW_TYPE_USER_MESSAGE:
                view = mInflater.inflate(R.layout.jarvis_message_user, parent, false);
                break;
            case VIEW_TYPE_AI_MESSAGE:
                view = mInflater.inflate(R.layout.jarvis_message_ai, parent, false);
                break;
            case VIEW_TYPE_SYSTEM_MESSAGE:
                view = mInflater.inflate(R.layout.jarvis_message_system, parent, false);
                break;
            case VIEW_TYPE_ACTION_MESSAGE:
                view = mInflater.inflate(R.layout.jarvis_message_action, parent, false);
                break;
            default:
                view = mInflater.inflate(R.layout.jarvis_message_ai, parent, false);
                break;
        }
        
        return new MessageViewHolder(view, viewType);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MessageViewHolder holder, int position) {
        ConversationMessage message = mMessages.get(position);
        holder.bind(message);
    }
    
    @Override
    public int getItemCount() {
        return mMessages.size();
    }
    
    public static class MessageViewHolder extends RecyclerView.ViewHolder {
        private TextView mMessageText;
        private TextView mTimestamp;
        private ImageView mStatusIcon;
        private ImageView mTypeIcon;
        private View mMessageBubble;
        private LinearLayout mMessageContainer;
        private int mViewType;
        
        public MessageViewHolder(@NonNull View itemView, int viewType) {
            super(itemView);
            mViewType = viewType;
            
            mMessageText = itemView.findViewById(R.id.message_text);
            mTimestamp = itemView.findViewById(R.id.message_timestamp);
            mStatusIcon = itemView.findViewById(R.id.status_icon);
            mTypeIcon = itemView.findViewById(R.id.type_icon);
            mMessageBubble = itemView.findViewById(R.id.message_bubble);
            mMessageContainer = itemView.findViewById(R.id.message_container);
        }
        
        public void bind(ConversationMessage message) {
            // Set message content
            mMessageText.setText(message.getContent());
            
            // Set timestamp
            if (mTimestamp != null) {
                mTimestamp.setText(message.getFormattedTimestamp());
            }
            
            // Configure based on message type and sender
            configureMessageAppearance(message);
            
            // Set status icon
            configureStatusIcon(message);
            
            // Set type icon
            configureTypeIcon(message);
            
            // Handle important messages
            if (message.isImportant()) {
                mMessageText.setTypeface(mMessageText.getTypeface(), Typeface.BOLD);
            }
        }
        
        private void configureMessageAppearance(ConversationMessage message) {
            Context context = itemView.getContext();
            
            switch (mViewType) {
                case VIEW_TYPE_USER_MESSAGE:
                    configureUserMessage(context, message);
                    break;
                case VIEW_TYPE_AI_MESSAGE:
                    configureAiMessage(context, message);
                    break;
                case VIEW_TYPE_SYSTEM_MESSAGE:
                    configureSystemMessage(context, message);
                    break;
                case VIEW_TYPE_ACTION_MESSAGE:
                    configureActionMessage(context, message);
                    break;
            }
        }
        
        private void configureUserMessage(Context context, ConversationMessage message) {
            // User messages align to the right
            if (mMessageContainer != null) {
                mMessageContainer.setGravity(Gravity.END);
            }
            
            // Set user message background
            if (mMessageBubble != null) {
                mMessageBubble.setBackground(context.getDrawable(R.drawable.jarvis_user_message_background));
            }
            
            // Set text color for user messages
            mMessageText.setTextColor(context.getColor(R.color.jarvis_user_message_text));
        }
        
        private void configureAiMessage(Context context, ConversationMessage message) {
            // AI messages align to the left
            if (mMessageContainer != null) {
                mMessageContainer.setGravity(Gravity.START);
            }
            
            // Set AI message background
            if (mMessageBubble != null) {
                mMessageBubble.setBackground(context.getDrawable(R.drawable.jarvis_ai_message_background));
            }
            
            // Set text color for AI messages
            mMessageText.setTextColor(context.getColor(R.color.jarvis_ai_message_text));
        }
        
        private void configureSystemMessage(Context context, ConversationMessage message) {
            // System messages center-aligned
            if (mMessageContainer != null) {
                mMessageContainer.setGravity(Gravity.CENTER);
            }
            
            // Set system message background
            if (mMessageBubble != null) {
                mMessageBubble.setBackground(context.getDrawable(R.drawable.jarvis_system_message_background));
            }
            
            // Set text color for system messages
            mMessageText.setTextColor(context.getColor(R.color.jarvis_system_message_text));
            mMessageText.setTextSize(12); // Smaller text for system messages
        }
        
        private void configureActionMessage(Context context, ConversationMessage message) {
            // Action messages similar to AI messages but with special styling
            configureAiMessage(context, message);
            
            // Add action-specific styling
            if (mMessageBubble != null) {
                mMessageBubble.setBackground(context.getDrawable(R.drawable.jarvis_action_message_background));
            }
        }
        
        private void configureStatusIcon(ConversationMessage message) {
            if (mStatusIcon == null) return;
            
            Context context = itemView.getContext();
            
            switch (message.getStatus()) {
                case SENT:
                    mStatusIcon.setImageDrawable(context.getDrawable(R.drawable.ic_message_sent));
                    mStatusIcon.setVisibility(View.VISIBLE);
                    break;
                case DELIVERED:
                    mStatusIcon.setImageDrawable(context.getDrawable(R.drawable.ic_message_delivered));
                    mStatusIcon.setVisibility(View.VISIBLE);
                    break;
                case PROCESSING:
                    mStatusIcon.setImageDrawable(context.getDrawable(R.drawable.ic_message_processing));
                    mStatusIcon.setVisibility(View.VISIBLE);
                    break;
                case COMPLETED:
                    mStatusIcon.setImageDrawable(context.getDrawable(R.drawable.ic_message_completed));
                    mStatusIcon.setVisibility(View.VISIBLE);
                    break;
                case FAILED:
                    mStatusIcon.setImageDrawable(context.getDrawable(R.drawable.ic_message_failed));
                    mStatusIcon.setVisibility(View.VISIBLE);
                    break;
                default:
                    mStatusIcon.setVisibility(View.GONE);
                    break;
            }
        }
        
        private void configureTypeIcon(ConversationMessage message) {
            if (mTypeIcon == null) return;
            
            Context context = itemView.getContext();
            
            switch (message.getType()) {
                case VOICE:
                    mTypeIcon.setImageDrawable(context.getDrawable(R.drawable.ic_voice_message));
                    mTypeIcon.setVisibility(View.VISIBLE);
                    break;
                case ACTION_RESULT:
                    mTypeIcon.setImageDrawable(context.getDrawable(R.drawable.ic_action_result));
                    mTypeIcon.setVisibility(View.VISIBLE);
                    break;
                case SYSTEM_INFO:
                    mTypeIcon.setImageDrawable(context.getDrawable(R.drawable.ic_system_info));
                    mTypeIcon.setVisibility(View.VISIBLE);
                    break;
                case ERROR:
                    mTypeIcon.setImageDrawable(context.getDrawable(R.drawable.ic_error));
                    mTypeIcon.setVisibility(View.VISIBLE);
                    break;
                case TEXT:
                default:
                    mTypeIcon.setVisibility(View.GONE);
                    break;
            }
        }
    }
    
    // Helper methods for adapter management
    public void addMessage(ConversationMessage message) {
        mMessages.add(message);
        notifyItemInserted(mMessages.size() - 1);
    }
    
    public void updateMessage(int position, ConversationMessage message) {
        if (position >= 0 && position < mMessages.size()) {
            mMessages.set(position, message);
            notifyItemChanged(position);
        }
    }
    
    public void removeMessage(int position) {
        if (position >= 0 && position < mMessages.size()) {
            mMessages.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    public void clearMessages() {
        int size = mMessages.size();
        mMessages.clear();
        notifyItemRangeRemoved(0, size);
    }
    
    public ConversationMessage getMessage(int position) {
        if (position >= 0 && position < mMessages.size()) {
            return mMessages.get(position);
        }
        return null;
    }
    
    public int findMessageById(String messageId) {
        for (int i = 0; i < mMessages.size(); i++) {
            if (mMessages.get(i).getMessageId().equals(messageId)) {
                return i;
            }
        }
        return -1;
    }
}
