/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.RemoteException;

import java.util.List;

/**
 * Interface for AI Context Engine service
 */
public interface IAiContextEngine extends IInterface {
    
    ContextSnapshot getCurrentContext() throws RemoteException;
    
    void registerContextListener(IContextListener listener) throws RemoteException;
    
    void unregisterContextListener(IContextListener listener) throws RemoteException;
    
    boolean requestContextPermission(String contextType, String callingPackage) throws RemoteException;
    
    List<String> getAvailableContextTypes() throws RemoteException;
    
    List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException;
    
    void setContextCollectionEnabled(String contextType, boolean enabled) throws RemoteException;

    /**
     * Local-side IPC implementation stub class.
     */
    public static abstract class Stub extends Binder implements IAiContextEngine {
        private static final String DESCRIPTOR = "android.ai.IAiContextEngine";

        public Stub() {
            this.attachInterface(this, DESCRIPTOR);
        }

        public static IAiContextEngine asInterface(IBinder obj) {
            if (obj == null) {
                return null;
            }
            IInterface iin = obj.queryLocalInterface(DESCRIPTOR);
            if (iin != null && iin instanceof IAiContextEngine) {
                return (IAiContextEngine) iin;
            }
            return new Proxy(obj);
        }

        @Override
        public IBinder asBinder() {
            return this;
        }

        private static class Proxy implements IAiContextEngine {
            private IBinder mRemote;

            Proxy(IBinder remote) {
                mRemote = remote;
            }

            @Override
            public IBinder asBinder() {
                return mRemote;
            }

            @Override
            public ContextSnapshot getCurrentContext() throws RemoteException {
                // Implementation would be generated by AIDL
                return null;
            }

            @Override
            public void registerContextListener(IContextListener listener) throws RemoteException {
                // Implementation would be generated by AIDL
            }

            @Override
            public void unregisterContextListener(IContextListener listener) throws RemoteException {
                // Implementation would be generated by AIDL
            }

            @Override
            public boolean requestContextPermission(String contextType, String callingPackage) throws RemoteException {
                // Implementation would be generated by AIDL
                return false;
            }

            @Override
            public List<String> getAvailableContextTypes() throws RemoteException {
                // Implementation would be generated by AIDL
                return null;
            }

            @Override
            public List<ContextSnapshot> getHistoricalContext(long startTime, long endTime) throws RemoteException {
                // Implementation would be generated by AIDL
                return null;
            }

            @Override
            public void setContextCollectionEnabled(String contextType, boolean enabled) throws RemoteException {
                // Implementation would be generated by AIDL
            }
        }
    }
}
