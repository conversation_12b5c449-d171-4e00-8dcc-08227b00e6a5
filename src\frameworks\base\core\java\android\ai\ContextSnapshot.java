/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package android.ai;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

/**
 * Represents a snapshot of the current system context at a specific point in time.
 * Contains information about app states, user interactions, sensor data, and other
 * contextual information that AI services can use for decision making.
 */
public class ContextSnapshot implements Parcelable {
    public long timestamp;
    public String currentApp;
    public String currentActivity;
    public Bundle appStates;
    public Bundle sensorData;
    public Bundle notificationData;
    public Bundle userInteractionData;
    public Bundle environmentalData;
    public Bundle calendarData;
    public Bundle communicationData;
    public int privacyLevel;
    
    public ContextSnapshot() {
        timestamp = System.currentTimeMillis();
        appStates = new Bundle();
        sensorData = new Bundle();
        notificationData = new Bundle();
        userInteractionData = new Bundle();
        environmentalData = new Bundle();
        calendarData = new Bundle();
        communicationData = new Bundle();
        privacyLevel = 0; // Default to public level
    }
    
    public ContextSnapshot(Parcel in) {
        timestamp = in.readLong();
        currentApp = in.readString();
        currentActivity = in.readString();
        appStates = in.readBundle(getClass().getClassLoader());
        sensorData = in.readBundle(getClass().getClassLoader());
        notificationData = in.readBundle(getClass().getClassLoader());
        userInteractionData = in.readBundle(getClass().getClassLoader());
        environmentalData = in.readBundle(getClass().getClassLoader());
        calendarData = in.readBundle(getClass().getClassLoader());
        communicationData = in.readBundle(getClass().getClassLoader());
        privacyLevel = in.readInt();
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(timestamp);
        dest.writeString(currentApp);
        dest.writeString(currentActivity);
        dest.writeBundle(appStates);
        dest.writeBundle(sensorData);
        dest.writeBundle(notificationData);
        dest.writeBundle(userInteractionData);
        dest.writeBundle(environmentalData);
        dest.writeBundle(calendarData);
        dest.writeBundle(communicationData);
        dest.writeInt(privacyLevel);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<ContextSnapshot> CREATOR = new Creator<ContextSnapshot>() {
        @Override
        public ContextSnapshot createFromParcel(Parcel in) {
            return new ContextSnapshot(in);
        }
        
        @Override
        public ContextSnapshot[] newArray(int size) {
            return new ContextSnapshot[size];
        }
    };
    
    /**
     * Get the age of this context snapshot in milliseconds
     */
    public long getAge() {
        return System.currentTimeMillis() - timestamp;
    }
    
    /**
     * Check if this context snapshot is still fresh (less than specified age)
     */
    public boolean isFresh(long maxAgeMs) {
        return getAge() < maxAgeMs;
    }
    
    /**
     * Create a copy of this context snapshot
     */
    public ContextSnapshot copy() {
        ContextSnapshot copy = new ContextSnapshot();
        copy.timestamp = this.timestamp;
        copy.currentApp = this.currentApp;
        copy.currentActivity = this.currentActivity;
        copy.appStates = new Bundle(this.appStates);
        copy.sensorData = new Bundle(this.sensorData);
        copy.notificationData = new Bundle(this.notificationData);
        copy.userInteractionData = new Bundle(this.userInteractionData);
        copy.environmentalData = new Bundle(this.environmentalData);
        copy.calendarData = new Bundle(this.calendarData);
        copy.communicationData = new Bundle(this.communicationData);
        copy.privacyLevel = this.privacyLevel;
        return copy;
    }
    
    /**
     * Convert to Bundle for serialization
     */
    public Bundle toBundle() {
        Bundle bundle = new Bundle();
        bundle.putLong("timestamp", timestamp);
        bundle.putString("currentApp", currentApp);
        bundle.putString("currentActivity", currentActivity);
        bundle.putBundle("appStates", appStates);
        bundle.putBundle("sensorData", sensorData);
        bundle.putBundle("notificationData", notificationData);
        bundle.putBundle("userInteractionData", userInteractionData);
        bundle.putBundle("environmentalData", environmentalData);
        bundle.putBundle("calendarData", calendarData);
        bundle.putBundle("communicationData", communicationData);
        bundle.putInt("privacyLevel", privacyLevel);
        return bundle;
    }
    
    /**
     * Create from Bundle
     */
    public static ContextSnapshot fromBundle(Bundle bundle) {
        ContextSnapshot snapshot = new ContextSnapshot();
        snapshot.timestamp = bundle.getLong("timestamp", System.currentTimeMillis());
        snapshot.currentApp = bundle.getString("currentApp");
        snapshot.currentActivity = bundle.getString("currentActivity");
        snapshot.appStates = bundle.getBundle("appStates");
        snapshot.sensorData = bundle.getBundle("sensorData");
        snapshot.notificationData = bundle.getBundle("notificationData");
        snapshot.userInteractionData = bundle.getBundle("userInteractionData");
        snapshot.environmentalData = bundle.getBundle("environmentalData");
        snapshot.calendarData = bundle.getBundle("calendarData");
        snapshot.communicationData = bundle.getBundle("communicationData");
        snapshot.privacyLevel = bundle.getInt("privacyLevel", 0);
        return snapshot;
    }
    
    @Override
    public String toString() {
        return "ContextSnapshot{" +
                "timestamp=" + timestamp +
                ", currentApp='" + currentApp + '\'' +
                ", currentActivity='" + currentActivity + '\'' +
                ", privacyLevel=" + privacyLevel +
                ", age=" + getAge() + "ms" +
                '}';
    }
}
