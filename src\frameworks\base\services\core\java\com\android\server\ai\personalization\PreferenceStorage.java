/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.personalization;

import android.content.Context;
import android.os.Bundle;
import android.util.Slog;

import com.android.server.ai.security.AiSecurityManager;

import java.util.HashMap;
import java.util.Map;

/**
 * Secure storage for user preferences in AI personalization
 */
public class PreferenceStorage {
    private static final String TAG = "PreferenceStorage";
    private static final boolean DEBUG = true;

    private final Context mContext;
    private final AiSecurityManager mSecurityManager;
    private final Map<String, Map<String, Bundle>> mPreferences = new HashMap<>();

    public PreferenceStorage(Context context, AiSecurityManager securityManager) {
        mContext = context;
        mSecurityManager = securityManager;
    }

    public void updatePreference(String packageName, String key, Bundle value) {
        if (DEBUG) Slog.d(TAG, "Updating preference " + key + " for package: " + packageName);
        
        Map<String, Bundle> packagePrefs = mPreferences.computeIfAbsent(packageName, k -> new HashMap<>());
        packagePrefs.put(key, value);
    }

    public Bundle getPreference(String packageName, String key) {
        Map<String, Bundle> packagePrefs = mPreferences.get(packageName);
        if (packagePrefs != null) {
            return packagePrefs.get(key);
        }
        return null;
    }

    public void resetPreferences(String packageName) {
        if (DEBUG) Slog.d(TAG, "Resetting preferences for package: " + packageName);
        mPreferences.remove(packageName);
    }

    public Bundle exportPreferences(String packageName) {
        Bundle exportData = new Bundle();
        Map<String, Bundle> packagePrefs = mPreferences.get(packageName);

        if (packagePrefs != null) {
            for (Map.Entry<String, Bundle> entry : packagePrefs.entrySet()) {
                exportData.putBundle(entry.getKey(), entry.getValue());
            }
        }

        exportData.putLong("export_time", System.currentTimeMillis());
        return exportData;
    }

    public void importPreferences(String packageName, Bundle preferences) {
        if (preferences == null) return;

        Map<String, Bundle> packagePrefs = mPreferences.computeIfAbsent(packageName, k -> new HashMap<>());

        for (String key : preferences.keySet()) {
            if (!"export_time".equals(key)) {
                Bundle value = preferences.getBundle(key);
                if (value != null) {
                    packagePrefs.put(key, value);
                }
            }
        }

        if (DEBUG) Slog.d(TAG, "Imported preferences for package: " + packageName);
    }

    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putInt("total_packages", mPreferences.size());

        int totalPreferences = 0;
        for (Map<String, Bundle> packagePrefs : mPreferences.values()) {
            totalPreferences += packagePrefs.size();
        }
        stats.putInt("total_preferences", totalPreferences);

        return stats;
    }
}
