<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2024 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/jarvis_nav_height"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    style="@style/JarvisNavigation"
    android:layout_margin="@dimen/jarvis_nav_margin">

    <!-- Home Navigation Item -->
    <LinearLayout
        android:id="@+id/nav_home"
        style="@style/JarvisNavigation.Item"
        android:layout_width="0dp"
        android:layout_height="@dimen/jarvis_nav_item_size"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <FrameLayout
            android:layout_width="@dimen/jarvis_nav_icon_size"
            android:layout_height="@dimen/jarvis_nav_icon_size">

            <ImageView
                android:id="@+id/nav_home_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_home"
                android:contentDescription="@string/nav_home" />

            <!-- AI Activity Indicator -->
            <View
                android:id="@+id/nav_home_indicator"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_gravity="top|end"
                android:background="@drawable/jarvis_activity_indicator"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/nav_home_label"
            style="@style/JarvisText.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nav_home"
            android:layout_marginTop="@dimen/jarvis_spacing_xs"
            android:textColor="@color/jarvis_nav_unselected" />

    </LinearLayout>

    <!-- AI Assistant Navigation Item -->
    <LinearLayout
        android:id="@+id/nav_assistant"
        style="@style/JarvisNavigation.Item"
        android:layout_width="0dp"
        android:layout_height="@dimen/jarvis_nav_item_size"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <FrameLayout
            android:layout_width="@dimen/jarvis_nav_icon_size"
            android:layout_height="@dimen/jarvis_nav_icon_size">

            <ImageView
                android:id="@+id/nav_assistant_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_jarvis_assistant"
                android:contentDescription="@string/nav_assistant" />

            <!-- Breathing Animation Container -->
            <View
                android:id="@+id/nav_assistant_breathing"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/jarvis_breathing_animation"
                android:visibility="gone" />

            <!-- Voice Activity Indicator -->
            <View
                android:id="@+id/nav_assistant_voice_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/jarvis_voice_indicator"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/nav_assistant_label"
            style="@style/JarvisText.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nav_assistant"
            android:layout_marginTop="@dimen/jarvis_spacing_xs"
            android:textColor="@color/jarvis_nav_unselected" />

    </LinearLayout>

    <!-- Menu Navigation Item -->
    <LinearLayout
        android:id="@+id/nav_menu"
        style="@style/JarvisNavigation.Item"
        android:layout_width="0dp"
        android:layout_height="@dimen/jarvis_nav_item_size"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <FrameLayout
            android:layout_width="@dimen/jarvis_nav_icon_size"
            android:layout_height="@dimen/jarvis_nav_icon_size">

            <ImageView
                android:id="@+id/nav_menu_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_menu"
                android:contentDescription="@string/nav_menu" />

            <!-- Notification Badge -->
            <TextView
                android:id="@+id/nav_menu_badge"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="top|end"
                android:background="@drawable/jarvis_notification_badge"
                android:text="3"
                android:textSize="10sp"
                android:textColor="@color/jarvis_text_inverse"
                android:gravity="center"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/nav_menu_label"
            style="@style/JarvisText.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nav_menu"
            android:layout_marginTop="@dimen/jarvis_spacing_xs"
            android:textColor="@color/jarvis_nav_unselected" />

    </LinearLayout>

    <!-- Notifications Navigation Item -->
    <LinearLayout
        android:id="@+id/nav_notifications"
        style="@style/JarvisNavigation.Item"
        android:layout_width="0dp"
        android:layout_height="@dimen/jarvis_nav_item_size"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <FrameLayout
            android:layout_width="@dimen/jarvis_nav_icon_size"
            android:layout_height="@dimen/jarvis_nav_icon_size">

            <ImageView
                android:id="@+id/nav_notifications_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_notifications"
                android:contentDescription="@string/nav_notifications" />

            <!-- Smart Priority Indicator -->
            <View
                android:id="@+id/nav_notifications_priority"
                android:layout_width="4dp"
                android:layout_height="4dp"
                android:layout_gravity="top|start"
                android:background="@drawable/jarvis_priority_indicator"
                android:visibility="gone" />

            <!-- Notification Count Badge -->
            <TextView
                android:id="@+id/nav_notifications_count"
                android:layout_width="18dp"
                android:layout_height="18dp"
                android:layout_gravity="top|end"
                android:background="@drawable/jarvis_notification_count_badge"
                android:text="5"
                android:textSize="10sp"
                android:textColor="@color/jarvis_text_inverse"
                android:gravity="center"
                android:visibility="gone" />

        </FrameLayout>

        <TextView
            android:id="@+id/nav_notifications_label"
            style="@style/JarvisText.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nav_notifications"
            android:layout_marginTop="@dimen/jarvis_spacing_xs"
            android:textColor="@color/jarvis_nav_unselected" />

    </LinearLayout>

    <!-- Profile Navigation Item -->
    <LinearLayout
        android:id="@+id/nav_profile"
        style="@style/JarvisNavigation.Item"
        android:layout_width="0dp"
        android:layout_height="@dimen/jarvis_nav_item_size"
        android:layout_weight="1"
        android:orientation="vertical"
        android:gravity="center">

        <FrameLayout
            android:layout_width="@dimen/jarvis_nav_icon_size"
            android:layout_height="@dimen/jarvis_nav_icon_size">

            <!-- Profile Avatar -->
            <ImageView
                android:id="@+id/nav_profile_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ic_profile"
                android:background="@drawable/jarvis_avatar_background"
                android:contentDescription="@string/nav_profile" />

            <!-- Online Status Indicator -->
            <View
                android:id="@+id/nav_profile_status"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_gravity="bottom|end"
                android:background="@drawable/jarvis_online_indicator"
                android:visibility="visible" />

        </FrameLayout>

        <TextView
            android:id="@+id/nav_profile_label"
            style="@style/JarvisText.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/nav_profile"
            android:layout_marginTop="@dimen/jarvis_spacing_xs"
            android:textColor="@color/jarvis_nav_unselected" />

    </LinearLayout>

    <!-- Navigation Indicator -->
    <View
        android:id="@+id/nav_indicator"
        android:layout_width="24dp"
        android:layout_height="3dp"
        android:background="@drawable/jarvis_nav_indicator"
        android:layout_gravity="bottom"
        android:layout_marginBottom="4dp"
        android:visibility="visible" />

</LinearLayout>
