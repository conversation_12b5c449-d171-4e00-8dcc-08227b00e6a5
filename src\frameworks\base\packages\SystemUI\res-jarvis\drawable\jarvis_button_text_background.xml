<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_interactive_pressed" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
    <!-- Focused State -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/jarvis_interactive_focused" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="@dimen/jarvis_corner_radius_md" />
        </shape>
    </item>
    
</selector>
