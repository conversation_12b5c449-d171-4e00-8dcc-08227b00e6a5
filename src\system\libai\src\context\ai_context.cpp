/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "ai_context.h"
#include <android/log.h>
#include <cutils/log.h>
#include <utils/Mutex.h>
#include <memory>
#include <vector>
#include <unordered_map>
#include <algorithm>
#include <cmath>
#include <chrono>
#include <string>
#include <json/json.h>

#define LOG_TAG "libai_context"
#define ALOGD_IF_DEBUG(cond, ...) if (cond) ALOGD(__VA_ARGS__)

namespace {

// Global state
static bool g_initialized = false;
static bool g_debug_logging = false;
static android::Mutex g_mutex;

// Context registries
static std::unordered_map<ai_context_processor_t*, std::unique_ptr<ai_context_processor_t>> g_processors;
static std::unordered_map<ai_context_fusion_t*, std::unique_ptr<ai_context_fusion_t>> g_fusion_engines;
static std::unordered_map<ai_context_data_t*, std::unique_ptr<ai_context_data_t>> g_context_data;

// Internal structures
struct ai_context_processor {
    std::vector<ai_context_data_t*> processed_contexts;
    uint64_t total_processed;
    uint64_t processing_time_ms;
    
    ai_context_processor() : total_processed(0), processing_time_ms(0) {}
};

struct ai_context_fusion {
    ai_context_fusion_config_t config;
    std::vector<ai_context_data_t*> context_sources;
    std::vector<ai_context_pattern_t> detected_patterns;
    ai_context_analysis_t last_analysis;
    uint64_t fusion_count;
    
    ai_context_fusion() : fusion_count(0) {
        memset(&last_analysis, 0, sizeof(last_analysis));
    }
    
    ~ai_context_fusion() {
        if (last_analysis.fused_data) {
            ai_context_data_destroy(last_analysis.fused_data);
        }
        if (last_analysis.source_weights) {
            delete[] last_analysis.source_weights;
        }
    }
};

struct ai_context_data {
    ai_context_type_t type;
    ai_context_format_t format;
    uint64_t timestamp;
    float confidence;
    size_t data_size;
    std::unique_ptr<uint8_t[]> data;
    std::string source;
    uint32_t privacy_level;
    
    ai_context_data() : type(AI_CONTEXT_TYPE_APP_STATE), format(AI_CONTEXT_FORMAT_JSON),
                       timestamp(0), confidence(0.0f), data_size(0), privacy_level(1) {}
};

// Helper functions
uint64_t get_current_timestamp() {
    return std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
}

float calculate_temporal_correlation(uint64_t timestamp1, uint64_t timestamp2, float temporal_weight) {
    uint64_t time_diff = std::abs(static_cast<int64_t>(timestamp1 - timestamp2));
    
    // Exponential decay based on time difference
    float correlation = std::exp(-static_cast<float>(time_diff) / (temporal_weight * 1000.0f));
    return std::max(0.0f, std::min(1.0f, correlation));
}

float calculate_type_similarity(ai_context_type_t type1, ai_context_type_t type2) {
    if (type1 == type2) {
        return 1.0f;
    }
    
    // Define type similarity matrix
    static const float similarity_matrix[8][8] = {
        // APP_STATE, NOTIFICATION, SENSOR, LOCATION, COMMUNICATION, CALENDAR, USER_INTERACTION, ENVIRONMENTAL
        {1.0f, 0.7f, 0.3f, 0.4f, 0.6f, 0.5f, 0.8f, 0.2f}, // APP_STATE
        {0.7f, 1.0f, 0.2f, 0.3f, 0.8f, 0.6f, 0.5f, 0.1f}, // NOTIFICATION
        {0.3f, 0.2f, 1.0f, 0.8f, 0.1f, 0.2f, 0.4f, 0.9f}, // SENSOR
        {0.4f, 0.3f, 0.8f, 1.0f, 0.2f, 0.7f, 0.3f, 0.6f}, // LOCATION
        {0.6f, 0.8f, 0.1f, 0.2f, 1.0f, 0.5f, 0.4f, 0.1f}, // COMMUNICATION
        {0.5f, 0.6f, 0.2f, 0.7f, 0.5f, 1.0f, 0.3f, 0.2f}, // CALENDAR
        {0.8f, 0.5f, 0.4f, 0.3f, 0.4f, 0.3f, 1.0f, 0.3f}, // USER_INTERACTION
        {0.2f, 0.1f, 0.9f, 0.6f, 0.1f, 0.2f, 0.3f, 1.0f}  // ENVIRONMENTAL
    };
    
    if (type1 < 8 && type2 < 8) {
        return similarity_matrix[type1][type2];
    }
    
    return 0.0f;
}

ai_context_result_t process_json_context(const void* raw_data, size_t data_size, 
                                        ai_context_type_t context_type, ai_context_data_t** processed_data) {
    if (!raw_data || data_size == 0 || !processed_data) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    // Parse JSON data
    Json::Value root;
    Json::Reader reader;
    std::string json_str(static_cast<const char*>(raw_data), data_size);
    
    if (!reader.parse(json_str, root)) {
        return AI_CONTEXT_ERROR_INVALID_FORMAT;
    }
    
    // Create processed context data
    auto new_context = std::make_unique<ai_context_data_t>();
    new_context->type = context_type;
    new_context->format = AI_CONTEXT_FORMAT_JSON;
    new_context->timestamp = get_current_timestamp();
    new_context->confidence = root.get("confidence", 0.8f).asFloat();
    new_context->source = root.get("source", "unknown").asString();
    new_context->privacy_level = root.get("privacy_level", 1).asUInt();
    
    // Enhanced JSON processing based on context type
    Json::Value enhanced_data;
    enhanced_data["original"] = root;
    enhanced_data["processed_timestamp"] = static_cast<uint64_t>(new_context->timestamp);
    enhanced_data["context_type"] = static_cast<int>(context_type);
    
    switch (context_type) {
        case AI_CONTEXT_TYPE_APP_STATE:
            enhanced_data["app_category"] = "productivity"; // Would be determined by ML
            enhanced_data["usage_pattern"] = "frequent";
            break;
        case AI_CONTEXT_TYPE_NOTIFICATION:
            enhanced_data["notification_priority"] = root.get("priority", "normal").asString();
            enhanced_data["content_category"] = "communication"; // Would be determined by NLP
            break;
        case AI_CONTEXT_TYPE_SENSOR:
            enhanced_data["sensor_fusion"] = true;
            enhanced_data["motion_state"] = "stationary"; // Would be determined by sensor fusion
            break;
        case AI_CONTEXT_TYPE_LOCATION:
            enhanced_data["location_type"] = "indoor"; // Would be determined by location analysis
            enhanced_data["visit_frequency"] = "regular";
            break;
        default:
            break;
    }
    
    // Serialize enhanced data
    Json::StreamWriterBuilder builder;
    std::string enhanced_json = Json::writeString(builder, enhanced_data);
    
    new_context->data_size = enhanced_json.size();
    new_context->data = std::make_unique<uint8_t[]>(new_context->data_size);
    memcpy(new_context->data.get(), enhanced_json.c_str(), new_context->data_size);
    
    ai_context_data_t* context_ptr = new_context.get();
    g_context_data[context_ptr] = std::move(new_context);
    *processed_data = context_ptr;
    
    return AI_CONTEXT_SUCCESS;
}

ai_context_result_t perform_context_fusion(ai_context_fusion_t* fusion, ai_context_analysis_t** analysis) {
    if (!fusion || !analysis || fusion->context_sources.empty()) {
        return AI_CONTEXT_ERROR_INSUFFICIENT_DATA;
    }
    
    // Clean up previous analysis
    if (fusion->last_analysis.fused_data) {
        ai_context_data_destroy(fusion->last_analysis.fused_data);
        fusion->last_analysis.fused_data = nullptr;
    }
    if (fusion->last_analysis.source_weights) {
        delete[] fusion->last_analysis.source_weights;
        fusion->last_analysis.source_weights = nullptr;
    }
    
    // Filter contexts by age and confidence
    std::vector<ai_context_data_t*> valid_contexts;
    uint64_t current_time = get_current_timestamp();
    
    for (auto* context : fusion->context_sources) {
        uint64_t age = current_time - context->timestamp;
        if (age <= fusion->config.max_context_age_ms && 
            context->confidence >= fusion->config.confidence_threshold) {
            valid_contexts.push_back(context);
        }
    }
    
    if (valid_contexts.empty()) {
        return AI_CONTEXT_ERROR_INSUFFICIENT_DATA;
    }
    
    // Calculate source weights
    size_t num_sources = valid_contexts.size();
    fusion->last_analysis.source_weights = new float[num_sources];
    float total_weight = 0.0f;
    
    for (size_t i = 0; i < num_sources; i++) {
        ai_context_data_t* context = valid_contexts[i];
        
        // Base weight from confidence
        float weight = context->confidence;
        
        // Temporal weight
        if (fusion->config.enable_temporal_fusion) {
            float temporal_factor = calculate_temporal_correlation(
                current_time, context->timestamp, fusion->config.temporal_weight);
            weight *= temporal_factor;
        }
        
        // Privacy level adjustment (higher privacy = lower weight in fusion)
        float privacy_factor = 1.0f / static_cast<float>(context->privacy_level);
        weight *= privacy_factor;
        
        fusion->last_analysis.source_weights[i] = weight;
        total_weight += weight;
    }
    
    // Normalize weights
    if (total_weight > 0.0f) {
        for (size_t i = 0; i < num_sources; i++) {
            fusion->last_analysis.source_weights[i] /= total_weight;
        }
    }
    
    // Create fused context data
    Json::Value fused_json;
    fused_json["fusion_timestamp"] = static_cast<uint64_t>(current_time);
    fused_json["num_sources"] = static_cast<int>(num_sources);
    fused_json["fusion_method"] = "weighted_average";
    
    // Fuse context data based on weights
    float overall_confidence = 0.0f;
    Json::Value sources_array(Json::arrayValue);
    
    for (size_t i = 0; i < num_sources; i++) {
        ai_context_data_t* context = valid_contexts[i];
        float weight = fusion->last_analysis.source_weights[i];
        
        overall_confidence += context->confidence * weight;
        
        // Parse source context data
        if (context->format == AI_CONTEXT_FORMAT_JSON && context->data) {
            Json::Value source_json;
            Json::Reader reader;
            std::string source_str(reinterpret_cast<const char*>(context->data.get()), context->data_size);
            
            if (reader.parse(source_str, source_json)) {
                Json::Value source_entry;
                source_entry["source"] = context->source;
                source_entry["type"] = static_cast<int>(context->type);
                source_entry["weight"] = weight;
                source_entry["confidence"] = context->confidence;
                source_entry["data"] = source_json;
                sources_array.append(source_entry);
            }
        }
    }
    
    fused_json["sources"] = sources_array;
    fused_json["overall_confidence"] = overall_confidence;
    
    // Create fused context data object
    ai_context_data_t* fused_data = nullptr;
    Json::StreamWriterBuilder builder;
    std::string fused_str = Json::writeString(builder, fused_json);
    
    ai_context_result_t result = ai_context_data_create(
        AI_CONTEXT_TYPE_APP_STATE, // Fused data is generic
        AI_CONTEXT_FORMAT_JSON,
        fused_str.c_str(),
        fused_str.size(),
        "context_fusion",
        &fused_data
    );
    
    if (result != AI_CONTEXT_SUCCESS) {
        return result;
    }
    
    // Fill analysis results
    fusion->last_analysis.overall_confidence = overall_confidence;
    fusion->last_analysis.num_sources = num_sources;
    fusion->last_analysis.fusion_timestamp = current_time;
    fusion->last_analysis.fused_data = fused_data;
    
    snprintf(fusion->last_analysis.analysis_summary, sizeof(fusion->last_analysis.analysis_summary),
        "Fused %zu contexts with %.2f confidence", num_sources, overall_confidence);
    
    *analysis = &fusion->last_analysis;
    fusion->fusion_count++;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Context fusion completed: %zu sources, confidence %.2f", 
                   num_sources, overall_confidence);
    
    return AI_CONTEXT_SUCCESS;
}

ai_context_result_t detect_patterns_impl(ai_context_fusion_t* fusion, ai_context_pattern_t* patterns,
                                        size_t max_patterns, size_t* num_patterns) {
    if (!fusion || !patterns || !num_patterns) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    *num_patterns = 0;
    
    if (fusion->context_sources.size() < 3) {
        return AI_CONTEXT_SUCCESS; // Not enough data for pattern detection
    }
    
    // Simple pattern detection based on context types and timing
    std::unordered_map<ai_context_type_t, std::vector<uint64_t>> type_timestamps;
    
    for (auto* context : fusion->context_sources) {
        type_timestamps[context->type].push_back(context->timestamp);
    }
    
    size_t pattern_count = 0;
    
    // Detect frequent context types
    for (auto& [type, timestamps] : type_timestamps) {
        if (timestamps.size() >= 3 && pattern_count < max_patterns) {
            std::sort(timestamps.begin(), timestamps.end());
            
            // Calculate average interval
            uint64_t total_interval = 0;
            for (size_t i = 1; i < timestamps.size(); i++) {
                total_interval += timestamps[i] - timestamps[i-1];
            }
            uint64_t avg_interval = total_interval / (timestamps.size() - 1);
            
            // Create pattern
            ai_context_pattern_t& pattern = patterns[pattern_count];
            snprintf(pattern.pattern_name, sizeof(pattern.pattern_name), 
                "frequent_context_type_%d", static_cast<int>(type));
            pattern.confidence = std::min(1.0f, static_cast<float>(timestamps.size()) / 10.0f);
            pattern.first_occurrence = timestamps.front();
            pattern.last_occurrence = timestamps.back();
            pattern.frequency = static_cast<uint32_t>(timestamps.size());
            pattern.primary_type = type;
            
            pattern_count++;
        }
    }
    
    *num_patterns = pattern_count;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Pattern detection completed: %zu patterns found", pattern_count);
    
    return AI_CONTEXT_SUCCESS;
}

} // anonymous namespace

// Public API implementation
extern "C" {

ai_context_result_t ai_context_init(void) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (g_initialized) {
        return AI_CONTEXT_SUCCESS;
    }
    
    g_initialized = true;
    ALOGD("AI Context library initialized");
    return AI_CONTEXT_SUCCESS;
}

void ai_context_cleanup(void) {
    android::Mutex::Autolock lock(g_mutex);
    
    if (!g_initialized) {
        return;
    }
    
    // Clean up all context data
    g_context_data.clear();
    
    // Clean up all fusion engines
    g_fusion_engines.clear();
    
    // Clean up all processors
    g_processors.clear();
    
    g_initialized = false;
    ALOGD("AI Context library cleaned up");
}

void ai_context_get_version(int* major, int* minor, int* patch) {
    if (major) *major = AI_CONTEXT_VERSION_MAJOR;
    if (minor) *minor = AI_CONTEXT_VERSION_MINOR;
    if (patch) *patch = AI_CONTEXT_VERSION_PATCH;
}

ai_context_result_t ai_context_processor_create(ai_context_processor_t** processor) {
    if (!processor || !g_initialized) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    auto new_processor = std::make_unique<ai_context_processor_t>();
    
    ai_context_processor_t* processor_ptr = new_processor.get();
    g_processors[processor_ptr] = std::move(new_processor);
    *processor = processor_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Context processor created");
    return AI_CONTEXT_SUCCESS;
}

void ai_context_processor_destroy(ai_context_processor_t* processor) {
    if (!processor) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_processors.find(processor);
    if (it != g_processors.end()) {
        g_processors.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Context processor destroyed");
    }
}

ai_context_result_t ai_context_process(ai_context_processor_t* processor, const void* raw_data,
                                      size_t data_size, ai_context_type_t context_type,
                                      ai_context_data_t** processed_data) {
    if (!processor || !raw_data || !processed_data || !g_initialized) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Process based on assumed JSON format
    ai_context_result_t result = process_json_context(raw_data, data_size, context_type, processed_data);
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Update processor statistics
    processor->total_processed++;
    processor->processing_time_ms += duration.count();
    
    if (result == AI_CONTEXT_SUCCESS) {
        processor->processed_contexts.push_back(*processed_data);
    }
    
    ALOGD_IF_DEBUG(g_debug_logging, "Context processed in %lld ms", duration.count());
    
    return result;
}

ai_context_result_t ai_context_fusion_create(const ai_context_fusion_config_t* config,
                                            ai_context_fusion_t** fusion) {
    if (!config || !fusion || !g_initialized) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    auto new_fusion = std::make_unique<ai_context_fusion_t>();
    new_fusion->config = *config;
    
    ai_context_fusion_t* fusion_ptr = new_fusion.get();
    g_fusion_engines[fusion_ptr] = std::move(new_fusion);
    *fusion = fusion_ptr;
    
    ALOGD_IF_DEBUG(g_debug_logging, "Context fusion engine created");
    return AI_CONTEXT_SUCCESS;
}

void ai_context_fusion_destroy(ai_context_fusion_t* fusion) {
    if (!fusion) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_fusion_engines.find(fusion);
    if (it != g_fusion_engines.end()) {
        g_fusion_engines.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Context fusion engine destroyed");
    }
}

ai_context_result_t ai_context_fusion_add_data(ai_context_fusion_t* fusion,
                                              const ai_context_data_t* context_data) {
    if (!fusion || !context_data) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    fusion->context_sources.push_back(const_cast<ai_context_data_t*>(context_data));
    
    ALOGD_IF_DEBUG(g_debug_logging, "Context data added to fusion engine");
    return AI_CONTEXT_SUCCESS;
}

ai_context_result_t ai_context_fusion_analyze(ai_context_fusion_t* fusion,
                                             ai_context_analysis_t** analysis) {
    if (!fusion || !analysis) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    return perform_context_fusion(fusion, analysis);
}

ai_context_result_t ai_context_detect_patterns(ai_context_fusion_t* fusion,
                                              ai_context_pattern_t* patterns,
                                              size_t max_patterns, size_t* num_patterns) {
    if (!fusion || !patterns || !num_patterns) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    return detect_patterns_impl(fusion, patterns, max_patterns, num_patterns);
}

ai_context_result_t ai_context_data_create(ai_context_type_t type, ai_context_format_t format,
                                          const void* data, size_t data_size, const char* source,
                                          ai_context_data_t** context_data) {
    if (!data || !context_data || !g_initialized) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    auto new_context = std::make_unique<ai_context_data_t>();
    new_context->type = type;
    new_context->format = format;
    new_context->timestamp = get_current_timestamp();
    new_context->confidence = 1.0f; // Default confidence
    new_context->data_size = data_size;
    new_context->source = source ? source : "unknown";
    new_context->privacy_level = 1; // Default privacy level
    
    new_context->data = std::make_unique<uint8_t[]>(data_size);
    memcpy(new_context->data.get(), data, data_size);
    
    ai_context_data_t* context_ptr = new_context.get();
    g_context_data[context_ptr] = std::move(new_context);
    *context_data = context_ptr;
    
    return AI_CONTEXT_SUCCESS;
}

void ai_context_data_destroy(ai_context_data_t* context_data) {
    if (!context_data) {
        return;
    }
    
    android::Mutex::Autolock lock(g_mutex);
    
    auto it = g_context_data.find(context_data);
    if (it != g_context_data.end()) {
        g_context_data.erase(it);
        ALOGD_IF_DEBUG(g_debug_logging, "Context data destroyed");
    }
}

ai_context_result_t ai_context_calculate_similarity(const ai_context_data_t* data1,
                                                   const ai_context_data_t* data2, float* similarity) {
    if (!data1 || !data2 || !similarity) {
        return AI_CONTEXT_ERROR_INVALID_PARAM;
    }
    
    // Calculate similarity based on type, temporal proximity, and content
    float type_sim = calculate_type_similarity(data1->type, data2->type);
    float temporal_sim = calculate_temporal_correlation(data1->timestamp, data2->timestamp, 1.0f);
    float confidence_sim = 1.0f - std::abs(data1->confidence - data2->confidence);
    
    // Weighted average
    *similarity = (type_sim * 0.4f + temporal_sim * 0.3f + confidence_sim * 0.3f);
    
    return AI_CONTEXT_SUCCESS;
}

const char* ai_context_get_error_message(ai_context_result_t result) {
    switch (result) {
        case AI_CONTEXT_SUCCESS: return "Success";
        case AI_CONTEXT_ERROR_INVALID_PARAM: return "Invalid parameter";
        case AI_CONTEXT_ERROR_OUT_OF_MEMORY: return "Out of memory";
        case AI_CONTEXT_ERROR_PROCESSING_FAILED: return "Processing failed";
        case AI_CONTEXT_ERROR_INVALID_FORMAT: return "Invalid format";
        case AI_CONTEXT_ERROR_INSUFFICIENT_DATA: return "Insufficient data";
        default: return "Unknown error";
    }
}

void ai_context_set_debug_logging(bool enable) {
    g_debug_logging = enable;
    ALOGD("Context debug logging %s", enable ? "enabled" : "disabled");
}

} // extern "C"
