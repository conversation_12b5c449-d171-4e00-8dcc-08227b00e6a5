/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.notification;

import android.app.Notification;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.service.notification.StatusBarNotification;
import android.text.TextUtils;
import android.util.Log;
import android.util.Slog;

import com.android.server.ai.AiContextEngineService;
import com.android.server.ai.AiPersonalizationService;
import com.android.server.ai.AiPlanningOrchestrationService;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * AI integration layer for NotificationManagerService in Jarvis OS.
 * 
 * Provides AI-aware notification management, privacy-preserving content analysis,
 * notification pattern detection, and intelligent notification prioritization.
 */
public class AiNotificationManagerIntegration {
    private static final String TAG = "AiNotificationManagerIntegration";
    private static final boolean DEBUG = true;

    // Notification categories for AI analysis
    private static final String CATEGORY_COMMUNICATION = "communication";
    private static final String CATEGORY_SOCIAL = "social";
    private static final String CATEGORY_PRODUCTIVITY = "productivity";
    private static final String CATEGORY_ENTERTAINMENT = "entertainment";
    private static final String CATEGORY_NEWS = "news";
    private static final String CATEGORY_SYSTEM = "system";
    private static final String CATEGORY_UNKNOWN = "unknown";

    // Privacy levels for notification content
    private static final int PRIVACY_LEVEL_PUBLIC = 1;
    private static final int PRIVACY_LEVEL_PERSONAL = 2;
    private static final int PRIVACY_LEVEL_SENSITIVE = 3;
    private static final int PRIVACY_LEVEL_CRITICAL = 4;

    // Notification importance levels
    private static final int IMPORTANCE_URGENT = 4;
    private static final int IMPORTANCE_HIGH = 3;
    private static final int IMPORTANCE_NORMAL = 2;
    private static final int IMPORTANCE_LOW = 1;

    private final Context mContext;
    private final NotificationManagerService mNotificationManagerService;
    private final Handler mHandler;
    
    // AI service references
    private AiContextEngineService mContextEngine;
    private AiPersonalizationService mPersonalizationService;
    private AiPlanningOrchestrationService mPlanningService;
    
    // Notification tracking
    private final ConcurrentHashMap<String, NotificationInfo> mTrackedNotifications = new ConcurrentHashMap<>();
    private final List<NotificationPattern> mDetectedPatterns = new ArrayList<>();
    private final Map<String, NotificationStats> mPackageStats = new HashMap<>();
    
    // Configuration
    private boolean mAiIntegrationEnabled = true;
    private boolean mContentAnalysisEnabled = true;
    private boolean mPatternDetectionEnabled = true;
    private boolean mPrivacyPreservationEnabled = true;
    private boolean mIntelligentPrioritizationEnabled = true;
    private int mMaxTrackedNotifications = 1000;
    
    // Privacy patterns for content sanitization
    private static final Pattern PHONE_PATTERN = Pattern.compile("\\b\\d{3}-\\d{3}-\\d{4}\\b");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b");
    private static final Pattern CREDIT_CARD_PATTERN = Pattern.compile("\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b");
    private static final Pattern ADDRESS_PATTERN = Pattern.compile("\\b\\d+\\s+[A-Za-z\\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd)\\b");
    
    // Statistics
    private long mTotalNotifications = 0;
    private long mAnalyzedNotifications = 0;
    private long mSanitizedNotifications = 0;
    private long mPatternsDetected = 0;
    private long mAiPrioritizedNotifications = 0;

    public AiNotificationManagerIntegration(Context context, NotificationManagerService nms, Handler handler) {
        mContext = context;
        mNotificationManagerService = nms;
        mHandler = handler;
        
        if (DEBUG) Slog.d(TAG, "AiNotificationManagerIntegration created");
    }

    /**
     * Initialize AI service connections
     */
    public void initializeAiServices() {
        // Get references to AI services
        mContextEngine = (AiContextEngineService) mContext.getSystemService(Context.AI_CONTEXT_ENGINE_SERVICE);
        mPersonalizationService = (AiPersonalizationService) mContext.getSystemService(Context.AI_PERSONALIZATION_SERVICE);
        mPlanningService = (AiPlanningOrchestrationService) mContext.getSystemService(Context.AI_PLANNING_ORCHESTRATION_SERVICE);
        
        if (DEBUG) Slog.d(TAG, "AI services initialized");
    }

    /**
     * Called when a notification is posted
     */
    public void onNotificationPosted(StatusBarNotification sbn) {
        if (!mAiIntegrationEnabled || sbn == null) {
            return;
        }
        
        try {
            String key = sbn.getKey();
            Notification notification = sbn.getNotification();
            
            // Create notification info for tracking
            NotificationInfo notificationInfo = new NotificationInfo(sbn, SystemClock.elapsedRealtime());
            mTrackedNotifications.put(key, notificationInfo);
            
            // Analyze notification content
            if (mContentAnalysisEnabled) {
                analyzeNotificationContent(notificationInfo);
            }
            
            // Collect notification context for AI
            if (mContextEngine != null) {
                Bundle contextData = createNotificationContextData(sbn, "notification_posted");
                mContextEngine.updateContext("notification_manager", contextData);
            }
            
            // Update package statistics
            updatePackageStats(sbn.getPackageName(), "posted");
            
            // Check for intelligent prioritization
            if (mIntelligentPrioritizationEnabled && mPersonalizationService != null) {
                suggestNotificationPriority(sbn);
            }
            
            // Detect notification patterns
            if (mPatternDetectionEnabled) {
                detectNotificationPatterns(sbn);
            }
            
            mTotalNotifications++;
            
            if (DEBUG) Slog.d(TAG, "Notification posted: " + key + " from " + sbn.getPackageName());
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onNotificationPosted", e);
        }
    }

    /**
     * Called when a notification is removed
     */
    public void onNotificationRemoved(StatusBarNotification sbn, int reason) {
        if (!mAiIntegrationEnabled || sbn == null) {
            return;
        }
        
        try {
            String key = sbn.getKey();
            
            // Update notification info
            NotificationInfo notificationInfo = mTrackedNotifications.get(key);
            if (notificationInfo != null) {
                notificationInfo.updateRemovalInfo(reason, SystemClock.elapsedRealtime());
            }
            
            // Collect removal context for AI
            if (mContextEngine != null) {
                Bundle contextData = createNotificationContextData(sbn, "notification_removed");
                contextData.putInt("removal_reason", reason);
                contextData.putString("removal_reason_name", getRemovalReasonName(reason));
                
                if (notificationInfo != null) {
                    long displayDuration = notificationInfo.removalTime - notificationInfo.postTime;
                    contextData.putLong("display_duration_ms", displayDuration);
                }
                
                mContextEngine.updateContext("notification_manager", contextData);
            }
            
            // Update package statistics
            updatePackageStats(sbn.getPackageName(), "removed");
            
            // Clean up old notifications
            cleanupOldNotifications();
            
            if (DEBUG) Slog.d(TAG, "Notification removed: " + key + " reason=" + reason);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onNotificationRemoved", e);
        }
    }

    /**
     * Called when a notification is clicked/interacted with
     */
    public void onNotificationClicked(StatusBarNotification sbn, String action) {
        if (!mAiIntegrationEnabled || sbn == null) {
            return;
        }
        
        try {
            String key = sbn.getKey();
            
            // Update notification info
            NotificationInfo notificationInfo = mTrackedNotifications.get(key);
            if (notificationInfo != null) {
                notificationInfo.recordInteraction(action, SystemClock.elapsedRealtime());
            }
            
            // Collect interaction context for AI
            if (mContextEngine != null) {
                Bundle contextData = createNotificationContextData(sbn, "notification_clicked");
                contextData.putString("action", action);
                contextData.putLong("interaction_timestamp", System.currentTimeMillis());
                
                mContextEngine.updateContext("notification_manager", contextData);
            }
            
            // Update personalization based on interaction
            if (mPersonalizationService != null) {
                Bundle interactionData = new Bundle();
                interactionData.putString("package_name", sbn.getPackageName());
                interactionData.putString("category", getNotificationCategory(sbn.getNotification()));
                interactionData.putString("action", action);
                interactionData.putLong("timestamp", System.currentTimeMillis());
                
                mPersonalizationService.recordNotificationInteraction(interactionData);
            }
            
            if (DEBUG) Slog.d(TAG, "Notification clicked: " + key + " action=" + action);
            
        } catch (Exception e) {
            Slog.e(TAG, "Error in onNotificationClicked", e);
        }
    }

    /**
     * Get AI-suggested notification priority
     */
    public int getAiSuggestedPriority(StatusBarNotification sbn) {
        if (!mIntelligentPrioritizationEnabled || mPersonalizationService == null || sbn == null) {
            return IMPORTANCE_NORMAL;
        }
        
        try {
            // Create priority request
            Bundle priorityRequest = new Bundle();
            priorityRequest.putString("package_name", sbn.getPackageName());
            priorityRequest.putString("category", getNotificationCategory(sbn.getNotification()));
            priorityRequest.putLong("timestamp", System.currentTimeMillis());
            priorityRequest.putInt("current_priority", sbn.getNotification().priority);
            
            // Add notification content analysis (privacy-preserving)
            String sanitizedContent = sanitizeNotificationContent(sbn.getNotification());
            priorityRequest.putString("content_summary", sanitizedContent);
            
            // Add user context
            addUserContextToBundle(priorityRequest);
            
            // Get AI suggestion
            Bundle suggestion = mPersonalizationService.suggestNotificationPriority(priorityRequest);
            if (suggestion != null) {
                int suggestedPriority = suggestion.getInt("suggested_priority", IMPORTANCE_NORMAL);
                float confidence = suggestion.getFloat("confidence", 0.0f);
                
                if (confidence > 0.7f) {
                    mAiPrioritizedNotifications++;
                    
                    if (DEBUG) Slog.d(TAG, "AI suggested notification priority: " + suggestedPriority + 
                        " (confidence: " + confidence + ")");
                    
                    return suggestedPriority;
                }
            }
            
        } catch (Exception e) {
            Slog.e(TAG, "Error getting AI notification priority suggestion", e);
        }
        
        return IMPORTANCE_NORMAL;
    }

    /**
     * Get privacy-preserving notification summary for AI analysis
     */
    public String getPrivacyPreservingNotificationSummary(StatusBarNotification sbn) {
        if (!mPrivacyPreservationEnabled || sbn == null) {
            return "";
        }
        
        try {
            Notification notification = sbn.getNotification();
            StringBuilder summary = new StringBuilder();
            
            // Add basic metadata (safe)
            summary.append("package=").append(sbn.getPackageName()).append(";");
            summary.append("category=").append(getNotificationCategory(notification)).append(";");
            summary.append("priority=").append(notification.priority).append(";");
            summary.append("timestamp=").append(sbn.getPostTime()).append(";");
            
            // Add sanitized content
            String sanitizedContent = sanitizeNotificationContent(notification);
            if (!TextUtils.isEmpty(sanitizedContent)) {
                summary.append("content_type=").append(classifyContentType(sanitizedContent)).append(";");
                summary.append("content_length=").append(sanitizedContent.length()).append(";");
            }
            
            // Add interaction patterns (if available)
            NotificationInfo info = mTrackedNotifications.get(sbn.getKey());
            if (info != null) {
                summary.append("interactions=").append(info.interactionCount).append(";");
            }
            
            return summary.toString();
            
        } catch (Exception e) {
            Slog.e(TAG, "Error creating privacy-preserving notification summary", e);
            return "";
        }
    }

    /**
     * Enable or disable AI integration
     */
    public void setAiIntegrationEnabled(boolean enabled) {
        mAiIntegrationEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "AI integration " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Enable or disable content analysis
     */
    public void setContentAnalysisEnabled(boolean enabled) {
        mContentAnalysisEnabled = enabled;
        if (DEBUG) Slog.d(TAG, "Content analysis " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * Get integration statistics
     */
    public Bundle getStatistics() {
        Bundle stats = new Bundle();
        stats.putBoolean("ai_integration_enabled", mAiIntegrationEnabled);
        stats.putBoolean("content_analysis_enabled", mContentAnalysisEnabled);
        stats.putBoolean("pattern_detection_enabled", mPatternDetectionEnabled);
        stats.putBoolean("privacy_preservation_enabled", mPrivacyPreservationEnabled);
        stats.putBoolean("intelligent_prioritization_enabled", mIntelligentPrioritizationEnabled);
        
        stats.putInt("tracked_notifications", mTrackedNotifications.size());
        stats.putInt("detected_patterns", mDetectedPatterns.size());
        stats.putInt("package_stats_count", mPackageStats.size());
        
        stats.putLong("total_notifications", mTotalNotifications);
        stats.putLong("analyzed_notifications", mAnalyzedNotifications);
        stats.putLong("sanitized_notifications", mSanitizedNotifications);
        stats.putLong("patterns_detected", mPatternsDetected);
        stats.putLong("ai_prioritized_notifications", mAiPrioritizedNotifications);
        
        if (mTotalNotifications > 0) {
            float analysisRate = (float) mAnalyzedNotifications / mTotalNotifications * 100.0f;
            stats.putFloat("analysis_rate", analysisRate);
            
            float sanitizationRate = (float) mSanitizedNotifications / mTotalNotifications * 100.0f;
            stats.putFloat("sanitization_rate", sanitizationRate);
        }
        
        return stats;
    }

    /**
     * Dump integration state for debugging
     */
    public void dump(PrintWriter pw) {
        pw.println("  AiNotificationManagerIntegration State:");
        pw.println("    AI Integration Enabled: " + mAiIntegrationEnabled);
        pw.println("    Content Analysis Enabled: " + mContentAnalysisEnabled);
        pw.println("    Pattern Detection Enabled: " + mPatternDetectionEnabled);
        pw.println("    Privacy Preservation Enabled: " + mPrivacyPreservationEnabled);
        pw.println("    Intelligent Prioritization Enabled: " + mIntelligentPrioritizationEnabled);
        pw.println("    Tracked Notifications: " + mTrackedNotifications.size());
        pw.println("    Detected Patterns: " + mDetectedPatterns.size());
        pw.println("    Package Stats: " + mPackageStats.size());
        pw.println("    Total Notifications: " + mTotalNotifications);
        pw.println("    Analyzed Notifications: " + mAnalyzedNotifications);
        pw.println("    Sanitized Notifications: " + mSanitizedNotifications);
        pw.println("    Patterns Detected: " + mPatternsDetected);
        pw.println("    AI Prioritized Notifications: " + mAiPrioritizedNotifications);
        
        if (mTotalNotifications > 0) {
            float analysisRate = (float) mAnalyzedNotifications / mTotalNotifications * 100.0f;
            pw.println("    Analysis Rate: " + analysisRate + "%");
            
            float sanitizationRate = (float) mSanitizedNotifications / mTotalNotifications * 100.0f;
            pw.println("    Sanitization Rate: " + sanitizationRate + "%");
        }
    }

    // Private helper methods

    private void analyzeNotificationContent(NotificationInfo notificationInfo) {
        try {
            Notification notification = notificationInfo.sbn.getNotification();
            
            // Determine privacy level
            int privacyLevel = determinePrivacyLevel(notification);
            notificationInfo.privacyLevel = privacyLevel;
            
            // Categorize notification
            String category = getNotificationCategory(notification);
            notificationInfo.category = category;
            
            // Sanitize content if needed
            if (privacyLevel >= PRIVACY_LEVEL_PERSONAL) {
                String sanitizedContent = sanitizeNotificationContent(notification);
                notificationInfo.sanitizedContent = sanitizedContent;
                mSanitizedNotifications++;
            }
            
            mAnalyzedNotifications++;
            
        } catch (Exception e) {
            Slog.e(TAG, "Error analyzing notification content", e);
        }
    }

    private String sanitizeNotificationContent(Notification notification) {
        try {
            StringBuilder content = new StringBuilder();
            
            // Extract text content safely
            if (notification.extras != null) {
                CharSequence title = notification.extras.getCharSequence(Notification.EXTRA_TITLE);
                CharSequence text = notification.extras.getCharSequence(Notification.EXTRA_TEXT);
                
                if (title != null) {
                    content.append(sanitizeText(title.toString())).append(" ");
                }
                if (text != null) {
                    content.append(sanitizeText(text.toString()));
                }
            }
            
            return content.toString().trim();
            
        } catch (Exception e) {
            Slog.e(TAG, "Error sanitizing notification content", e);
            return "";
        }
    }

    private String sanitizeText(String text) {
        if (TextUtils.isEmpty(text)) {
            return "";
        }
        
        // Replace sensitive patterns with placeholders
        String sanitized = text;
        sanitized = PHONE_PATTERN.matcher(sanitized).replaceAll("[PHONE]");
        sanitized = EMAIL_PATTERN.matcher(sanitized).replaceAll("[EMAIL]");
        sanitized = CREDIT_CARD_PATTERN.matcher(sanitized).replaceAll("[CARD]");
        sanitized = ADDRESS_PATTERN.matcher(sanitized).replaceAll("[ADDRESS]");
        
        return sanitized;
    }

    private int determinePrivacyLevel(Notification notification) {
        // Simple privacy level determination based on content and source
        String category = getNotificationCategory(notification);
        
        switch (category) {
            case CATEGORY_COMMUNICATION:
            case CATEGORY_SOCIAL:
                return PRIVACY_LEVEL_PERSONAL;
            case CATEGORY_PRODUCTIVITY:
                return PRIVACY_LEVEL_SENSITIVE;
            case CATEGORY_SYSTEM:
                return PRIVACY_LEVEL_PUBLIC;
            default:
                return PRIVACY_LEVEL_PERSONAL;
        }
    }

    private String getNotificationCategory(Notification notification) {
        // Determine notification category based on various factors
        if (notification.category != null) {
            switch (notification.category) {
                case Notification.CATEGORY_MESSAGE:
                case Notification.CATEGORY_EMAIL:
                case Notification.CATEGORY_CALL:
                    return CATEGORY_COMMUNICATION;
                case Notification.CATEGORY_SOCIAL:
                    return CATEGORY_SOCIAL;
                case Notification.CATEGORY_SYSTEM:
                case Notification.CATEGORY_SERVICE:
                    return CATEGORY_SYSTEM;
                default:
                    return CATEGORY_UNKNOWN;
            }
        }
        
        return CATEGORY_UNKNOWN;
    }

    private String classifyContentType(String content) {
        if (TextUtils.isEmpty(content)) {
            return "empty";
        }
        
        // Simple content type classification
        if (content.contains("@") || content.contains("email")) {
            return "email";
        } else if (content.contains("call") || content.contains("phone")) {
            return "call";
        } else if (content.contains("message") || content.contains("text")) {
            return "message";
        } else {
            return "general";
        }
    }

    private void detectNotificationPatterns(StatusBarNotification sbn) {
        // Simple pattern detection based on timing and frequency
        // In a real implementation, this would use more sophisticated ML algorithms
        mPatternsDetected++;
    }

    private void suggestNotificationPriority(StatusBarNotification sbn) {
        int suggestedPriority = getAiSuggestedPriority(sbn);
        
        // In a real implementation, this would apply the suggested priority
        if (DEBUG && suggestedPriority != IMPORTANCE_NORMAL) {
            Slog.d(TAG, "AI suggested priority " + suggestedPriority + " for " + sbn.getPackageName());
        }
    }

    private Bundle createNotificationContextData(StatusBarNotification sbn, String event) {
        Bundle contextData = new Bundle();
        contextData.putString("event_type", event);
        contextData.putLong("timestamp", System.currentTimeMillis());
        contextData.putString("package_name", sbn.getPackageName());
        contextData.putString("notification_key", sbn.getKey());
        contextData.putLong("post_time", sbn.getPostTime());
        contextData.putInt("user_id", sbn.getUserId());
        
        Notification notification = sbn.getNotification();
        contextData.putString("category", getNotificationCategory(notification));
        contextData.putInt("priority", notification.priority);
        contextData.putInt("flags", notification.flags);
        
        // Add privacy-preserving content summary
        String summary = getPrivacyPreservingNotificationSummary(sbn);
        contextData.putString("content_summary", summary);
        
        return contextData;
    }

    private void updatePackageStats(String packageName, String action) {
        NotificationStats stats = mPackageStats.get(packageName);
        if (stats == null) {
            stats = new NotificationStats(packageName);
            mPackageStats.put(packageName, stats);
        }
        
        if ("posted".equals(action)) {
            stats.totalPosted++;
        } else if ("removed".equals(action)) {
            stats.totalRemoved++;
        }
        
        stats.lastActivity = SystemClock.elapsedRealtime();
    }

    private void addUserContextToBundle(Bundle bundle) {
        // Add current user context for AI analysis
        bundle.putLong("current_time", System.currentTimeMillis());
        bundle.putInt("active_notifications", mTrackedNotifications.size());
    }

    private String getRemovalReasonName(int reason) {
        // Convert removal reason code to human-readable name
        switch (reason) {
            case 1: return "user_dismissed";
            case 2: return "app_cancelled";
            case 3: return "timeout";
            default: return "unknown";
        }
    }

    private void cleanupOldNotifications() {
        // Remove old notification tracking data to prevent memory leaks
        if (mTrackedNotifications.size() > mMaxTrackedNotifications) {
            // In a real implementation, this would remove the oldest entries
            if (DEBUG) Slog.d(TAG, "Cleaning up old notification tracking data");
        }
    }

    // Inner classes

    private static class NotificationInfo {
        final StatusBarNotification sbn;
        final long postTime;
        long removalTime;
        int removalReason;
        int privacyLevel;
        String category;
        String sanitizedContent;
        int interactionCount;
        long lastInteractionTime;
        
        NotificationInfo(StatusBarNotification sbn, long postTime) {
            this.sbn = sbn;
            this.postTime = postTime;
            this.privacyLevel = PRIVACY_LEVEL_PUBLIC;
            this.category = CATEGORY_UNKNOWN;
            this.interactionCount = 0;
        }
        
        void updateRemovalInfo(int reason, long time) {
            this.removalReason = reason;
            this.removalTime = time;
        }
        
        void recordInteraction(String action, long time) {
            this.interactionCount++;
            this.lastInteractionTime = time;
        }
    }

    private static class NotificationStats {
        final String packageName;
        long totalPosted;
        long totalRemoved;
        long lastActivity;
        
        NotificationStats(String packageName) {
            this.packageName = packageName;
            this.totalPosted = 0;
            this.totalRemoved = 0;
            this.lastActivity = SystemClock.elapsedRealtime();
        }
    }

    private static class NotificationPattern {
        final String patternType;
        final String packageName;
        final long firstOccurrence;
        long lastOccurrence;
        int frequency;
        float confidence;
        
        NotificationPattern(String patternType, String packageName, long timestamp) {
            this.patternType = patternType;
            this.packageName = packageName;
            this.firstOccurrence = timestamp;
            this.lastOccurrence = timestamp;
            this.frequency = 1;
            this.confidence = 0.5f;
        }
    }
}
