/*
 * Copyright (C) 2024 Jarvis OS
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.systemui.jarvis;

import android.content.ComponentName;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Looper;
import android.service.quicksettings.Tile;
import android.service.quicksettings.TileService;
import android.util.Log;

import com.android.systemui.R;
import com.android.systemui.dagger.SysUISingleton;
import com.android.systemui.plugins.qs.QSTile;
import com.android.systemui.qs.QSHost;
import com.android.systemui.qs.tileimpl.QSTileImpl;

import javax.inject.Inject;

/**
 * Quick Settings tile for Jarvis AI controls
 * Provides quick access to AI features and settings
 */
@SysUISingleton
public class JarvisQuickSettingsTile extends QSTileImpl<QSTile.BooleanState> {
    private static final String TAG = "JarvisQuickSettingsTile";
    private static final boolean DEBUG = true;
    
    // Tile states
    private static final int STATE_DISABLED = 0;
    private static final int STATE_ENABLED = 1;
    private static final int STATE_PROCESSING = 2;
    private static final int STATE_LISTENING = 3;
    private static final int STATE_ERROR = 4;
    
    private final JarvisController mJarvisController;
    private final AiControlsPanel mControlsPanel;
    private final Handler mHandler;
    
    private int mCurrentState = STATE_DISABLED;
    private boolean mIsAiEnabled = false;
    private boolean mIsProcessing = false;
    private boolean mIsListening = false;
    private String mStatusText = "";
    
    @Inject
    public JarvisQuickSettingsTile(QSHost host, JarvisController jarvisController) {
        super(host);
        mJarvisController = jarvisController;
        mControlsPanel = new AiControlsPanel(mContext);
        mHandler = new Handler(Looper.getMainLooper());
        
        if (DEBUG) Log.d(TAG, "JarvisQuickSettingsTile created");
        
        // Register for AI service updates
        if (mJarvisController != null) {
            mJarvisController.addStateListener(mAiStateListener);
        }
    }
    
    @Override
    public BooleanState newTileState() {
        BooleanState state = new BooleanState();
        state.handlesLongClick = true;
        return state;
    }
    
    @Override
    protected void handleClick() {
        if (DEBUG) Log.d(TAG, "Tile clicked, current state: " + mCurrentState);
        
        if (mJarvisController == null) {
            if (DEBUG) Log.w(TAG, "JarvisController not available");
            return;
        }
        
        switch (mCurrentState) {
            case STATE_DISABLED:
                // Enable AI services
                mJarvisController.enableAiServices();
                updateState(STATE_PROCESSING);
                break;
                
            case STATE_ENABLED:
                // Show quick actions or disable
                showQuickActions();
                break;
                
            case STATE_PROCESSING:
                // Cancel current operation
                mJarvisController.cancelCurrentOperation();
                updateState(STATE_ENABLED);
                break;
                
            case STATE_LISTENING:
                // Stop listening
                mJarvisController.stopVoiceListening();
                updateState(STATE_ENABLED);
                break;
                
            case STATE_ERROR:
                // Retry AI services
                mJarvisController.retryAiServices();
                updateState(STATE_PROCESSING);
                break;
        }
        
        refreshState();
    }
    
    @Override
    protected void handleLongClick() {
        if (DEBUG) Log.d(TAG, "Tile long clicked");
        
        // Show AI controls panel
        showControlsPanel();
    }
    
    @Override
    protected void handleUpdateState(BooleanState state, Object arg) {
        if (DEBUG) Log.d(TAG, "Updating tile state: " + mCurrentState);
        
        // Update tile appearance based on current state
        switch (mCurrentState) {
            case STATE_DISABLED:
                state.value = false;
                state.state = Tile.STATE_INACTIVE;
                state.icon = getDisabledIcon();
                state.label = "Jarvis AI";
                state.secondaryLabel = "Tap to enable";
                state.contentDescription = "Jarvis AI disabled. Tap to enable.";
                break;
                
            case STATE_ENABLED:
                state.value = true;
                state.state = Tile.STATE_ACTIVE;
                state.icon = getEnabledIcon();
                state.label = "Jarvis AI";
                state.secondaryLabel = "Active";
                state.contentDescription = "Jarvis AI active. Tap for actions.";
                break;
                
            case STATE_PROCESSING:
                state.value = true;
                state.state = Tile.STATE_ACTIVE;
                state.icon = getProcessingIcon();
                state.label = "Jarvis AI";
                state.secondaryLabel = "Processing...";
                state.contentDescription = "Jarvis AI processing. Tap to cancel.";
                break;
                
            case STATE_LISTENING:
                state.value = true;
                state.state = Tile.STATE_ACTIVE;
                state.icon = getListeningIcon();
                state.label = "Jarvis AI";
                state.secondaryLabel = "Listening...";
                state.contentDescription = "Jarvis AI listening. Tap to stop.";
                break;
                
            case STATE_ERROR:
                state.value = false;
                state.state = Tile.STATE_UNAVAILABLE;
                state.icon = getErrorIcon();
                state.label = "Jarvis AI";
                state.secondaryLabel = "Error";
                state.contentDescription = "Jarvis AI error. Tap to retry.";
                break;
        }
        
        // Add status text if available
        if (!mStatusText.isEmpty()) {
            state.secondaryLabel = mStatusText;
        }
    }
    
    @Override
    public int getMetricsCategory() {
        return 0; // TODO: Add proper metrics category
    }
    
    @Override
    public Intent getLongClickIntent() {
        // Open AI settings
        Intent intent = new Intent();
        intent.setComponent(new ComponentName("com.android.settings", 
            "com.android.settings.jarvis.JarvisSettingsActivity"));
        return intent;
    }
    
    @Override
    public CharSequence getTileLabel() {
        return "Jarvis AI";
    }
    
    private void updateState(int newState) {
        if (mCurrentState != newState) {
            if (DEBUG) Log.d(TAG, "State changed: " + mCurrentState + " -> " + newState);
            mCurrentState = newState;
            
            // Schedule state refresh
            mHandler.post(() -> refreshState());
        }
    }
    
    private void showQuickActions() {
        if (DEBUG) Log.d(TAG, "Showing quick actions");
        
        // Show quick action panel
        if (mJarvisController != null) {
            mJarvisController.showQuickActions();
        }
    }
    
    private void showControlsPanel() {
        if (DEBUG) Log.d(TAG, "Showing controls panel");
        
        // Show AI controls panel
        if (mControlsPanel != null) {
            mControlsPanel.show();
        }
    }
    
    private QSTile.Icon getDisabledIcon() {
        return QSTileImpl.ResourceIcon.get(R.drawable.ic_jarvis_disabled);
    }
    
    private QSTile.Icon getEnabledIcon() {
        return QSTileImpl.ResourceIcon.get(R.drawable.ic_jarvis_enabled);
    }
    
    private QSTile.Icon getProcessingIcon() {
        return QSTileImpl.ResourceIcon.get(R.drawable.ic_jarvis_processing);
    }
    
    private QSTile.Icon getListeningIcon() {
        return QSTileImpl.ResourceIcon.get(R.drawable.ic_jarvis_listening);
    }
    
    private QSTile.Icon getErrorIcon() {
        return QSTileImpl.ResourceIcon.get(R.drawable.ic_jarvis_error);
    }
    
    private final JarvisController.StateListener mAiStateListener = new JarvisController.StateListener() {
        @Override
        public void onAiServiceStateChanged(boolean enabled, boolean processing, boolean listening) {
            mHandler.post(() -> {
                mIsAiEnabled = enabled;
                mIsProcessing = processing;
                mIsListening = listening;
                
                // Determine new state
                int newState;
                if (!enabled) {
                    newState = STATE_DISABLED;
                } else if (listening) {
                    newState = STATE_LISTENING;
                } else if (processing) {
                    newState = STATE_PROCESSING;
                } else {
                    newState = STATE_ENABLED;
                }
                
                updateState(newState);
            });
        }
        
        @Override
        public void onAiServiceError(String error) {
            mHandler.post(() -> {
                mStatusText = "Error: " + error;
                updateState(STATE_ERROR);
            });
        }
        
        @Override
        public void onAiStatusUpdate(String status) {
            mHandler.post(() -> {
                mStatusText = status;
                refreshState();
            });
        }
    };
    
    @Override
    public void destroy() {
        if (DEBUG) Log.d(TAG, "Destroying tile");
        
        // Unregister listeners
        if (mJarvisController != null) {
            mJarvisController.removeStateListener(mAiStateListener);
        }
        
        super.destroy();
    }
}
